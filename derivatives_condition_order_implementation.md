# Prompt AI: <PERSON><PERSON><PERSON> tính năng Hiển thị lệnh điều kiện trong ngày phái sinh

## Y<PERSON><PERSON> cầu chức năng:
T<PERSON><PERSON> tính năng hiển thị danh sách lệnh điều kiện trong ngày cho phái sinh với các chức năng:
1. <PERSON><PERSON><PERSON> thị danh sách lệnh điều kiện phái sinh
2. T<PERSON>h năng hủy lệnh đơn lẻ
3. Tính năng hủy nhiều lệnh (multi-select)
4. T<PERSON>h năng hủy tất cả lệnh
5. Pull-to-refresh và loading states

## Tham khảo implementation:
- **DerivativesOrderBookCubit**: Logic quản lý state và API calls
- **DerivativesOrderBookFilterCubit**: Logic filter
- **DeleteUpdateOrderCubit**: Logic hủy lệnh
- **API**: Sử dụng `getFuConditionOrderBook` từ `CommandHistoryRepository`

## <PERSON><PERSON><PERSON> trúc cần tạo:

### 1. Cubit State Management

**File: `features/vp_trading/lib/cubit/derivatives_condition_order/derivatives_condition_order_cubit.dart`**

```dart
import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';

part 'derivatives_condition_order_state.dart';

class DerivativesConditionOrderCubit extends Cubit<DerivativesConditionOrderState> {
  DerivativesConditionOrderCubit() : super(const DerivativesConditionOrderState());
  
  final CommandHistoryRepository _repository = GetIt.instance<CommandHistoryRepository>();
  
  Future<void> loadData() async {
    try {
      emit(state.copyWith(isLoading: true));
      
      final result = await _repository.getFuConditionOrderBook(
        OrderBookRequest(
          accountId: GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
          pageNo: 1,
          pageSize: 1000,
        ),
      );
      
      if (result.isSuccess) {
        final items = result.data?.content ?? [];
        final orderIds = items.map((e) => e.orderId).whereType<String>().toList();
        
        emit(state.copyWith(
          isLoading: false,
          listItems: items,
          listOrderIdCancel: orderIds,
        ));
      }
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      showError(e);
    }
  }
  
  void toggleSelectAll(bool isSelectAll) {
    emit(state.copyWith(
      isSelectAll: isSelectAll,
      selectedItems: isSelectAll ? Set.from(state.listItems) : {},
    ));
  }
  
  void toggleSelectItem(ConditionOrderBookModel item) {
    final selectedItems = Set<ConditionOrderBookModel>.from(state.selectedItems);
    
    if (selectedItems.contains(item)) {
      selectedItems.remove(item);
    } else {
      selectedItems.add(item);
    }
    
    emit(state.copyWith(
      selectedItems: selectedItems,
      isSelectAll: selectedItems.length == state.listItems.length,
    ));
  }
}
```

**File: `features/vp_trading/lib/cubit/derivatives_condition_order/derivatives_condition_order_state.dart`**

```dart
part of 'derivatives_condition_order_cubit.dart';

class DerivativesConditionOrderState extends Equatable {
  final bool isLoading;
  final List<ConditionOrderBookModel> listItems;
  final List<String> listOrderIdCancel;
  final Set<ConditionOrderBookModel> selectedItems;
  final bool isSelectAll;
  final bool isMultiSelectMode;
  
  const DerivativesConditionOrderState({
    this.isLoading = false,
    this.listItems = const [],
    this.listOrderIdCancel = const [],
    this.selectedItems = const {},
    this.isSelectAll = false,
    this.isMultiSelectMode = false,
  });
  
  DerivativesConditionOrderState copyWith({
    bool? isLoading,
    List<ConditionOrderBookModel>? listItems,
    List<String>? listOrderIdCancel,
    Set<ConditionOrderBookModel>? selectedItems,
    bool? isSelectAll,
    bool? isMultiSelectMode,
  }) {
    return DerivativesConditionOrderState(
      isLoading: isLoading ?? this.isLoading,
      listItems: listItems ?? this.listItems,
      listOrderIdCancel: listOrderIdCancel ?? this.listOrderIdCancel,
      selectedItems: selectedItems ?? this.selectedItems,
      isSelectAll: isSelectAll ?? this.isSelectAll,
      isMultiSelectMode: isMultiSelectMode ?? this.isMultiSelectMode,
    );
  }
  
  @override
  List<Object?> get props => [
    isLoading,
    listItems,
    listOrderIdCancel,
    selectedItems,
    isSelectAll,
    isMultiSelectMode,
  ];
}
```

### 2. UI Screen Implementation

**File: `features/vp_trading/lib/screen/derivatives_condition_order/derivatives_condition_order_screen.dart`**

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivatives_condition_order/derivatives_condition_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';

class DerivativesConditionOrderScreen extends StatefulWidget {
  @override
  State<DerivativesConditionOrderScreen> createState() => _DerivativesConditionOrderScreenState();
}

class _DerivativesConditionOrderScreenState extends State<DerivativesConditionOrderScreen> {
  late final DerivativesConditionOrderCubit _cubit;
  late final DeleteUpdateOrderCubit _deleteUpdateOrderCubit;
  
  @override
  void initState() {
    super.initState();
    _cubit = DerivativesConditionOrderCubit();
    _deleteUpdateOrderCubit = DeleteUpdateOrderCubit();
    _cubit.loadData();
  }
  
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _cubit),
        BlocProvider.value(value: _deleteUpdateOrderCubit),
      ],
      child: BlocListener<DeleteUpdateOrderCubit, DeleteUpdateOrderState>(
        listener: (context, deleteState) {
          if (deleteState.status == DeleteUpdateOrderStateEnum.isDeleteSuccess ||
              deleteState.status == DeleteUpdateOrderStateEnum.isDeleteAllSuccess) {
            context.showSuccess(
              content: VPTradingLocalize.current.trading_cancel_order_success,
            );
            _cubit.loadData();
          }
        },
        child: BlocBuilder<DerivativesConditionOrderCubit, DerivativesConditionOrderState>(
          builder: (context, state) {
            return VPScaffold(
              appBar: _buildAppBar(state),
              body: _buildBody(state),
            );
          },
        ),
      ),
    );
  }
  
  PreferredSizeWidget _buildAppBar(DerivativesConditionOrderState state) {
    return AppBar(
      title: Text('Lệnh điều kiện phái sinh'),
      actions: [
        if (state.listItems.isNotEmpty) _buildDeleteAllButton(),
      ],
    );
  }
  
  Widget _buildDeleteAllButton() {
    return GestureDetector(
      onTap: () {
        dialogConfirmDeleteAllOrder(context, () {
          _deleteUpdateOrderCubit.deleteAllOrder(
            DeleteOrderRequest(
              accountId: GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
              requestId: "app_${AppHelper().genXRequestID()}",
              via: "V",
              orderId: _cubit.state.listOrderIdCancel.join(','),
            ),
            isConditional: true,
          );
          Navigator.of(context).pop();
        });
      },
      child: Row(
        children: [
          VpTradingAssets.icons.icRemove2.svg(),
          const SizedBox(width: 8),
          Text(
            VPTradingLocalize.current.trading_cancel_all_order,
            style: context.textStyle.captionMedium?.copyWith(color: vpColor.textAccentRed),
          ),
        ],
      ),
    );
  }
  
  Widget _buildBody(DerivativesConditionOrderState state) {
    if (state.isLoading) {
      return const CommandHistoryLoadingWidget();
    }
    
    if (state.listItems.isEmpty) {
      return PullToRefreshView(
        onRefresh: () async => await _cubit.loadData(),
        child: NoDataView(
          content: VPTradingLocalize.current.trading_no_data_message,
        ),
      );
    }
    
    return PullToRefreshView(
      onRefresh: () async => await _cubit.loadData(),
      child: ListView.builder(
        itemCount: state.listItems.length,
        itemBuilder: (context, index) {
          final item = state.listItems[index];
          return _buildOrderItem(item, state);
        },
      ),
    );
  }
  
  Widget _buildOrderItem(ConditionOrderBookModel item, DerivativesConditionOrderState state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: vpColor.backgroundSecondary,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                item.symbol ?? '',
                style: context.textStyle.body16?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              GestureDetector(
                onTap: () => _showDeleteConfirmDialog(item),
                child: Icon(
                  Icons.delete_outline,
                  color: vpColor.textAccentRed,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('Giá: ${item.price}'),
          Text('Khối lượng: ${item.quantity}'),
          Text('Loại lệnh: ${item.orderType}'),
        ],
      ),
    );
  }
  
  void _showDeleteConfirmDialog(ConditionOrderBookModel item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Xác nhận hủy lệnh'),
        content: Text('Bạn có chắc chắn muốn hủy lệnh này?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteUpdateOrderCubit.deleteOrder(
                DeleteOrderRequest(
                  accountId: GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
                  requestId: "app_${AppHelper().genXRequestID()}",
                  via: "V",
                  orderId: item.orderId,
                ),
                isConditional: true,
              );
            },
            child: Text('Xác nhận'),
          ),
        ],
      ),
    );
  }
}
```

## Các điểm chính cần implement:

### 1. API Integration
- Sử dụng `getFuConditionOrderBook` từ `CommandHistoryRepository`
- Truyền đúng `accountId` cho tài khoản phái sinh
- Xử lý response và error handling

### 2. State Management
- Quản lý loading states
- Danh sách lệnh điều kiện
- Selection states cho multi-select
- Order IDs để hủy lệnh

### 3. Delete Operations
- Tích hợp với `DeleteUpdateOrderCubit`
- Sử dụng `isConditional: true` cho lệnh điều kiện
- Hỗ trợ hủy đơn lẻ, hủy nhiều, hủy tất cả

### 4. UI Components
- Sử dụng VP Design System components
- `VPScaffold`, `PullToRefreshView`, `NoDataView`
- Consistent styling với `vpColor` và `context.textStyle`

### 5. Error Handling
- Try-catch blocks cho API calls
- Hiển thị error messages
- Loading states management

### 6. User Experience
- Pull-to-refresh functionality
- Confirmation dialogs cho delete actions
- Success/error notifications
- Multi-select mode

## Lưu ý quan trọng:

1. **Conditional Orders**: Luôn sử dụng `isConditional: true` khi gọi delete APIs
2. **Account Management**: Sử dụng đúng derivative account ID
3. **UI Consistency**: Tham khảo UI/UX từ derivatives order book hiện tại
4. **Error Handling**: Implement robust error handling cho tất cả API calls
5. **Performance**: Optimize cho danh sách lớn với pagination nếu cần
6. **Localization**: Sử dụng `VPTradingLocalize` cho tất cả text strings

## Testing Checklist:

- [ ] Load danh sách lệnh điều kiện
- [ ] Hủy lệnh đơn lẻ
- [ ] Hủy nhiều lệnh (multi-select)
- [ ] Hủy tất cả lệnh
- [ ] Pull-to-refresh
- [ ] Error handling
- [ ] Loading states
- [ ] Empty state
- [ ] Confirmation dialogs
- [ ] Success/error notifications