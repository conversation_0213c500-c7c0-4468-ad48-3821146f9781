import 'package:vp_assets/core/service/asset_service.dart';
import 'package:vp_assets/model/asset_overview/asset_overview_model.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
import 'package:vp_assets/model/asset_report/investment_asset_model.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

abstract class AssetRepository {
  Future<BaseResponse<AssetOverviewModel>> getAssetOverview();
  Future<AsssetSummaryModel?> getSummaryAccounts(String? accountId);
  Future<List<InvestmentAssetModel>?> getInvestmentAssets({
    String? accountId,
    String? fromDate,
    String? toDate,
  });
}

class AssetRepositoryImpl extends AssetRepository {
  final AssetService assetService;

  AssetRepositoryImpl({required this.assetService});

  @override
  Future<BaseResponse<AssetOverviewModel>> getAssetOverview() async {
    try {
      final result = await assetService.getAssetOverview();
      return result;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<AsssetSummaryModel?> getSummaryAccounts(String? accountId) async {
    try {
      final result = await assetService.getSummaryAccounts(accountId);
      return result.data;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<InvestmentAssetModel>?> getInvestmentAssets({
    String? accountId,
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final result = await assetService.getInvestmentAssets(
        accountId,
        fromDate,
        toDate,
      );
      return result.data;
    } catch (err) {
      throw HandleError.from(err);
    }
  }
}
