import 'package:vp_assets/model/asset_overview/asset_overview_model.dart';
import 'package:vp_assets/model/asset_overview/assset_summary_model.dart';
import 'package:vp_assets/model/asset_report/investment_asset_model.dart';
import 'package:vp_core/vp_core.dart';

part 'asset_service.g.dart';

@RestApi()
abstract class AssetService {
  factory AssetService(Dio dio, {String baseUrl}) = _AssetService;

  @GET("/neo-inv-customer/public/v1/asset/assetInfo")
  Future<BaseResponse<AssetOverviewModel>> getAssetOverview();

  @GET("/neo-inv-customer/public/v1/asset/summaryAccounts")
  Future<BaseResponse<AsssetSummaryModel>> getSummaryAccounts(
    @Query('accountId') String? accountId,
  );
  
  @GET("https://external-uat-krx.vpbanks.com.vn/asset/measurement/investmentAsset")
  Future<BaseResponse<List<InvestmentAssetModel>>> getInvestmentAssets(
    @Query('account') String? accountId,
    @Query('fromDate') String? fromDate,
    @Query('toDate') String? toDate,
  );
  // @GET(AssetsPathApi.getInvestmentAssetsPath)
  // Future<BaseResponse<List<InvestmentAssetModel>>> getInvestmentAssets(
  //   @Queries() Map<String, dynamic> param,
  // );

  // @GET(AssetsPathApi.investmentAsset)
  // Future<BaseResponse<List<InvestmentAssetModel>>> getInvestmentAsset(
  //   @Queries() Map<String, dynamic> param,
  // );

  // @GET(AssetsPathApi.copierNavAmount)
  // Future<BaseResponse<CopyTradeAssetModel>> getCopierNavRealtime();

  // @GET(AssetsPathApi.investmentEfficiency)
  // Future<BaseResponse<List<InvestmentEfficiencyModel>>> getInvestmentEfficiency(
  //   @Queries() Map<String, dynamic> param,
  // );

  // @GET(AssetsPathApi.psNavAmount)
  // Future<BaseResponse<DerivativeSummaryAccountModel>> getPSNav(
  //   @Path() String accountId,
  // );
}
