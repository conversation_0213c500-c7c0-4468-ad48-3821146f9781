part of 'asset_summary_cubit.dart';

enum AssetSummaryStatus { initial, loading, success, failure }

extension AssetSummaryStatusX on AssetSummaryStatus {
  bool get isInitial => this == AssetSummaryStatus.initial;
  bool get isLoading => this == AssetSummaryStatus.loading;
  bool get isSuccess => this == AssetSummaryStatus.success;
  bool get isFailure => this == AssetSummaryStatus.failure;
}

final class AssetSummaryState extends Equatable {
  final bool isHidden;
  final AssetSummaryStatus status;
  final AsssetSummaryModel? asssetSummaryModel;
  final SubAccountModel subAccount;
  final List<ChartAssetReportModel>? investmentGrowth;
  const AssetSummaryState({
    this.isHidden = true,
    this.status = AssetSummaryStatus.initial,
    this.asssetSummaryModel,
    required this.subAccount,
    this.investmentGrowth,
  });

  @override
  List<Object?> get props => [
    isHidden,
    status,
    asssetSummaryModel,
    subAccount,
    investmentGrowth,
  ];

  AssetSummaryState copyWith({
    bool? isHidden,
    AssetSummaryStatus? status,
    AsssetSummaryModel? asssetSummaryModel,
    SubAccountModel? subAccount,
    List<ChartAssetReportModel>? investmentGrowth,
  }) {
    return AssetSummaryState(
      subAccount: subAccount ?? this.subAccount,
      isHidden: isHidden ?? this.isHidden,
      status: status ?? this.status,
      asssetSummaryModel: asssetSummaryModel ?? this.asssetSummaryModel,
      investmentGrowth: investmentGrowth ?? this.investmentGrowth,
    );
  }
}
