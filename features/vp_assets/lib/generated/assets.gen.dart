// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/ic_asset_overview_selected.svg
  SvgGenImage get icAssetOverviewSelected =>
      const SvgGenImage('assets/icons/ic_asset_overview_selected.svg');

  /// File path: assets/icons/ic_asset_overview_unselected.svg
  SvgGenImage get icAssetOverviewUnselected =>
      const SvgGenImage('assets/icons/ic_asset_overview_unselected.svg');

  /// File path: assets/icons/ic_assets_history.svg
  SvgGenImage get icAssetsHistory =>
      const SvgGenImage('assets/icons/ic_assets_history.svg');

  /// File path: assets/icons/ic_assets_info.svg
  SvgGenImage get icAssetsInfo =>
      const SvgGenImage('assets/icons/ic_assets_info.svg');

  /// File path: assets/icons/ic_calendar.svg
  SvgGenImage get icCalendar =>
      const SvgGenImage('assets/icons/ic_calendar.svg');

  /// File path: assets/icons/ic_derivative_statement.svg
  SvgGenImage get icDerivativeStatement =>
      const SvgGenImage('assets/icons/ic_derivative_statement.svg');

  /// File path: assets/icons/ic_info_circle.svg
  SvgGenImage get icInfoCircle =>
      const SvgGenImage('assets/icons/ic_info_circle.svg');

  /// File path: assets/icons/ic_money_history.svg
  SvgGenImage get icMoneyHistory =>
      const SvgGenImage('assets/icons/ic_money_history.svg');

  /// File path: assets/icons/ic_report_selected.svg
  SvgGenImage get icReportSelected =>
      const SvgGenImage('assets/icons/ic_report_selected.svg');

  /// File path: assets/icons/ic_report_unselected.svg
  SvgGenImage get icReportUnselected =>
      const SvgGenImage('assets/icons/ic_report_unselected.svg');

  /// File path: assets/icons/ic_securities_statement.svg
  SvgGenImage get icSecuritiesStatement =>
      const SvgGenImage('assets/icons/ic_securities_statement.svg');

  /// File path: assets/icons/ic_statement_money.svg
  SvgGenImage get icStatementMoney =>
      const SvgGenImage('assets/icons/ic_statement_money.svg');

  /// File path: assets/icons/ic_statement_selected.svg
  SvgGenImage get icStatementSelected =>
      const SvgGenImage('assets/icons/ic_statement_selected.svg');

  /// File path: assets/icons/ic_statement_unselected.svg
  SvgGenImage get icStatementUnselected =>
      const SvgGenImage('assets/icons/ic_statement_unselected.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    icAssetOverviewSelected,
    icAssetOverviewUnselected,
    icAssetsHistory,
    icAssetsInfo,
    icCalendar,
    icDerivativeStatement,
    icInfoCircle,
    icMoneyHistory,
    icReportSelected,
    icReportUnselected,
    icSecuritiesStatement,
    icStatementMoney,
    icStatementSelected,
    icStatementUnselected,
  ];
}

class Assets {
  const Assets._();

  static const String package = 'vp_assets';

  static const $AssetsIconsGen icons = $AssetsIconsGen();
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'vp_assets';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_assets/$_assetName';
}
