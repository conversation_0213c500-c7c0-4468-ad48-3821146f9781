import 'package:json_annotation/json_annotation.dart';

part 'investment_asset_model.g.dart';
@JsonSerializable()
class InvestmentAssetModel {
  InvestmentAssetModel({this.nav, this.date, this.totalAsset, this.debt});
  num? nav;
  String? date;
  num? totalAsset;
  num? debt;


  factory InvestmentAssetModel.fromJson(Map<String, dynamic> json) =>
      _$InvestmentAssetModelFromJson(json);

  // Map<String, dynamic> toJson() => _$InvestmentAssetModel(this);
}
