import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_assets/model/asset_report/chart_data_report_model.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';

import '../../asset_page/widgets/assets_statistics_chart/stacked_column_chart_custom/tooltip_behavior_widget.dart';

class AssetGrowthWidget extends StatefulWidget {
  const AssetGrowthWidget({super.key, this.lstData});

  final List<ChartAssetReportModel>? lstData;

  @override
  State<AssetGrowthWidget> createState() => _AssetGrowthWidgetState();
}

class _AssetGrowthWidgetState extends State<AssetGrowthWidget> {
  late TrackballBehavior trackballBehavior;

  @override
  void initState() {
    super.initState();

    trackballBehavior = TrackballBehavior(
      tooltipAlignment: ChartAlignment.near,
      tooltipSettings: InteractiveTooltip(
        enable: true,
        borderRadius: 4,
        borderColor: themeData.white,
        color: themeData.white,
        textStyle: vpTextStyle.captionRegular.copyColor(themeData.gray900),
      ),
      lineDashArray: const [5, 5],
      lineColor: themeData.gray500,
      enable: true,
      activationMode: ActivationMode.singleTap,
      tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
      shouldAlwaysShow: false,
      builder: (_, args) {
        final index = args.groupingModeInfo?.currentPointIndices.firstOrNull;

        final item = widget.lstData.getElementAt(index);

        if (item == null) return const SizedBox(height: 0);

        return TooltipBehaviorWidget(
          title: item.x.stringToDateDdMmYyyy(),
          subTitle: '• Tài sản ròng: ${item.y.toMoney()}',
          // subTitle: LocaleKeys.assetPage_report_asset_growth_nav_tooltip.tr(
          //   args: [item.y.toMoney()],
          // ),
          content: '• Nợ: ${item.y1.toMoney()}',
          // content: LocaleKeys.assetPage_report_asset_growth_debt_tooltip.tr(
          //   args: [item.y1.toMoney()],
          // ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height / 5;

    return SizedBox(
      width: width,
      height: height * 2,
      child: SfCartesianChart(
        trackballBehavior: trackballBehavior,
        primaryXAxis: const CategoryAxis(
          majorGridLines: MajorGridLines(width: 0),
          majorTickLines: MajorTickLines(
            width: 0, // Độ rộng của các đường gạch chia
            color: Colors.black, // Màu sắc của các đường gạch chia
          ),
        ),
        primaryYAxis: NumericAxis(
          majorGridLines: const MajorGridLines(width: 0.7),
          axisLabelFormatter: (value) {
            return ChartAxisLabel(value.value.toPriceChart(), null);
          },
          majorTickLines: const MajorTickLines(
            width: 0, // Độ rộng của các đường gạch chia
            color: Colors.black, // Màu sắc của các đường gạch chia
          ),
        ),
        legend: const Legend(
          isVisible: true,
          position: LegendPosition.bottom, // Vị trí của chú thích
          overflowMode:
              LegendItemOverflowMode.wrap, // Chế độ tràn khi quá nhiều mục
        ),
        series: <CartesianSeries>[
          StackedColumnSeries<ChartAssetReportModel, String>(
            dataSource: widget.lstData,
            xValueMapper:
                (ChartAssetReportModel data, _) => data.x.stringToDateDdMm(),
            yValueMapper: (ChartAssetReportModel data, _) => data.y,
            pointColorMapper: (ChartAssetReportModel data, _) => Colors.green,
            legendIconType: LegendIconType.circle,
            color: Colors.green,
            legendItemText: 'Tài sản ròng',
          ),
          StackedColumnSeries<ChartAssetReportModel, String>(
            dataSource: widget.lstData,
            xValueMapper:
                (ChartAssetReportModel data, _) => data.x.stringToDateDdMm(),
            yValueMapper: (ChartAssetReportModel data, _) => data.y1,
            pointColorMapper: (ChartAssetReportModel data, _) => Colors.red,
            legendIconType: LegendIconType.circle,
            color: Colors.red,
            legendItemText: 'Nợ',
          ),
        ],
      ),
    );
  }
}
