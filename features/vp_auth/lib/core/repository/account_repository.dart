import 'package:flutter/foundation.dart';
import 'package:vp_auth/core/service/account_service.dart';
import 'package:vp_auth/model/change_pass/change_pass_request_model.dart';
import 'package:vp_auth/model/change_pin/change_pin_request_model.dart';
import 'package:vp_auth/model/sign_in/sign_in_responses.dart';
import 'package:vp_auth/model/sign_in/signin_request_model.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/model/sign_in_model/account_list_model.dart';
import 'package:vp_core/vp_core.dart';

abstract class AccountRepository {
  Future<BaseResponse<SignInResponses>> login(SignInRequestModel request);

  Future<BaseResponse<UserInfoModel>> getUserInfo();

  Future<BaseResponse<VerificationInfoModel>> getVerificationInfo();

  Future<BaseResponse> changePass(ChangePassRequestModel request);

  Future<BaseResponse> changePinFirst(ChangePinRequestModel request);

  // api mới  accountList
  Future<AccountListModel?> getAccountList();

// iam
  Future<BaseResponse<CustomerInfoNewModel>> getCustomerInfo();

  Future<BaseResponse<List<SettingUserModel>>> getUserSetting(
      SettingUserParam param);

  Future<BaseResponse> confirmOCRUpdate();
}

class AccountRepositoryImpl extends AccountRepository {
  final AccountService accountService;

  AccountRepositoryImpl({required this.accountService});

  @override
  Future<BaseResponse<SignInResponses>> login(
      SignInRequestModel request) async {
    try {
      return accountService.login(request);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<UserInfoModel>> getUserInfo() async {
    try {
      return accountService.getUserInfo();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<VerificationInfoModel>> getVerificationInfo() async {
    try {
      return accountService.getVerificationInfo();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> changePass(ChangePassRequestModel request) {
    try {
      return accountService.changePass(request);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> changePinFirst(ChangePinRequestModel request) {
    try {
      return accountService.changePinFirst(request);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<AccountListModel?> getAccountList() async {
    try {
      final response = await accountService.getAccountList();

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<CustomerInfoNewModel>> getCustomerInfo() {
    try {
      return accountService.getCustomerInfo();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<List<SettingUserModel>>> getUserSetting(
      SettingUserParam param) {
    try {
      return accountService.getUserSetting(param);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> confirmOCRUpdate() {
    try {
      return accountService.confirmOCRUpdate(
        KeyAPI.appJson,
        SharedPref.getString(KeyShared.onboardingToken),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }
}
