import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_auth/model/change_pass/change_pass_request_model.dart';
import 'package:vp_auth/model/change_pin/change_pin_request_model.dart';
import 'package:vp_auth/model/forgot_password/forgot_pass_auth_model.dart';
import 'package:vp_auth/model/forgot_password/gen_otp_reset_password_request_model.dart';
import 'package:vp_auth/model/forgot_pin/forgot_pin_request_model.dart';
import 'package:vp_auth/model/sign_in/sign_in_responses.dart';
import 'package:vp_auth/model/sign_in/signin_request_model.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/model/sign_in_model/account_list_model.dart';
import 'package:vp_core/vp_core.dart';

part 'account_service.g.dart';

@RestApi()
abstract class AccountService {
  factory AccountService(Dio dio, {String baseUrl}) = _AccountService;

  @POST(AccountPathAPI.login)
  Future<BaseResponse<SignInResponses>> login(
      @Body() SignInRequestModel request);

  @GET(AccountPathAPI.userInfo)
  Future<BaseResponse<UserInfoModel>> getUserInfo();

  @GET(AccountPathAPI.verificationInfo)
  Future<BaseResponse<VerificationInfoModel>> getVerificationInfo();

  @POST(AccountPathAPI.changePassFirst)
  Future<BaseResponse> changePass(@Body() ChangePassRequestModel request);

  @POST(AccountPathAPI.genOTPForgotPin)
  Future<BaseResponse> genOTPForgotPin();

  @POST(AccountPathAPI.changePin)
  Future<BaseResponse> changePin(@Body() ForgotPinRequestModel request);

  @POST(AccountPathAPI.changePinFirst)
  Future<BaseResponse> changePinFirst(@Body() ChangePinRequestModel request);

  @POST(AccountPathAPI.genOTPResetPasswordV2)
  Future<BaseResponse> genOTPResetPasswordV2(
      @Body() GenOtpResetPasswordRequestModel request);

  @POST(AccountPathAPI.verifyOTPResetPassword)
  Future<BaseResponse> verifyOTPResetPassword(
      @Body() ForgotPassAuthRequestModel request);

  @POST(AccountPathAPI.passwordModification)
  Future<BaseResponse> modificationPassword(
      @Body() String newPass, @Header(KeyAPI.authorization) String objVerify);

  //todo: Sửa enpoint sau khi ghép xong api mới
  @GET(
      "https://neopro-uat.vpbanks.com.vn/neo-inv-customer/public/v1/accounts/accountList")
  Future<BaseResponse<AccountListModel>> getAccountList();

  @GET(AccountPathAPI.custinfo)
  Future<BaseResponse<CustomerInfoNewModel>> getCustomerInfo();

  @GET(AccountPathAPI.userSettings)
  Future<BaseResponse<List<SettingUserModel>>> getUserSetting(
      @Queries() SettingUserParam param);

  @PUT(AccountPathAPI.confirmOCRUpdate)
  Future<BaseResponse> confirmOCRUpdate(
      @Header(KeyAPI.contentType) String contentType,
      @Header(KeyAPI.authorization) String authorization);
}
