import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:vp_auth/core/repository/account_repository.dart';
import 'package:vp_auth/core/repository/onboarding_repository.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_ocr_data.dart';
import 'package:vp_auth/utils/widgets/base_dialog.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_core/utils/go_router_helper.dart';

part 'info_ocr_state.dart';

class InfoOcrCubit extends Cubit<InfoOcrState> {
  InfoOcrCubit() : super(const InfoOcrState());

  Future updateEkyc() async {
    try {
      showDialogLoading();
      final baseResponse =
          await GetIt.instance.get<AccountRepository>().confirmOCRUpdate();
      hideDialogLoading();
      if (baseResponse.code != 'IABNOT000') {
        getContext.popUntilRoute('/mainTabbar');
        GetIt.instance<AuthCubit>().verificationInfoModel?.needChangeIdType =
            false;
        ///TODO not implement for bond
        // Session().setShowND13ForBond(false);
        await Future.delayed(const Duration(milliseconds: 300));
        VPPopup.oneButton(
                title: 'Cập nhật thành công',
                content:
                    'VPBankS đã ghi nhận dữ liệu cập nhật. Chúng tôi sẽ tiến hành đối chiếu với VSD và thông báo kết quả sớm nhất tới quý khách hàng.')
            .copyWith(
              icon: SvgPicture.asset(
                Assets.icons.icSuccessNew.path,
                package: 'vp_common',
              ),
              button: VpsButton.secondarySmall(
                title: 'Đóng',
                onPressed: getContext.pop,
              ),
            )
            .showDialog(getContext);
      } else {
        showSnackBar(getContext, baseResponse.message.toString());
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      hideDialogLoading();
      showError(e);
    }
  }
}
