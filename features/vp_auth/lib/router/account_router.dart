enum AccountRouter {
  signIn('/signIn'),
  splashDefault('/noauth-splashDefault'),
  forgotPasswordVerifyPage('/noauth-forgotPasswordVerifyPage'),
  forgotPasswordAuthPage('/noauth-forgotPasswordAuthPage'),
  forgotPasswordChangePage('/noauth-forgotPasswordChangePage'),
  changePassForFirst('/noauth-changePassForFirst'),
  forgotPin('/noauth-forgotPin'),
  changePinForFirst('/noauth-changePinForFirst'),
  regsiter('/noauth-register'),
  overlayUserIamInfo('/overlayUserIamInfo'),
  scanNfcPage('/scanNfcPage'),
  infoOCRPage('/infoOCRPage'),
  uploadOcrPage('/uploadOcrPage'),
  uploadOcrForIdentificationPage('/UploadOcrForIdentificationPage');

  final String routeName;

  const AccountRouter(this.routeName);
}
