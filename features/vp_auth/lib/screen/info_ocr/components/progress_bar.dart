import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_common/widget/line_view.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class StepView extends StatefulWidget {
  const StepView({
    super.key,
    required this.step,
    this.total = 3,
    this.space = 3,
    this.height = 3,
    this.onEndAnimation,
    this.revertAnimation = false,
    this.enableColor,
    this.disableColor,
  })  : assert(step >= 1),
        assert(step <= total);

  final int total;

  final int step;

  final double space;

  final double height;

  final Color? enableColor;

  final Color? disableColor;

  final bool revertAnimation;

  final VoidCallback? onEndAnimation;

  @override
  State<StepView> createState() => _StepViewState();
}

class _StepViewState extends State<StepView> {
  double width = 0;

  double widthExact = 0;

  @override
  void initState() {
    super.initState();

    startAnimation();
  }

  void startAnimation() async {
    await Future.delayed(const Duration(milliseconds: 200));
    if (mounted) setState(() => width = widthExact);
  }

  void revertAnimation() {
    if (mounted && widget.revertAnimation && width == widthExact) {
      setState(() => width = 0);
    }
  }

  @override
  void didUpdateWidget(covariant StepView oldWidget) {
    if (widget.step != oldWidget.step) {
      width = 0;
      startAnimation();
    }

    if (widget.revertAnimation != oldWidget.revertAnimation) {
      revertAnimation();
    }

    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final widgets = <Widget>[];

        final maxWidth = constraints.maxWidth;

        widthExact = maxWidth - (widget.space * (widget.total - 1));

        widthExact /= widget.total;

        for (int i = 1; i < widget.total + 1; i++) {
          if (i < widget.step) {
            widgets.add(
              Expanded(
                child: LineView(
                  width: double.infinity,
                  height: widget.height,
                  color: widget.enableColor,
                ),
              ),
            );
          }

          if (i == widget.step) {
            widgets.add(
              Expanded(
                child: Stack(
                  children: [
                    LineView(
                      width: double.infinity,
                      height: widget.height,
                      color: widget.disableColor,
                    ),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: AnimatedContainer(
                        width: width,
                        height: widget.height,
                        onEnd: widget.onEndAnimation,
                        duration: const Duration(milliseconds: 300),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3),
                          color: themeData.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          if (i > widget.step) {
            widgets.add(
              Expanded(
                child: LineView(
                  width: double.infinity,
                  height: widget.height,
                  color: widget.disableColor,
                ),
              ),
            );
          }

          if (i < widget.total) {
            widgets.add(
              LineView(
                width: widget.space,
                height: widget.height,
                color: themeData.white,
              ),
            );
          }
        }

        return Row(children: widgets);
      },
    );
  }
}

class BackAndNextView extends StatelessWidget {
  const BackAndNextView({
    super.key,
    this.onBack,
    this.onNext,
    this.padding = const EdgeInsets.symmetric(horizontal: 10),
  });

  final VoidCallback? onBack;

  final VoidCallback? onNext;

  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Row(
        children: [
          /// build back button
          AccountButtonBackDefault(
            title: 'Quay lại',
            onPressed: () => onBack?.call(),
            enable: onBack != null,
          ),
          const Spacer(),

          /// build next button
          SignUpButtonNext(
            enable: onNext != null,
            onPress: () => onNext?.call(),
            title: 'Tiếp theo',
          ),
        ],
      ),
    );
  }
}

class StepBottomView extends StatefulWidget {
  const StepBottomView({
    super.key,
    required this.step,
    this.total = 3,
    this.space = 3,
    this.height = 3,
    this.enableColor,
    this.disableColor,
    this.executeCallbackWhenFinishRevert = false,
    this.onBack,
    this.onNext,
  });

  final int total;

  final int step;

  final double space;

  final double height;

  final Color? enableColor;

  final Color? disableColor;

  final VoidCallback? onBack;

  final VoidCallback? onNext;

  final bool executeCallbackWhenFinishRevert;

  @override
  State<StepBottomView> createState() => _StepBottomViewState();
}

class _StepBottomViewState extends State<StepBottomView> {
  bool revertAnimation = false;

  bool back = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StepView(
          total: widget.total,
          step: widget.step,
          space: widget.space,
          height: widget.height,
          revertAnimation: revertAnimation,
          enableColor: widget.enableColor ?? themeData.primary,
          disableColor: widget.disableColor ?? themeData.gray300,
          onEndAnimation: onEndAnimation,
        ),
        const SizedBox(height: 10),
        BackAndNextView(
          onBack:
              widget.executeCallbackWhenFinishRevert && widget.onBack != null
                  ? handleOnBack
                  : widget.onBack,
          onNext: widget.onNext,
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  /// execute onback when end animation
  void onEndAnimation() {
    if (back) {
      back = false;

      revertAnimation = false;

      widget.onBack?.call();
    }
  }

  void handleOnBack() {
    back = true;

    if (widget.executeCallbackWhenFinishRevert) {
      setState(() => revertAnimation = true);
    }
  }
}

class BottomView extends StatelessWidget {
  const BottomView({this.lineHeight = 1, this.onBack, this.onNext, super.key});

  final double lineHeight;

  final VoidCallback? onBack;

  final VoidCallback? onNext;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        LineView(
          width: double.infinity,
          height: lineHeight,
          color: themeData.disabledColor,
        ),
        const SizedBox(height: 10),
        BackAndNextView(onBack: onBack, onNext: onNext),
        const SizedBox(height: 10),
      ],
    );
  }
}

class AccountButtonBackDefault extends StatelessWidget {
  const AccountButtonBackDefault({
    super.key,
    required this.onPressed,
    required this.title,
    this.enable = true,
    this.colorBack,
  });

  final VoidCallback onPressed;

  final String title;

  final bool enable;
  final Color? colorBack;

  @override
  Widget build(BuildContext context) {
    return TextButton.icon(
      style: ButtonStyle(
        foregroundColor: WidgetStateProperty.all<Color>(
          enable ? vpColor.textDisabled : vpColor.textBrand,
        ),
      ),
      icon: SvgPicture.asset(
        Assets.icons.icArrowBack.path,
        package: 'vp_common',
        colorFilter: ColorFilter.mode(
          enable ? vpColor.textBrand : vpColor.textDisabled,
          BlendMode.srcIn,
        ),
      ),
      label: Text(
        title,
        style: vpTextStyle.subtitle14?.copyColor(
          enable ? vpColor.textBrand : vpColor.textDisabled,
        ),
      ),
      onPressed: enable ? () => onPressed() : null,
    );
  }
}

class SignUpButtonNext extends StatelessWidget {
  const SignUpButtonNext({
    super.key,
    required this.onPress,
    this.enable = false,
    required this.title,
  });

  final VoidCallback onPress;
  final bool enable;
  final String title;

  @override
  Widget build(BuildContext context) {
    final colorDisable = themeData.gray500;
    return SizedBox(
      height: 40,
      child: ElevatedButton(
        style: ButtonStyle(
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          elevation: WidgetStateProperty.all<double>(enable ? 1 : 0),
          backgroundColor: WidgetStateProperty.all<Color>(
            enable
                ? vpColor.backgroundBrandPressed
                : vpColor.backgroundElevationMinus1,
          ),
        ),
        onPressed: () => handleOnPress(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            children: [
              Text(
                title,
                style: vpTextStyle.subtitle14?.copyColor(
                  enable ? vpColor.textWhite : vpColor.textDisabled,
                ),
              ),
              const SizedBox(width: 8),
              Padding(
                padding: const EdgeInsets.only(top: 2.0),
                child: SvgPicture.asset(
                  Assets.icons.icArrowNext.path,
                  package: 'vp_common',
                  colorFilter: ColorFilter.mode(
                    enable ? vpColor.iconWhite : colorDisable,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void handleOnPress() {
    if (!enable) return;

    NoDuplicate(() => onPress(), inMilliseconds: 300);
  }
}

class NoDuplicate {
  static int previousTime = 0;

  NoDuplicate(Function function, {int inMilliseconds = 500}) {
    final now = DateTime.now().millisecondsSinceEpoch;
    if ((now - previousTime).abs() >= inMilliseconds) {
      function();
    }
    previousTime = now;
  }
}
