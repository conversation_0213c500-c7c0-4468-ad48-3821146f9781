import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class SignUpInfoContentView extends StatelessWidget {
  const SignUpInfoContentView({
    Key? key,
    required this.title,
    required this.content,
  }) : super(key: key);
  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    return getContentInfo(title, content);
  }

  Padding getContentInfo(String title, String content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
                child: Text(
              title,
              style: vpTextStyle.body14?.copyWith(color: themeData.gray700),
              textAlign: TextAlign.start,
            )),
            const SizedBox(width: 16),
            Expanded(
                child: Text(content,
                    style: vpTextStyle.body14?.copyWith(color: themeData.black),
                    textAlign: TextAlign.end)),
          ]),
    );
  }
}
