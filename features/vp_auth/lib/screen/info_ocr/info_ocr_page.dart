import 'package:flutter/material.dart';
import 'package:vp_auth/cubit/info_ocr/info_ocr_cubit.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/6.sign_up_info_ocr/components/sign_up_info_content_view.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

import 'components/progress_bar.dart';

class InfoOcrPage extends StatefulWidget {
  const InfoOcrPage({Key? key}) : super(key: key);

  // final int? approvald;

  @override
  State<InfoOcrPage> createState() => _BodySignUpInfoOcrState();
}

class _BodySignUpInfoOcrState extends State<InfoOcrPage> {
  final ScrollController scrollController = ScrollController();
  late InfoOcrCubit _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = InfoOcrCubit();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: PopScope(
        canPop: false,
        child: Scaffold(
          backgroundColor: themeData.bgMain,
          body: SafeArea(
            child: Column(
              children: [
                HeaderWidget(
                  title: S.current.account_confirm_info,
                  subTitle: S.current.account_update_cccd,
                  back: false,
                  icon: ((GetIt.instance<AuthCubit>()
                                  .verificationInfoModel
                                  ?.needChangeIdMode ??
                              0) ==
                          1)
                      ? SvgPicture.asset(
                          Assets.icons.icClose.path,
                          package: 'vp_common',
                        )
                      : null,
                  actionRight: ((GetIt.instance<AuthCubit>()
                                  .verificationInfoModel
                                  ?.needChangeIdMode ??
                              0) ==
                          1)
                      ? () {
                          context.pop();
                          context.pop();
                        }
                      : null,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                        top: 24, left: 16, right: 16, bottom: 16),
                    child: BlocBuilder<InfoOcrCubit, InfoOcrState>(
                      builder: (context, state) {
                        final obj = state.obj ?? SignUpData().ocrData;
                        return SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(S.current.account_info_check_guide,
                                  style: vpTextStyle.subtitle16
                                      ?.copyWith(color: themeData.black)),
                              SizedBox(
                                height: 16,
                              ),
                              SignUpInfoContentView(
                                  title: S.current.account_full_name,
                                  content: obj.fullname),
                              SignUpInfoContentView(
                                  title: S.current.account_birthday,
                                  content: obj.dateofbirth),
                              SignUpInfoContentView(
                                  title: S.current.account_gender,
                                  content:
                                      SignUpUtils.getGenderString(obj.sex)),
                              SignUpInfoContentView(
                                  title: S.current.account_identify_card,
                                  content: obj.idcode),
                              SignUpInfoContentView(
                                  title: S.current.account_issued_date_card,
                                  content: obj.idDate),
                              SignUpInfoContentView(
                                  title: S.current.account_place_of_issued_card,
                                  content: obj.idplace),
                              SignUpInfoContentView(
                                  title: S.current.account_permanent_address,
                                  content: obj.address),
                              SignUpInfoContentView(
                                  title: S.current.account_current_address,
                                  content: obj.address2),
                              const SizedBox(height: 8),
                              // _InfoOcrNoteView()
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
                StepBottomView(
                  step: 2,
                  total: 2,
                  onBack: () {
                    context.pop();
                  },
                  onNext: () {
                    _bloc.updateEkyc();
                  },
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

// /*--------  Build dialog thông báo chuẩn bị chỉnh sửa -------- */
// Future<void> _showMyDialog() async {
//   bloc.trackingEditInfo();
//   await Navigator.push(
//     context,
//     MaterialPageRoute(
//         builder: (context) => const AccountRegisterEditInfo(),
//         fullscreenDialog: true),
//   ).then((data) {
//     if (data is bool && data) {
//       setState(() {});
//       checkWhenResume();
//     }
//     if (data is AccountAction && data == AccountAction.cancel) {
//       Navigator.pop(context);
//     }
//   });
//   // }
// }
}

class _InfoOcrNoteView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
      decoration: BoxDecoration(
        color: themeData.yellow.withValues(alpha: isDark ? 0.32 : 0.1),
        border: Border.all(
            color: isDark ? ColorDefine.transparent : themeData.yellow),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: const EdgeInsets.all(2),
          child: SvgPicture.asset(Assets.icons.icWarning2.path),
        ),
        SizedBox(
          width: 4,
        ),
        Expanded(
          child: Column(
            children: [
              Text(
                  'Nếu các thông tin trên không trùng khớp với thông tin trên CCCD của bạn, vui lòng tới quầy giao dịch Chứng khoán VPBank để được hỗ trợ hoặc chủ động chỉnh sửa trực tiếp tại đây:',
                  style:
                      vpTextStyle.body14?.copyWith(color: themeData.gray900)),
              IntrinsicWidth(
                child: Padding(
                  padding: const EdgeInsets.only(right: 26),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CommonAssets.icons.edit.icEdit.svg(
                        color: vpColor.iconPrimary,
                        width: 20,
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: TextButton(
                            onPressed: () {
                              SignUpRouter()
                                  .onPush(SignUpTypeScreen.editInfoOcr);
                              SignUpTracking().signupProfileInfoEditClick1();
                            },
                            child: Text('Chỉnh sửa',
                                style: vpTextStyle.body14
                                    ?.copyWith(color: themeData.primary))),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        )
      ]),
    );
  }
}
