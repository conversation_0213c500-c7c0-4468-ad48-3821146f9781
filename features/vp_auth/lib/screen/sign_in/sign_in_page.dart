import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_biometric_change_detector/flutter_biometric_change_detector.dart';
import 'package:flutter_biometric_change_detector/status_enum.dart';
import 'package:local_auth/local_auth.dart';
import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_auth/core/constant/app_tracking_event.dart';
import 'package:vp_auth/cubit/sign_in/biometric/sign_in_biometric_cubit.dart';
import 'package:vp_auth/cubit/sign_in/sign_in_config_cubit.dart';
import 'package:vp_auth/cubit/sign_in/sign_in_cubit.dart';
import 'package:vp_auth/cubit/sign_in/sign_in_validator_cubit.dart';
import 'package:vp_auth/gen/assets.gen.dart' as assets;
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/router/account_router.dart';
import 'package:vp_auth/screen/sign_in/widgets/dialog_sign_in_error.dart';
import 'package:vp_auth/screen/sign_in/widgets/info_user_logged_widget.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/base/local_storage/local_storage.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

import 'widgets/bottom_button.dart';
import 'widgets/dialog_select_support.dart';

class SignInPage extends StatefulWidget {
  final String? returnTo;

  const SignInPage({this.returnTo, Key? key}) : super(key: key);

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage>  with WidgetsBindingObserver {
  late final signInValidatorCubit = SignInValidatorCubit();
  final _signInCubit = SignInCubit();
  final _signInBiometricCubit = SignInBiometricCubit();

  InputValidatorController _userController = InputValidatorController()
    ..text = '116C300715';

  InputValidatorController _passController = InputValidatorController()
    ..text = '1234567A';

  late Stream<dynamic> gameTetStream =
      RealtimeDatabaseService().getConfig(child: 'gameTet').asStream();
  bool _hasChangedBio = false;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 500), () {
      loginBiometric();
    });
  }

  @override
  void dispose() {
    _userController.dispose();
    _passController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      final hasChanged = await FlutterBiometricChangeDetector.checkBiometric();
      if (hasChanged == AuthChangeStatus.CHANGED) {
        setState(() {
          _hasChangedBio = true;
        });
        return;
      }
    }
  }

  void loginBiometric() async {
    final hasChanged = await FlutterBiometricChangeDetector.checkBiometric();
    if (hasChanged == AuthChangeStatus.CHANGED) {
      setState(() {
        _hasChangedBio = true;
      });
      return;
    }
    final statusBiometric = _signInCubit.getStatusBioemetric;
    final isShowBiometrics =
        _signInBiometricCubit.checkShowBiometrics && statusBiometric;
    if (isShowBiometrics == false) {
      return;
    }
    final passwordSaved = await LocalStorage.instance.read<String>(
          key: _signInCubit.state.userCurrent!.userinfo!.username!,
        ) ??
        "";

    if (passwordSaved.isEmpty) {
      return;
    }
    _signInBiometricCubit.authenticateWithBiometrics(
      context,
      _signInBiometricCubit.checkShowBiometrics,
      () {
        _signInCubit.onSubmitted(
            _signInCubit.state.userCurrent!.userinfo!.username!, passwordSaved);
      },
    );
  }

  Widget _loadBg() {
    return BlocBuilder<SignInConfigCubit, SiginInConfigState>(
        builder: (context, state) {
      final defaultImage = assets.Assets.images.loginBackground.image(
          package: vpAuth,
          fit: BoxFit.fill,
          height: double.infinity,
          width: double.infinity);
      if (state.background == null) {
        return defaultImage;
      }
      bool isHide = state.background?.type == 0;
      return VpImage.fromNetwork(
          network: CachedNetworkImage(
        fit: BoxFit.cover,
        height: double.infinity,
        width: double.infinity,
        imageUrl: isHide
            ? (state.background?.defaultLink ?? '')
            : (state.background?.link ?? ''),
        progressIndicatorBuilder: (context, url, downloadProgress) =>
            defaultImage,
        errorWidget: (context, url, error) => defaultImage,
      ));
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider(create: (_) => SignInConfigCubit()),
          BlocProvider(create: (_) => _signInCubit),
          BlocProvider(create: (_) => signInValidatorCubit),
          BlocProvider(
              create: (_) => _signInBiometricCubit..getAvailableBiometrics()),
        ],
        child: MultiBlocListener(
          listeners: [
            BlocListener<SignInCubit, SignInState>(
              listener: (context, state) async {
                if (state.status.isWrongPass) {
                  final result =
                      await showDialogLoginError(context, state.message ?? "");
                  if (result == true) {
                    gotoForgotPass(context);
                  }
                }
                if (state.status.isSuccess) {
                  AppTracking.instance.logEvent(
                    name: AppTrackingEvent.loginSuccess,
                    type: TrackingType.appsflyer,
                  );

                  context.read<AuthCubit>().loginSuccess(state.userCurrent!);

                  Future.delayed(const Duration(milliseconds: 200), () {
                    final target = (widget.returnTo?.isNotEmpty ?? false)
                        ? Uri.decodeComponent(widget.returnTo!)
                        : '/mainTabbar';

                    context.pushReplacement(target);
                  });
                }
                if (state.status.isFailure) {
                  AppTracking.instance.logEvent(
                    name: AppTrackingEvent.loginFailed,
                    type: TrackingType.appsflyer,
                  );
                }
                if (state.status == SignInStatus.changePassForFirst) {
                  // clearData();
                  if (state.updatePassword) {
                    context.push(AccountRouter.changePassForFirst.routeName,
                        extra: state.updatePin);
                  } else if (state.updatePin) {
                    context.push(AccountRouter.changePinForFirst.routeName);
                  }
                }
              },
            ),
            // MessageListener<SignInCubit>((message) {
            //   if (message.type == SignInStatus.changePassForFirst) {
            //     // clearData();
            //     final updatePassword = message.content.$1;
            //     final updatePin = message.content.$2;
            //     if (updatePassword) {
            //       context.push(AccountRouter.changePassForFirst.routeName,
            //           extra: updatePin);
            //     } else if (updatePin) {
            //       context.push(AccountRouter.changePinForFirst.routeName);
            //     }
            //   }
            // }),
          ],
          child: PopScope(
            canPop: false,
            onPopInvokedWithResult: (didPop, result) async {
              if (didPop) {
                return;
              }
              final navigator = Navigator.of(context);

              AppTracking.instance.logEvent(
                name: AppTrackingEvent.loginScreenBack,
                type: TrackingType.appsflyer,
              );
              navigator.pop(result);
            },
            child: GestureDetector(
              //       onTap: () => AppKeyboardUtils.dismissKeyboard(),
              //       child: BlocProvider<SignInBloc>.value(
              //         value: bloc,
              //         child: BlocListener<SignInBloc, SignInState>(
              //           listener: (context, state) {

              //             if (state is ChangeDefaultPassword) {
              //               bloc.clearData();
              //               navigation.navigateTo(AccountRouter.changePassword);
              //             }

              //             if (state is ChangeInfoForFirstLogin) {
              //               bloc.clearData();
              //               if (state.updatePassword) {
              //                 navigation.navigateTo(AccountRouter.changePassForFirst,
              //                     arguments: state.updatePin);
              //               } else if (state.updatePin) {
              //                 navigation.navigateTo(AccountRouter.changePinForFirst);
              //               }
              //             }

              //             if (state is ChangeDefaultPin) {
              //               bloc.clearData();
              //               navigation.navigateTo(SettingRouter.settingChangePIN,
              //                   arguments: ChangePinParam(ChangePinFlowType.auth));
              //             }

              //             if (state is ListAccountState) {
              //               if (state.listUser.length > 1) {
              //                 showSelectUser(
              //                   context,
              //                   state.listUser,
              //                   onCancel: () => bloc.setUserInfo(null),
              //                 ).then(
              //                   (value) {
              //                     if (value != null) {
              //                       bloc.setUserInfo(value);
              //                     }
              //                   },
              //                 );
              //               }
              //             }
              //             // if (state is UpdateEkyc) {
              //             //   showPopupEKYC();
              //             // }
              //           },
              child: Stack(
                children: [
                  GestureDetector(
                    onTap: () {
                      context.hideKeyboard();
                    },
                    child: Scaffold(
                      extendBodyBehindAppBar: true,
                      resizeToAvoidBottomInset: false,
                      appBar: AppBar(
                        forceMaterialTransparency: true,
                        elevation: 0,
                        backgroundColor: Colors.transparent,
                      ),
                      body: Stack(children: [
                        _loadBg(),
                        Padding(
                          padding: EdgeInsets.only(top: 40),
                          child: Container(
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(16),
                                topRight: Radius.circular(16),
                              ),
                            ),
                            child: _body(),
                          ),
                        )
                      ]),
                    ),
                  ),
                  buildLoadingView()
                ],
              ),
            ),
          ),
        ));
  }

  Widget buildLoadingView() {
    return BlocBuilder<SignInCubit, SignInState>(
      builder: (context, state) {
        return Visibility(
          child: VPBankLoadingWithText(
            text: Text(
              VPCommonLocalize.current.loading,
              style: vpTextStyle.body14.copyColor(vpColor.textTertiary),
            ),
          ),
          visible: state.status.isLoading,
        );
      },
    );
  }

  bool fillColorUser = false;

  bool fillColorPass = false;

  Widget _body() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Expanded(
            child: ListView(
                padding: const EdgeInsets.fromLTRB(0, 204, 0, 0),
                children: [
                  BlocBuilder<SignInCubit, SignInState>(
                      builder: (context, state) {
                    return state.userCurrent != null
                        ? InfoUserLoggedWidget()
                        : BlocBuilder<SignInValidatorCubit,
                            SignInValidatorState>(
                            builder: (context, state) => VPTextField.large(
                              hintText: S.current.account_input_account,
                              textInputAction: TextInputAction.next,
                              controller: _userController,
                              prefixIcon: (color) => IconButton(
                                icon: SvgPicture.asset(
                                  assets.Assets.icons.user,
                                  colorFilter: ColorFilter.mode(
                                    vpColor.iconPrimary,
                                    BlendMode.srcIn,
                                  ),
                                  width: 16,
                                  height: 16,
                                  package: vpAuth,
                                ),
                                onPressed: () {},
                              ),
                              captionText: state.accountErrorLabel,
                              inputType: state.accountInputType,
                              filledColor: vpColor.backgroundElevation0,
                              captionStyle: vpTextStyle.captionRegular?.copyWith(
                                color: vpColor.backgroundElevation0,
                              ),
                              inputFormatters: [
                                UpperCaseTextFormatter(),
                              ],
                            ),
                          );
                  }),
                  const SizedBox(
                    height: 16,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: BlocBuilder<SignInValidatorCubit,
                            SignInValidatorState>(
                          builder: (context, state) {
                            return VPTextField.large(
                              hintText: S.current.account_password,
                              textInputAction: TextInputAction.next,
                              obscureText: true,
                              controller: _passController,
                              prefixIcon: (color) => IconButton(
                                icon: SvgPicture.asset(
                                  assets.Assets.icons.password,
                                  width: 16,
                                  height: 16,
                                  package: vpAuth,
                                  colorFilter: ColorFilter.mode(
                                    vpColor.iconPrimary,
                                    BlendMode.srcIn,
                                  ),
                                ),
                                onPressed: () {},
                              ),
                              captionText: state.passwordErrorLabel,
                              inputType: state.passwordInputType,
                            );
                          },
                        ),
                      ),
                      BlocBuilder<SignInCubit, SignInState>(
                          builder: (context, state) {
                        final statusBiometric =
                            context.read<SignInCubit>().getStatusBioemetric;
                        return BlocBuilder<SignInBiometricCubit,
                            SignInBiometricState>(builder: (context, state) {
                          final isShowBiometrics = context
                                  .read<SignInBiometricCubit>()
                                  .checkShowBiometrics &&
                              statusBiometric;
                          return Visibility(
                            visible: isShowBiometrics && !_hasChangedBio,
                            child: Container(
                              margin: const EdgeInsets.only(left: 8),
                              height: 56,
                              width: 56,
                              child: InkWell(
                                onTap: () {
                                  AppTracking.instance.logEvent(
                                    name: AppTrackingEvent.loginBioClick,
                                    type: TrackingType.appsflyer,
                                  );
                                  loginBiometric();
                                },
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(8)),
                                child: Container(
                                  decoration: BoxDecoration(
                                      color: themeData.bgInput,
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(8)),
                                      border: Border.all(
                                          color: Theme.of(context)
                                              .inputDecorationTheme
                                              .disabledBorder!
                                              .borderSide
                                              .color,
                                          width: 1)),
                                  child: Center(
                                    child: SvgPicture.asset(
                                      state.availableBiometrics.contains(
                                                  BiometricType.face) &&
                                              Platform.isIOS
                                          ? assets.Assets.icons.faceid
                                          : assets.Assets.icons.fingerprint,
                                      package: vpAuth,
                                      colorFilter: ColorFilter.mode(
                                        vpColor.iconPrimary,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        });
                      })
                    ],
                  ),
                  const SizedBox(
                    height: 32,
                  ),
                  VpsButton.primarySmall(
                    width: double.infinity,
                    title: S.current.account_login,
                    alignment: Alignment.center,
                    dismissKeyboardWhenTap: true,
                    onPressed: () {
                      if (_signInCubit.state.userCurrent == null) {
                        _userController
                            .validate(signInValidatorCubit.accountValidator);
                        _passController
                            .validate(signInValidatorCubit.passwordValidator);
                        if (signInValidatorCubit.state.done) {
                          _signInCubit.onSubmitted(
                              _userController.text, _passController.text);
                        }
                      } else {
                        _passController
                            .validate(signInValidatorCubit.passwordValidator);
                        if (signInValidatorCubit.state.done) {
                          _signInCubit.onSubmitted(
                              _signInCubit
                                  .state.userCurrent!.userinfo!.username!,
                              _passController.text);
                        }
                      }
                    },
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  VpsButton.teriatySmall(
                    onPressed: () {
                      AppTracking.instance.logEvent(
                        name: AppTrackingEvent.signupClick,
                        type: TrackingType.appsflyer,
                      );
                      context.push(AccountRouter.splashDefault.routeName);
                    },
                    title: S.current.account_open_account,
                    //    textStyle: vpTextStyle.subtitle14.copyColor(vpColor.textWhite),
                  ),
                  const SizedBox(
                    height: 28,
                  ),
                  StreamBuilder<dynamic>(
                      stream: gameTetStream,
                      builder: (context, snapshot) {
                        if (snapshot.hasData &&
                            ((snapshot.data ?? false) == true)) {
                          return GestureDetector(
                              onTap: () {
                                AppTracking.instance.logEvent(
                                  name: AppTrackingEvent.gamePageEnteredApp,
                                  type: TrackingType.appsflyer,
                                );
                                //navigation.navigateTo(HomeRouter.game);
                              },
                              child: assets.Assets.images.bgTet
                                  .image(package: vpAuth));
                        }
                        return const SizedBox.shrink();
                      }),
                  const SizedBox(
                    height: 8,
                  ),
                  BlocBuilder<SignInConfigCubit, SiginInConfigState>(
                      builder: (context, state) {
                    return state.sologan != null
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(state.sologan!.value ?? '',
                                  textAlign: TextAlign.center,
                                  style: vpTextStyle.captionMedium
                                      .copyColor(vpColor.textWhite)),
                              const SizedBox(
                                height: 8,
                              ),
                              Text("- ${state.sologan?.author} -",
                                  textAlign: TextAlign.right,
                                  style: vpTextStyle.caption2Medium
                                      .copyColor(vpColor.textWhite)),
                            ],
                          )
                        : const SizedBox();
                  }),
                ]),
          ),
          BlocBuilder<SignInCubit, SignInState>(
            builder: (context, state) {
              return BottomButton(
                showSmartOtp: state.userCurrent != null,
                onTapSmartOtp: () async {
                  AppTracking.instance.logEvent(
                    name: AppTrackingEvent.loginSmartOtpClick,
                    type: TrackingType.appsflyer,
                  );
                  await checkShowButtonSmartOTP();
                },
                onTapSupport: showPopupSupport,
                onTapForgotPassword: () async {
                  gotoForgotPass(context);
                  AppTracking.instance.logEvent(
                    name: AppTrackingEvent.forgotPasswordClick,
                    type: TrackingType.appsflyer,
                  );
                },
                onGoToMarketPage: () {
                  context.go('/noauth-priceBoard');
                },
              );
            },
          ),
        ],
      ),
    );
  }

  /*-------- GOTO - FORGOT PASS -------- */
  void gotoForgotPass(BuildContext context) async {
    // bloc.clearData();

    // isHideValidatorMessage = true;

    // userGlobalKey.currentState?.validate();

    // passGlobalKey.currentState?.validate();

    // AppKeyboardUtils.dismissKeyboard();
    context.push(AccountRouter.forgotPasswordVerifyPage.routeName);
    // final value = await navigation.navigateTo(AccountRouter.forgotPasswordPage);

    // if (value == SignInPage.userInfoNull) {
    //   bloc.setUserInfo(null);
    // }
  }

  /*-------- Check show SmartOTP -------- */
  Future<void> checkShowButtonSmartOTP() async {
    try {
      final accountNo = _signInCubit.state.userCurrent?.userinfo?.custodycd;
      final hasSmartOTP =
          await context.push("/getInfoSmartOTP", extra: accountNo);

      if (hasSmartOTP == true) {
        context.push("/smartOTP", extra: accountNo);
      } else {
        showPopupSmartOTP();
      }
    } catch (e, stackTrace) {
      showError(e);
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  /*-------- POP UP - SMART OTP -------- */
  Future<void> showPopupSmartOTP() async {
    VPPopup.oneButton(
      title: S.current.account_unregister_smart_otp_title,
      content: S.current.account_unregister_smart_otp_content,
    )
        .copyWith(
          icon: SvgPicture.asset(
            Assets.icons.icWarning.path,
            package: 'vp_common',
          ),
        )
        .copyWith(
          button: VpsButton.primarySmall(
            title: VPCommonLocalize.current.close,
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        )
        .showDialog(context);
  }

  /*-------- POP UP - SUPPORT -------- */
  Future<void> showPopupSupport() async {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      elevation: 0,
      backgroundColor: Colors.transparent,
      builder: (_) => DialogSelectSupport(),
    );
  }
}
