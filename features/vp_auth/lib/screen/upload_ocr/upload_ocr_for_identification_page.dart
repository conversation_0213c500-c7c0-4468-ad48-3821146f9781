import 'package:flutter/material.dart';
import 'package:vp_auth/cubit/upload_ocr/upload_ocr_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class UploadOcrForIdentificationPage extends StatefulWidget {
  final NavigateNFCType type;

  const UploadOcrForIdentificationPage({super.key, required this.type});

  @override
  _UploadOcrForIdentificationPageState createState() =>
      _UploadOcrForIdentificationPageState();
}

class _UploadOcrForIdentificationPageState
    extends State<UploadOcrForIdentificationPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        context.pop();
        UploadOcrCubit()
          ..goToSDKEkyc(
            isUpdateIdentification: true,
            nfcType: widget.type,
          );
      },
    );
  }

  @override
  Widget build(BuildContext context) => VPScaffold(
        backgroundColor: ColorDefine.transparent,
      );
}
