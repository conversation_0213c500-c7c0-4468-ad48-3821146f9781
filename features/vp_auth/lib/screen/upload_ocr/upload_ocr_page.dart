import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_auth/cubit/upload_ocr/upload_ocr_cubit.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/router/account_router.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_attach_data.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/5.sign_up_upload/components/item_cccd.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/5.sign_up_upload/components/item_description_view.dart';
import 'package:vp_auth/screen/upload_ocr/widgets/progress_bar.dart';
import 'package:vp_auth/utils/widgets/app_signature_camera/button_widget.dart';
import 'package:vp_common/generated/assets.gen.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_auth/gen/assets.gen.dart' as assets;

class UploadOCRPage extends StatefulWidget {
  const UploadOCRPage({super.key});

  // final int? approvald;

  @override
  State<UploadOCRPage> createState() => _UploadOCRPageState();
}

class _UploadOCRPageState extends State<UploadOCRPage> {
  late UploadOcrCubit _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = UploadOcrCubit();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: PopScope(
        canPop: false,
        child: Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                HeaderWidget(
                  title: S.current.account_take_a_photo_verification,
                  subTitle: S.current.account_update_cccd,
                  back: false,
                  icon: ((GetIt.instance<AuthCubit>()
                                  .verificationInfoModel
                                  ?.needChangeIdMode ??
                              0) ==
                          1)
                      ? SvgPicture.asset(
                          Assets.icons.icClose.path,
                          package: 'vp_common',
                        )
                      : null,
                  actionRight: ((GetIt.instance<AuthCubit>()
                                  .verificationInfoModel
                                  ?.needChangeIdMode ??
                              0) ==
                          1)
                      ? () {
                          context.pop();
                        }
                      : null,
                ),
                SizedBox(
                  height: 16,
                ),
                Expanded(
                  child: Column(
                    children: [
                      BlocBuilder<UploadOcrCubit, UploadOCRState>(
                        builder: (context, state) {
                          final obj = SignUpData().attachDataObj;

                          return obj.getFrontIdCard() != ''
                              ? Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: ItemCCCD(
                                            typeUpload:
                                                UploadImageType.frontIDCard,
                                            pathImage: obj.getFrontIdCard(),
                                            title: S.current.account_front,
                                            onCallBack: (type) {}),
                                      ),
                                      SizedBox(
                                        width: 16,
                                      ),
                                      Expanded(
                                        child: ItemCCCD(
                                            typeUpload:
                                                UploadImageType.backIDCard,
                                            pathImage: obj.getBackIdCard(),
                                            title: S.current.account_behind,
                                            onCallBack: (type) {}),
                                      ),
                                      SizedBox(
                                        height: 16,
                                      ),
                                    ],
                                  ),
                                )
                              : SizedBox();
                        },
                      ),
                      SizedBox(
                        height: 16,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          children: [
                            BlocBuilder<UploadOcrCubit, UploadOCRState>(
                              buildWhen: (previous, current) =>
                                  previous.pathIdFront != current.pathIdFront,
                              builder: (context, state) {
                                return state.pathIdFront == ''
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(S.current.account_ekyc_title_1,
                                              softWrap: true,
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 2,
                                              style: vpTextStyle.subtitle16
                                                  ?.copyWith(
                                                      color: themeData.gray900),
                                              textAlign: TextAlign.start),
                                          SizedBox(
                                            height: 16,
                                          ),
                                          ItemDescriptionView(
                                            title: S.current
                                                .account_ekyc_description_1,
                                          ),
                                          SizedBox(
                                            height: 16,
                                          ),
                                          ItemDescriptionView(
                                            title: S.current
                                                .account_ekyc_description_2,
                                          ),
                                          SizedBox(
                                            height: 16,
                                          ),
                                          ItemDescriptionView(
                                            title: S.current
                                                .account_ekyc_description_3,
                                          ),
                                          SizedBox(
                                            height: 16,
                                          ),
                                        ],
                                      )
                                    : const SizedBox();
                              },
                            ),
                            SizedBox(
                              height: 16,
                            ),
                            BlocBuilder<UploadOcrCubit, UploadOCRState>(
                              buildWhen: (previous, current) =>
                                  previous.pathIdFront != current.pathIdFront,
                              builder: (context, state) {
                                return Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    state.pathIdFront == ''
                                        ? ButtonWidget(
                                            colorEnable: themeData.primary,
                                            colorBorder: themeData.bgPopup,
                                            actionWidget: Row(
                                              children: [
                                                SvgPicture.asset(
                                                  assets.Assets.icons.icCamera,
                                                  colorFilter: ColorFilter.mode(
                                                      ColorDefine.white,
                                                      BlendMode.srcIn),
                                                  package: vpAuth,
                                                ),
                                                SizedBox(
                                                  width: 4,
                                                ),
                                                Text(
                                                  S.current
                                                      .account_start_taking_photos,
                                                  style: vpTextStyle.body14
                                                      ?.copyWith(
                                                          color: ColorDefine
                                                              .white),
                                                ),
                                              ],
                                            ),
                                            onPressed: () async {
                                              _bloc.goToSDKEkyc();
                                            },
                                          )
                                        : ButtonWidget(
                                            colorEnable: themeData.primary,
                                            colorBorder: themeData.bgPopup,
                                            actionWidget: Row(
                                              children: [
                                                SvgPicture.asset(
                                                  Assets.icons.icSuccess.path,
                                                  width: 16,
                                                  height: 16,
                                                  colorFilter: ColorFilter.mode(
                                                      ColorDefine.white,
                                                      BlendMode.srcIn),
                                                  package: 'vp_common',
                                                ),
                                                SizedBox(
                                                  width: 4,
                                                ),
                                                Text(
                                                  S.current
                                                      .account_takePhotosSuccessfully,
                                                  style: vpTextStyle.body14
                                                      ?.copyWith(
                                                          color: ColorDefine
                                                              .white),
                                                ),
                                              ],
                                            ),
                                          ),
                                    const SizedBox()
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 24,
                      ),
                      Expanded(child: Container()),
                      BlocBuilder<UploadOcrCubit, UploadOCRState>(
                        buildWhen: (previous, current) =>
                            previous.pathIdFront != current.pathIdFront,
                        builder: (context, state) {
                          return StepBottomView(
                            step: 1,
                            total: 2,
                            onBack: null,
                            enableButton:
                                state.pathIdFront != '' ? true : false,
                            disableColor: themeData.gray300,
                            onNext: () {
                              context.push(AccountRouter.infoOCRPage.routeName);
                            },
                          );
                        },
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
