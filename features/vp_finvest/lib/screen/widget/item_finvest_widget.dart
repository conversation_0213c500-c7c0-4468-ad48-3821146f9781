import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_finvest/generated/assets.gen.dart';
import 'package:vp_finvest/model/spv_information_model.dart';

class ItemFinvestWidget extends StatelessWidget {
  final Function() callBack;
  final SpvInformationReponse product;

  const ItemFinvestWidget({
    super.key,
    required this.callBack,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => callBack.call(),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            color:
                context.isDark ? ColorDefine.bgDialogDart : ColorDefine.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Image.asset(
                context.isDark
                    ? VpFinvestAssets.images.flexfinLogoDarkMode.path
                    : VpFinvestAssets.images.flexfinLogo.path,
                width: 80.0,
                height: 40,
                package: VpFinvestAssets.package,
                fit: BoxFit.contain,
              ),
              SizedBox(height: 4),
              item('Lãi suất', '${product.maxInterestRate}%/năm'),
              item(
                'Kỳ hạn',
                '${product.getMinTerm}-${product.getMaxTerm} ngày',
              ),
              item(
                'Số tiền đầu tư tối thiểu',
                product.invMin.toDouble().toMoney(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget item(String title, String value) {
    return Row(
      children: [
        Text(title, style: vpTextStyle.body14.copyColor(vpColor.strokeBold)),
        const Spacer(),
        Text(value, style: vpTextStyle.subtitle14.copyColor(themeData.gray900)),
      ],
    );
  }
}
