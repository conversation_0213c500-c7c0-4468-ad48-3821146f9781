class MarketInfoApi {
  static const String listMarket = '/api/v2/marketIndex';
  static const String marketVolatility =
      '/invest/api/v2/market/getNewVolatilityByMarket';

  static const String marketPriceChart = '/invest/api/v2/marketInfo';

  static const String topTradingD = '/invest/api/stockTop';

  static const String topTradingHistory = '/invest/api/stockOutstanding';

  static const String buyUpSellDownByMarket = '/invest/api/buyUpSellDown/ByMarket';

  static const String topForeignTradingD = '/invest/api/topForeignTradingD';

  static const String topForeignTrading = '/invest/api/topForeignTrading';

  //danh sách các ngành
  static const String getIcbLevel = '/invest/api/v2/getIcbLevel';

  static const String getHeatMapSectorBySymbols =
      '/invest/api/v2/getHeatMapSectorBySymbols';

  static const String getHeatMapStockBySymbols =
      '/invest/api/v2/getHeatMapStockBySymbols';

  static const String getHeatMapIndustryByIndex =
      '/invest/api/v2/getHeatMapIndustryByIndex';

  static const String getTransactionStatistic =
      '/invest/api/v2/getTransactionStatistic';

  static const String getMarketLiquidity = '/invest/api/v2/getMarketLiquidity';

  static const String getIntradaySupplyDemandChart = '/invest/api/v2/getIntradaySupplyDemandChart';

  static const getAllStock = '/invest/api/stockInfoByList';
}
