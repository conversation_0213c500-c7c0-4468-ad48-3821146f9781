import 'package:flutter/foundation.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/core/const/market_info_path.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/data/chart_buy_up_sell_obj.dart';
import 'package:vp_market/data/heatmap_industry_index_model.dart';
import 'package:vp_market/data/heatmap_sector_symbols_model.dart';
import 'package:vp_market/data/heatmap_stock_symbols_model.dart';
import 'package:vp_market/data/icb_level_model.dart';
import 'package:vp_market/data/market_entity.dart';
import 'package:vp_market/data/market_liquidity_model.dart';
import 'package:vp_market/data/market_price_chart_entity.dart';
import 'package:vp_market/data/market_price_chart_model.dart';
import 'package:vp_market/data/market_top_trading_d_model.dart';
import 'package:vp_market/data/market_top_trading_entity.dart';
import 'package:vp_market/data/market_top_trading_history_model.dart';
import 'package:vp_market/data/market_volatility_entity.dart';
import 'package:vp_market/data/market_volatility_model.dart';
import 'package:vp_market/data/response/base_response.dart';
import 'package:vp_market/data/stock_detail_entity.dart';
import 'package:vp_market/data/supply_demand_model.dart';
import 'package:vp_market/data/top_foreign_trading_d_obj.dart';
import 'package:vp_market/data/top_foreign_trading_obj.dart';
import 'package:vp_market/data/transaction_statistic_chart.dart';
import 'package:vp_market/data/transaction_statistic_model.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:vp_market/data/stock_detail_model.dart';

class MarketRepoImpl implements MarketRepository {
  final Dio restClient;
  final options = CacheOptions(store: MemCacheStore());

  MarketRepoImpl({required this.restClient});

  @override
  Future<MarketVolatilityEntity> getMarketVolatility(String marketCode) async {
    try {
      final response = await restClient.get(
        '${MarketInfoApi.marketVolatility}?marketCode=$marketCode',
      );

      if (response.statusCode == 200 && response.data != null) {
        return compute(transformMarketVolatilityEntity, response.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<MarketPriceChartEntity> getMarketPriceChart(String marketCode) async {
    try {
      final response = await restClient.get(
        '${MarketInfoApi.marketPriceChart}?Marketcode=$marketCode',
      );

      if (response.statusCode == 200 && response.data != null) {
        return compute(transformMarketChartEntity, response.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<MarketTopTradingEntity> getMarketTopTradingDay(
    String floorCode,
  ) async {
    try {
      final response = await restClient.get(
        '${MarketInfoApi.topTradingD}?FloorCode=$floorCode',
      );

      if (response.statusCode == 200 && response.data != null) {
        return compute(transformMarketTopTradingDEntity, response.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<MarketTopTradingEntity> getMarketTopTradingHistory(
    String floorCode,
    String type,
  ) async {
    try {
      final response = await restClient.get(
        '${MarketInfoApi.topTradingHistory}?FloorCode=$floorCode&Type=$type',
      );

      if (response.statusCode == 200 && response.data != null) {
        return compute(transformMarketTopTradingEntity, response.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<ListChartBuyUpSellResponseObj?> getBuyUpSellDownByMarket(
    String indexCode,
  ) async {
    ListChartBuyUpSellResponseObj? resultResponse;
    try {
      final response = await restClient.get(
        MarketInfoApi.buyUpSellDownByMarket,
        queryParameters: {'indexCode': indexCode},
        options:
            options.copyWith(maxStale: const Duration(seconds: 3)).toOptions(),
      );
      resultResponse = ListChartBuyUpSellResponseObj.fromJson(response.data);
    } catch (e) {
      throw e.toString();
    }
    return resultResponse;
  }

  @override
  Future<TopForeignTradingObj?> getTopForeignTrading(String type) async {
    TopForeignTradingObj? topForeignTradingObj;

    try {
      final response = await restClient.get(
        MarketInfoApi.topForeignTrading,
        queryParameters: type.isEmpty ? null : {'Type': type},
      );
      topForeignTradingObj = TopForeignTradingObj.fromJson(response.data);
    } catch (e) {
      throw e.toString();
    }
    return topForeignTradingObj;
  }

  @override
  Future<TopForeignTradingDObj?> getTopForeignTradingD() async {
    TopForeignTradingDObj? topForeignTradingObj;
    try {
      final response = await restClient.get(MarketInfoApi.topForeignTradingD);
      topForeignTradingObj = TopForeignTradingDObj.fromJson(response.data);
    } catch (e) {
      throw e.toString();
    }
    return topForeignTradingObj;
  }

  @override
  Future<List<IcbLevelModel>> getIcbLevel(String icbLevels) async {
    List<IcbLevelModel> list = [];
    try {
      Response response = await restClient.get(
        MarketInfoApi.getIcbLevel,
        queryParameters: {"icbLevels": icbLevels},
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.isSuccess() && value.data != null) {
        value.data[icbLevels].map((e) {
          list.add(IcbLevelModel.fromJson(e));
        }).toList();
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from<ResponseError>(e);
    }
    return list;
  }

  @override
  Future<List<HeatMapSectorBySymbolsModel>> getHeatMapSectorBySymbols({
    required String indexCode,
    required String icbCodeLevel2s,
    required String sortBy,
  }) async {
    List<HeatMapSectorBySymbolsModel> list = [];
    try {
      Response response = await restClient.get(
        MarketInfoApi.getHeatMapSectorBySymbols,
        queryParameters: {
          "indexCode": indexCode,
          "icbCodeLevel2s": icbCodeLevel2s,
          "sortBy": sortBy,
        },
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.isSuccess() && value.data != null) {
        value.data[KeyAPI.data].map((e) {
          list.add(HeatMapSectorBySymbolsModel.fromJson(e));
        }).toList();
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from<ResponseError>(e);
    }
    return list;
  }

  @override
  Future<List<HeatMapStockBySymbolsModel>> getHeatMapStockBySymbols({
    required String symbols,
    required String sortBy,
  }) async {
    List<HeatMapStockBySymbolsModel> list = [];
    try {
      Response response = await restClient.get(
        MarketInfoApi.getHeatMapStockBySymbols,
        queryParameters: {"symbols": symbols, "sortBy": sortBy},
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.isSuccess() && value.data != null) {
        value.data[KeyAPI.data].map((e) {
          list.add(HeatMapStockBySymbolsModel.fromJson(e));
        }).toList();
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from<ResponseError>(e);
    }
    return list;
  }

  @override
  Future<List<HeatMapIndustryByIndexModel>> getHeatMapIndustryByIndex({
    required String icbCodeLevel2s,
    required String sortBy,
  }) async {
    List<HeatMapIndustryByIndexModel> list = [];
    try {
      Response response = await restClient.get(
        MarketInfoApi.getHeatMapIndustryByIndex,
        queryParameters: {"icbCodeLevel2s": icbCodeLevel2s, "sortBy": sortBy},
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.isSuccess() && value.data != null) {
        value.data[KeyAPI.data].map((e) {
          list.add(HeatMapIndustryByIndexModel.fromJson(e));
        }).toList();
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from<ResponseError>(e);
    }
    return list;
  }

  @override
  Future<List<TransactionStatisticChart>> getTransactionStatistic({
    required String timeFrame,
    required String marketCode,
  }) async {
    try {
      Response response = await restClient.get(
        MarketInfoApi.getTransactionStatistic,
        options:
            options.copyWith(maxStale: const Duration(seconds: 3)).toOptions(),
        queryParameters: {"timeFrame": timeFrame, "marketCode": marketCode},
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.data != null) {
        final model = TransactionStatisticModel.fromJson(value.data);
        return transformListTransactionStatistic2(model);
      } else if (value.isEmpty()) {
        return [];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from<ResponseError>(e);
    }
  }

  @override
  Future<List<MarketLiquidityModel>> getMarketLiquidity({
    required String type,
    required String marketCode,
  }) async {
    try {
      Response response = await restClient.get(
        MarketInfoApi.getMarketLiquidity,
        options:
            options.copyWith(maxStale: const Duration(seconds: 3)).toOptions(),
        queryParameters: {"type": type, "marketCode": marketCode},
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.isSuccess() && value.data is List) {
        List<MarketLiquidityModel> data = [];
        value.data.forEach((v) {
          data.add(MarketLiquidityModel.fromJson(v));
        });
        return data;
      } else if (value.isEmpty()) {
        return [];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from<ResponseError>(e);
    }
  }

  @override
  Future<List<SupplyDemandModel>> getIntradaySupplyDemandChart({
    required String marketCode,
  }) async {
    try {
      Response response = await restClient.get(
        MarketInfoApi.getIntradaySupplyDemandChart,
        options:
            options.copyWith(maxStale: const Duration(seconds: 3)).toOptions(),
        queryParameters: {"marketCode": marketCode},
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.isSuccess() && value.data is List) {
        List<SupplyDemandModel> data = [];
        value.data.forEach((v) {
          data.add(SupplyDemandModel.fromJson(v));
        });
        return data;
      }
      if (value.isEmpty()) {
        return [];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from<ResponseError>(e);
    }
  }

  @override
  Future<List<MarketEntity>> getListMarket() async {
    try {
      String marketCodes = StockAppConstants.listExchange.join(',');
      Response response = await restClient.get(
        MarketInfoApi.listMarket,
        queryParameters: {"marketCodes": marketCodes},
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.isSuccess() && value.data is List) {
        List<MarketPriceChartEntity> data = transformListMarketChartEntity(
          value.data,
        );
        List<MarketPriceChartEntity?> result = sortListMarket(data);
        return result.marketEntityList;
      }
      if (value.isEmpty()) {
        return [];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from<ResponseError>(e);
    }
  }

  List<MarketPriceChartEntity?> sortListMarket(
    List<MarketPriceChartEntity> data,
  ) {
    List<MarketPriceChartEntity?> result = [];
    for (int i = 0; i < StockAppConstants.listExchange.length; i++) {
      final int index = data.indexWhere(
        (e) => e.marketCode == StockAppConstants.listExchange[i],
      );
      if (index >= 0) {
        result.add(data[index]);
      } else {
        result.add(null);
      }
    }
    return result;
  }

  @override
  Future<List<StockDetailEntity>> getStockList(String symbols) async {
    try {
      final response = await restClient.get(
        MarketInfoApi.getAllStock,
        queryParameters: {'symbols': symbols},
      );

      final beResponse = BEBaseResponse.fromJson(response.data);

      if (beResponse.isSuccess() && beResponse.data != null) {
        return transformListStockEntities((beResponse.data as Map)['symbols']);
      } else {
        throw ResponseError.fromJson(response.data);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }
}
