import 'package:flutter/material.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class StockDetailEntity {
  String symbol;
  String underlyingSymbol;
  String companyName;
  double price;
  double closePrice;
  double referencePrice;
  double floorPrice;
  double ceilingPrice;
  double openPrice;
  String? exchange;
  double? foreignRoom;
  double lowestPrice = 0; // Gia thap
  double highestPrice = 0; // Gia cao
  double averagePrice = 0; // Gia TB
  num? foreignBuy; //  NN mua
  num? foreignSell; // NN bán
  double? changePercent;
  double? changeValue;
  double? totalVolume;
  double? totalTradingValue;
  num? totalBidQTTY; // Dư mua
  num? totalOfferQTTY; // Dư bán
  String? otherName; // Gia TB

  //buy & sell order info

  String? offerPrice1;
  String? offerPrice2;
  String? offerPrice3;
  num? offerVol1;
  num? offerVol2;
  num? offerVol3;

  String? bidPrice1;
  String? bidPrice2;
  String? bidPrice3;
  num? bidVol1;
  num? bidVol2;
  num? bidVol3;
  num? diff;

  String? stockType;

  String? exrightDate;

  StockDetailEntity({
    required this.symbol,
    this.underlyingSymbol = '',
    required this.companyName,
    this.price = 0,
    this.closePrice = 0,
    this.referencePrice = 0,
    this.floorPrice = 0,
    this.ceilingPrice = 0,
    this.openPrice = 0,
    this.exchange = '',
    this.changePercent,
    this.changeValue,
    this.foreignRoom,
    this.lowestPrice = 0,
    this.highestPrice = 0,
    this.averagePrice = 0,
    this.otherName,
    this.foreignBuy,
    this.foreignSell,
    this.totalVolume,
    this.offerPrice1,
    this.offerPrice2,
    this.offerPrice3,
    this.offerVol1,
    this.offerVol2,
    this.offerVol3,
    this.bidPrice1,
    this.bidPrice2,
    this.bidPrice3,
    this.bidVol1,
    this.bidVol2,
    this.bidVol3,
    this.totalTradingValue,
    this.totalBidQTTY,
    this.totalOfferQTTY,
    this.stockType,
    this.exrightDate,
    this.diff,
  });

  StockDetailEntity copyWith({
    String? symbol,
    String? underlyingSymbol,
    String? companyName,
    double? price,
    double? closePrice,
    double? referencePrice,
    double? floorPrice,
    double? ceilingPrice,
    double? openPrice,
    String? exchange,
    double? foreignRoom,
    double? lowestPrice,
    double? highestPrice,
    double? averagePrice,
    String? otherName,
    num? foreignBuy,
    num? foreignSell,
    double? changePercent,
    double? changeValue,
    double? totalVolume,
    double? totalTradingValue,
    num? totalBidQTTY,
    num? totalOfferQTTY,
    String? offerPrice1,
    String? offerPrice2,
    String? offerPrice3,
    num? offerVol1,
    num? offerVol2,
    num? offerVol3,
    String? bidPrice1,
    String? bidPrice2,
    String? bidPrice3,
    num? bidVol1,
    num? bidVol2,
    num? bidVol3,
    num? diff,
    String? stockType,
    String? exrightDate,
  }) => StockDetailEntity(
    symbol: symbol ?? this.symbol,
    underlyingSymbol: underlyingSymbol ?? this.underlyingSymbol,
    companyName: companyName ?? this.companyName,
    price: price ?? this.price,
    closePrice: closePrice ?? this.closePrice,
    referencePrice: referencePrice ?? this.referencePrice,
    floorPrice: floorPrice ?? this.floorPrice,
    ceilingPrice: ceilingPrice ?? this.ceilingPrice,
    openPrice: openPrice ?? this.openPrice,
    exchange: exchange ?? this.exchange,
    foreignRoom: foreignRoom ?? this.foreignRoom,
    lowestPrice: lowestPrice ?? this.lowestPrice,
    highestPrice: highestPrice ?? this.highestPrice,
    averagePrice: averagePrice ?? this.averagePrice,
    otherName: otherName ?? this.otherName,
    foreignBuy: foreignBuy ?? this.foreignBuy,
    foreignSell: foreignSell ?? this.foreignSell,
    changePercent: changePercent ?? this.changePercent,
    changeValue: changeValue ?? this.changeValue,
    totalVolume: totalVolume ?? this.totalVolume,
    totalTradingValue: totalTradingValue ?? this.totalTradingValue,
    totalBidQTTY: totalBidQTTY ?? this.totalBidQTTY,
    totalOfferQTTY: totalOfferQTTY ?? this.totalOfferQTTY,
    offerPrice1: offerPrice1 ?? this.offerPrice1,
    offerPrice2: offerPrice2 ?? this.offerPrice2,
    offerPrice3: offerPrice3 ?? this.offerPrice3,
    offerVol1: offerVol1 ?? this.offerVol1,
    offerVol2: offerVol2 ?? this.offerVol2,
    offerVol3: offerVol3 ?? this.offerVol3,
    bidPrice1: bidPrice1 ?? this.bidPrice1,
    bidPrice2: bidPrice2 ?? this.bidPrice2,
    bidPrice3: bidPrice3 ?? this.bidPrice3,
    bidVol1: bidVol1 ?? this.bidVol1,
    bidVol2: bidVol2 ?? this.bidVol2,
    bidVol3: bidVol3 ?? this.bidVol3,
    stockType: stockType ?? this.stockType,
    exrightDate: exrightDate ?? this.exrightDate,
    diff: diff ?? this.diff,
  );

  double get currentPrice => price == 0 ? referencePrice : price;

  num get totalBestBidVol => (bidVol1 ?? 0) + (bidVol2 ?? 0) + (bidVol3 ?? 0);

  num get totalBestOfferVol =>
      (offerVol1 ?? 0) + (offerVol2 ?? 0) + (offerVol3 ?? 0);

  num get totalBestBidOfferVol => totalBestBidVol + totalBestOfferVol;

  Color get colorByPrice => CommonColorUtils.colorByPrice(
    currentPrice: currentPrice,
    referencePrice: referencePrice,
    ceilingPrice: ceilingPrice,
    floorPrice: floorPrice,
  );
}
