import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:vp_market/data/transaction_statistic_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_socket/vp_socket.dart';

class TransactionStatisticChart {
  final num value;
  final String? name;
  final Color color;

  TransactionStatisticChart({
    required this.value,
    required this.name,
    required this.color,
  });
}

List<TransactionStatisticChart> transformListTransactionStatistic2(
    TransactionStatisticModel model,
    ) {
  List<TransactionStatisticChart> list = [
    TransactionStatisticChart(
      value: model.l?.rateBuyUpValueL ?? 0,
      name: VPMarketInfoLocalize.current.big,
      color: const Color(0xFF00744B),
    ),
    TransactionStatisticChart(
      value: model.m?.rateBuyUpValueM ?? 0,
      name: VPMarketInfoLocalize.current.medium,
      color: const Color(0xFF009460),
    ),
    TransactionStatisticChart(
      value: model.s?.rateBuyUpValueS ?? 0,
      name: VPMarketInfoLocalize.current.small,
      color: const Color(0xFF33B588),
    ),
    TransactionStatisticChart(
      value: model.l?.rateSellDownValueL ?? 0,
      name: VPMarketInfoLocalize.current.big,
      color: const Color(0xFFAD1D26),
    ),
    TransactionStatisticChart(
      value: model.m?.rateSellDownValueM ?? 0,
      name: VPMarketInfoLocalize.current.medium,
      color: const Color(0xFFE8312B),
    ),
  ];
  num rateSellDownValueS =
      100 -
          (list.map((e) => num.parse(e.value.toStringAsFixed(2))).toList().sum);
  list.add(
    TransactionStatisticChart(
      value: rateSellDownValueS,
      name: VPMarketInfoLocalize.current.small,
      color: const Color(0xFFFF5E59),
    ),
  );
  return list;
}


List<TransactionStatisticChart> transformListTransactionStatistic(
  VPTransactionStatisticData model,
) {
  List<TransactionStatisticChart> list = [
    TransactionStatisticChart(
      value: model.l?.rateBuyUpValueL ?? 0,
      name: VPMarketInfoLocalize.current.big,
      color: const Color(0xFF00744B),
    ),
    TransactionStatisticChart(
      value: model.m?.rateBuyUpValueM ?? 0,
      name: VPMarketInfoLocalize.current.medium,
      color: const Color(0xFF009460),
    ),
    TransactionStatisticChart(
      value: model.s?.rateBuyUpValueS ?? 0,
      name: VPMarketInfoLocalize.current.small,
      color: const Color(0xFF33B588),
    ),
    TransactionStatisticChart(
      value: model.l?.rateSellDownValueL ?? 0,
      name: VPMarketInfoLocalize.current.big,
      color: const Color(0xFFAD1D26),
    ),
    TransactionStatisticChart(
      value: model.m?.rateSellDownValueM ?? 0,
      name: VPMarketInfoLocalize.current.medium,
      color: const Color(0xFFE8312B),
    ),
  ];
  num rateSellDownValueS =
      100 -
      (list.map((e) => num.parse(e.value.toStringAsFixed(2))).toList().sum);
  list.add(
    TransactionStatisticChart(
      value: rateSellDownValueS,
      name: VPMarketInfoLocalize.current.small,
      color: const Color(0xFFFF5E59),
    ),
  );
  return list;
}
