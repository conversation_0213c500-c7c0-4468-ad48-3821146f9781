import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class FilterItem extends StatelessWidget {
  const FilterItem({super.key, required this.value, this.isSelected = false});

  final bool isSelected;

  final String value;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: BoxDecoration(
        color: isSelected ? themeData.primary : themeData.highlightBg,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        value,
        style: vpTextStyle.captionMedium?.copyWith(
          color: isSelected ? themeData.textEnable : themeData.black,
        ),
      ),
    );
  }
}
