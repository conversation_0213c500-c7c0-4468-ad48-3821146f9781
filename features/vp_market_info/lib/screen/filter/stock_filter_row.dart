import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/screen/filter/filter_item.dart';

class FilterRow<T> extends StatefulWidget {
  const FilterRow({
    super.key,
    required this.title,
    required this.items,
    required this.onChanged,
    required this.builder,
    this.initValue,
  });

  final String title;

  final T? initValue;

  final List<T> items;

  final String Function(T) builder;

  final Function(T) onChanged;

  @override
  FilterRowState<T> createState() => FilterRowState<T>();
}

class FilterRowState<T> extends State<FilterRow<T>> {
  late T? itemSelected = widget.initValue;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 60,
          child: Text(
            widget.title,
            style: vpTextStyle.body14?.copyWith(color: themeData.gray500),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 200,
          child: SizedBox(
            height: 30,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemBuilder: (_, index) {
                final item = widget.items[index];

                return InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: () => _changeCriteria(item),
                  child: FilterItem(
                    value: widget.builder(item),
                    isSelected: itemSelected == item,
                  ),
                );
              },
              separatorBuilder: (_, index) => const SizedBox(width: 8),
              itemCount: widget.items.length,
            ),
          ),
        ),
      ],
    );
  }

  void _changeCriteria(T item) {
    if (item != itemSelected) {
      setState(() => itemSelected = item);
      widget.onChanged(item);
    }
  }
}
