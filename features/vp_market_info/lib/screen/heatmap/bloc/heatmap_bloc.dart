import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/data/heatmap_sector_symbols_model.dart';
import 'package:vp_market/data/icb_level_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_state.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

enum CriteriaEnum {
  marketCap,
  totalTradingValue,
  foreignNetBuyValue,
  foreignNetSellValue,
}

class HeatmapBloc extends Cubit<HeatmapState> with StockInfoSocketMixin {
  HeatmapBloc() : super(HeatmapState()) {
    onRefresh();
  }

  final MarketRepository _repository = GetIt.instance<MarketRepository>();

  List<ItemSelect> listCriteria = [
    ItemSelect(
      id: CriteriaEnum.marketCap,
      title: VPMarketInfoLocalize.current.capitalization,
      selected: true,
      value: CriteriaEnum.marketCap.name,
    ),
    ItemSelect(
      id: CriteriaEnum.totalTradingValue,
      title: VPMarketInfoLocalize.current.totalTradingValue,
      value: CriteriaEnum.totalTradingValue.name,
    ),
    ItemSelect(
      id: CriteriaEnum.foreignNetBuyValue,
      title: VPMarketInfoLocalize.current.foreignNetBuyValue,
      value: CriteriaEnum.foreignNetBuyValue.name,
    ),
    ItemSelect(
      id: CriteriaEnum.foreignNetSellValue,
      title: VPMarketInfoLocalize.current.foreignNetSellValue,
      value: CriteriaEnum.foreignNetSellValue.name,
    ),
  ];

  List<ItemSelect> listFloor = [
    ItemSelect(
      id: MarketInfoConstants.all,
      title: VPMarketInfoLocalize.current.all,
      selected: true,
    ),
    ItemSelect(id: MarketInfoConstants.VNINDEX, title: MarketInfoConstants.hsx),
    ItemSelect(
      id: MarketInfoConstants.HNXINDEX,
      title: MarketInfoConstants.hnx,
    ),
    ItemSelect(
      id: MarketInfoConstants.UPCOMINDEX,
      title: MarketInfoConstants.upCom,
    ),
  ];

  void selectIcbLevel(IcbLevelModel? model) {
    if (model != null && model.icbCode != state.icbLevelModel.icbCode) {
      emit(state.copyWith(icbLevelModel: model));
      onRefresh();
    }
  }

  void selectCriteria(ItemSelect? value) {
    if (value != null && value.id != state.criteria.id) {
      emit(state.copyWith(criteria: value));
      onRefresh();
    }
  }

  void selectFloor(ItemSelect? value) {
    if (value != null && value.id != state.floor.id) {
      emit(state.copyWith(floor: value));
      onRefresh();
    }
  }

  Future onRefresh() async {
    emit(state.copyWith(status: StatusEnum.loading));
    try {
      await Future.wait([
        if (state.icbLevels.isEmpty) _getIcbLevel(),
        _getHeatMapSectorBySymbols(),
      ]);
      emit(state.copyWith(status: StatusEnum.success));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: StatusEnum.error));
      showError(e);
    }
  }

  Future _getIcbLevel() async {
    final icbLevels = await _repository.getIcbLevel(AppConstants.icbLevel2);
    final model = IcbLevelModel(
      icbCode: MarketInfoConstants.all,
      icbName: VPMarketInfoLocalize.current.all,
    );
    icbLevels.insert(0, model);
    emit(state.copyWith(icbLevels: icbLevels));
  }

  Future _getHeatMapSectorBySymbols() async {
    final result = await _repository.getHeatMapSectorBySymbols(
      indexCode: state.floor.id,
      icbCodeLevel2s: state.icbLevelModel.icbCode ?? '',
      sortBy: state.criteria.value,
    );
    emit(state.copyWith(listSector: result));
    if (result.isNotEmpty) {
      if (state.icbLevelModel.icbCode != MarketInfoConstants.all) {
        //biểu đồ nhiệt theo mã
        await _getHeatMapStockBySymbols(result[0]);
      } else {
        //biểu đồ nhiệt theo ngành
        await _getHeatMapIndustryByIndex();
      }
    }
  }

  Future _getHeatMapStockBySymbols(HeatMapSectorBySymbolsModel model) async {
    String symbols = (model.listStock ?? []).join(',');
    final result = await _repository.getHeatMapStockBySymbols(
      symbols: symbols,
      sortBy: state.criteria.value,
    );
    emit(state.copyWith(listStock: result));

    unsubscribeStockInfo();

    subscribeStockInfo(
      state.listStock
          .where((e) => e.symbol.hasData)
          .map((e) => e.symbol!)
          .toSet(),
    );
  }

  Future _getHeatMapIndustryByIndex() async {
    final result = await _repository.getHeatMapIndustryByIndex(
      icbCodeLevel2s: state.icbLevelModel.icbCode ?? MarketInfoConstants.all,
      sortBy:
          state.criteria.id.index < 2
              ? state.criteria.value
              : listCriteria[0].value,
    );
    emit(state.copyWith(listIndustry: result));
  }

  num? percentChangeIcbLevel2(String? icbCode) {
    if (state.listIndustry.isNotEmpty) {
      return state.listIndustry
          .where((e) => e.icbCodeLevel2 == icbCode)
          .first
          .percentChangeIcbLevel2;
    }
    return null;
  }

  @override
  void onSocketStockInfoListener(VPStockInfoData data) {
    final listStock = state.listStock;

    final stock = listStock.where((e) => e.symbol == data.symbol).first;
    final closePrice = data.closePrice?.toDouble();
    final stockPercentChange = data.percentChange?.toDouble();
    final foreignNetValue = data.foreignNetValue;
    final totalTradingValue = data.totalTradingValue;
    final foreignBuyValue = data.foreignBuyVolume;
    final foreignSellValue = data.foreignSellValue;
    bool change = false;
    if (closePrice != null && stock.closePrice != closePrice) {
      stock.closePrice = closePrice;
      change = true;
    }
    if (stockPercentChange != null &&
        stock.stockPercentChange != stockPercentChange) {
      stock.stockPercentChange = stockPercentChange;
      change = true;
    }
    if (totalTradingValue != null &&
        stock.totalTradingValue != totalTradingValue &&
        state.criteria.id == CriteriaEnum.totalTradingValue) {
      stock.totalTradingValue = totalTradingValue;
      change = true;
    }
    if (foreignNetValue != null) {
      if (foreignNetValue >= 0 &&
          stock.foreignNetBuyValue != foreignNetValue &&
          state.criteria.id == CriteriaEnum.foreignNetBuyValue) {
        stock.foreignNetBuyValue = foreignNetValue;
        change = true;
      }
      if (foreignNetValue <= 0 &&
          stock.foreignNetSellValue != foreignNetValue * (-1) &&
          state.criteria.id == CriteriaEnum.foreignNetSellValue) {
        stock.foreignNetSellValue = foreignNetValue * (-1);
        change = true;
      }
    }
    if (state.criteria.id == CriteriaEnum.foreignNetBuyValue ||
        state.criteria.id == CriteriaEnum.foreignNetSellValue) {
      if (foreignBuyValue != null && stock.foreignBuyValue != foreignBuyValue) {
        stock.foreignBuyValue = foreignBuyValue;
        change = true;
      }
      if (foreignSellValue != null &&
          stock.foreignSellValue != foreignSellValue) {
        stock.foreignSellValue = foreignSellValue;
        change = true;
      }
    }
    if (change) {
      emit(state.copyWith(listStock: listStock));
    }
  }

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
