import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/none_widget.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_bloc.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_state.dart';
import 'package:vp_market/screen/heatmap/widgets/heatmap_sector.dart';
import 'package:vp_market/screen/heatmap/widgets/heatmap_stock.dart';
import 'package:vp_market/screen/heatmap/widgets/option_heatmap_widget.dart';
import 'package:vp_market/screen/widget/app_lifecycle_listener.dart';
import 'package:vp_market/screen/widget/error_network_widget.dart';

class HeatmapPage extends StatefulWidget {
  const HeatmapPage({super.key});

  @override
  State<HeatmapPage> createState() => _HeatmapPageState();
}

class _HeatmapPageState extends AppLifeCycleListener<HeatmapPage>
    with AutomaticKeepAliveClientMixin<HeatmapPage> {
  late final HeatmapBloc _bloc = HeatmapBloc();

  @override
  void onResumeApp() {
    if (mounted) {
      _bloc.onRefresh();
    }
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider<HeatmapBloc>.value(
      value: _bloc,
      child: Scaffold(
        body: Column(
          children: [
            HeaderWidget(
              subTitle: VPMarketInfoLocalize.current.stock,
              title: VPMarketInfoLocalize.current.marketInfo,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: () => _bloc.onRefresh(),
                color: themeData.primary,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          VPMarketInfoLocalize.current.heatmap2,
                          style: vpTextStyle.body16?.copyWith(
                            color: vpColor.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 16),
                        const OptionHeatmapWidget(),
                        const SizedBox(height: 16),
                        BlocBuilder<HeatmapBloc, HeatmapState>(
                          builder: (context, state) {
                            if (state.status == StatusEnum.loading) {
                              return Shimmer.fromColors(
                                baseColor: themeData.skeletonBase,
                                highlightColor: themeData.skeletonHighLight,
                                child: Container(
                                  height: 343,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color:
                                        Theme.of(
                                          context,
                                        ).scaffoldBackgroundColor,
                                  ),
                                ),
                              );
                            } else if (state.status == StatusEnum.error) {
                              return ErrorNetworkWidget(
                                tryAgain: true,
                                onPressed: () => _bloc.onRefresh(),
                              );
                            } else {
                              if (state.icbLevelModel.icbCode ==
                                  MarketInfoConstants.all) {
                                if (state.listSector.isNotEmpty) {
                                  return const HeatmapSector();
                                }
                              } else if (state.listStock.isNotEmpty) {
                                return const HeatmapStock();
                              }
                              return NoneWidget(
                                padding: const EdgeInsets.only(top: 24),
                                desc: VPMarketInfoLocalize.current.noData,
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
