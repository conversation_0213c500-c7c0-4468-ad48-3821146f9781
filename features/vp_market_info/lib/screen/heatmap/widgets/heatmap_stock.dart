import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_treemap/treemap.dart';
import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/none_widget.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/heatmap_stock_symbols_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_bloc.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_state.dart';
import 'package:vp_market/screen/heatmap/widgets/tooltips/tooltip_stock.dart';

class HeatmapStock extends StatefulWidget {
  const HeatmapStock({super.key});

  @override
  State<HeatmapStock> createState() => _HeatmapStockState();
}

class _HeatmapStockState extends State<HeatmapStock> {
  List<HeatMapStockBySymbolsModel> listStock = [];
  num total = 0;

  HeatMapStockBySymbolsModel getModelByTile(
    String symbol,
    List<HeatMapStockBySymbolsModel> listStock,
  ) {
    final HeatMapStockBySymbolsModel value =
        listStock.where((e) => e.symbol == symbol).first;
    return value;
  }

  void setData(List<HeatMapStockBySymbolsModel> list, CriteriaEnum criteria) {
    switch (criteria) {
      case CriteriaEnum.marketCap:
        for (var e in list) {
          total += e.marketCap ?? 0;
        }
        listStock.addAll(list.where((e) => (e.marketCap ?? 0) != 0));
        break;
      case CriteriaEnum.totalTradingValue:
        for (var e in list) {
          total += e.totalTradingValue ?? 0;
        }
        listStock.addAll(list.where((e) => (e.totalTradingValue ?? 0) != 0));
        break;
      case CriteriaEnum.foreignNetBuyValue:
        for (var e in list) {
          total += e.foreignNetBuyValue ?? 0;
        }
        listStock.addAll(list.where((e) => (e.foreignNetBuyValue ?? 0) != 0));
        break;
      case CriteriaEnum.foreignNetSellValue:
        for (var e in list) {
          total += e.foreignNetSellValue ?? 0;
        }
        listStock.addAll(list.where((e) => (e.foreignNetSellValue ?? 0) != 0));
        break;
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HeatmapBloc, HeatmapState>(
      builder: (context, state) {
        setData(state.listStock, state.criteria.id);
        return total != 0
            ? SfTreemap(
              dataCount: listStock.length,
              weightValueMapper: (int index) {
                switch (state.criteria.id) {
                  case CriteriaEnum.marketCap:
                    return ((listStock[index].marketCap ?? 0) / total) * 100;
                  case CriteriaEnum.totalTradingValue:
                    return ((listStock[index].totalTradingValue ?? 0) / total) *
                        100;
                  case CriteriaEnum.foreignNetBuyValue:
                    return ((listStock[index].foreignNetBuyValue ?? 0) /
                            total) *
                        100;
                  case CriteriaEnum.foreignNetSellValue:
                    return ((listStock[index].foreignNetSellValue ?? 0) /
                            total) *
                        100;
                  default:
                    return ((listStock[index].marketCap ?? 0) / total) * 100;
                }
              },
              tooltipSettings: TreemapTooltipSettings(
                color: themeData.white,
                hideDelay: 5.0,
              ),
              levels: <TreemapLevel>[
                TreemapLevel(
                  groupMapper: (int index) {
                    return listStock[index].symbol;
                  },
                  labelBuilder: (BuildContext context, TreemapTile tile) {
                    String percent =
                        (getModelByTile(
                                  tile.group,
                                  listStock,
                                ).stockPercentChange ??
                                0)
                            .getChangePercentDisplay();
                    return Container(
                      color: getModelByTile(tile.group, listStock).colorByPrice,
                      child: Center(
                        child: Visibility(
                          visible: !(tile.weight < 3 && listStock.length == 2),
                          child: Text(
                            '${tile.group} ($percent)',
                            style: vpTextStyle.subtitle14?.copyWith(
                              color: themeData.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    );
                  },
                  tooltipBuilder: (BuildContext context, TreemapTile tile) {
                    return Container(
                      width: MediaQuery.of(context).size.width * 2 / 3,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: TooltipStock(symbol: tile.group),
                    );
                  },
                ),
              ],
            )
            : NoneWidget(
              padding: const EdgeInsets.only(top: 24),
              desc: VPMarketInfoLocalize.current.noData,
            );
      },
    );
  }
}
