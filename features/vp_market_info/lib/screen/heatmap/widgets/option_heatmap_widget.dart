import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_bloc.dart';
import 'package:vp_market/screen/widget/item_header_widget.dart';

import 'business_bottom_sheet.dart';

class OptionHeatmapWidget extends StatelessWidget {
  const OptionHeatmapWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final bloc = context.watch<HeatmapBloc>();
    final state = bloc.state;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Text(
                VPMarketInfoLocalize.current.branch,
                style: vpTextStyle.body14?.copyWith(color: themeData.gray500),
              ),
            ),
            Expanded(
              flex: 3,
              child: ItemHeaderWidget(
                text: state.icbLevelModel.icbName ?? '',
                icon: VpMarketAssets.icons.arrowDrop.path,
                textStyle: vpTextStyle.captionMedium?.copyWith(
                  color: vpColor.textPrimary,
                ),
                isFull: true,
                onTap: () {
                  if (state.status != StatusEnum.loading) {
                    showBusinessBottomSheet(context, state.icbLevels).then((
                      value,
                    ) {
                      bloc.selectIcbLevel(value);
                    });
                  }
                },
                // isFull: true,
              ),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Text(
                VPMarketInfoLocalize.current.criteria,
                style: vpTextStyle.body14?.copyWith(color: themeData.gray500),
              ),
            ),
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  ListSelectOne(
                    listItem: bloc.listCriteria,
                    callBackItemSelect: (value) => bloc.selectCriteria(value),
                    padding: const EdgeInsets.symmetric(
                      vertical: 4,
                      horizontal: 8,
                    ),
                    allOneLine: true,
                  ),
                ],
              ),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Text(
                VPMarketInfoLocalize.current.priceFloor,
                style: vpTextStyle.body14?.copyWith(color: themeData.gray500),
              ),
            ),
            Expanded(
              flex: 3,
              child: ListSelectOne(
                listItem: bloc.listFloor,
                callBackItemSelect: (value) => bloc.selectFloor(value),
                allOneLine: true,
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
