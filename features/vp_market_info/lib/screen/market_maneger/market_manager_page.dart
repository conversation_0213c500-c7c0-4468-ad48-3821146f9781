import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/heatmap_page.dart';
import 'package:vp_market/screen/overview/market_page.dart';
import 'package:vp_market/screen/top_stocks/top_stock_market_page.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class MarketManagerPage extends StatefulWidget {
  const MarketManagerPage({super.key, required this.args});

  final MarketDetailArgs args;

  @override
  State<MarketManagerPage> createState() => _MarketManagerPageState();
}

class _MarketManagerPageState extends State<MarketManagerPage> {
  late PageController pageController = PageController(
    initialPage: tabIndexSelected,
  );

  late int tabIndexSelected = widget.args.initialPage ?? 0;

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      bottomBar: MarketManagerBottomView(
        initTabIndexSelected: tabIndexSelected,
        onPageChange: (index) => pageController.jumpToPage(index),
      ),
      child: PageView(
        controller: pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          MarketPage(indexCode: widget.args.indexCode),
          const TopStockMarketPage(),
          const HeatmapPage(),
        ],
      ),
    );
  }
}

class MarketManagerBottomView extends StatefulWidget {
  const MarketManagerBottomView({
    super.key,
    this.onPageChange,
    this.initTabIndexSelected = 0,
  });

  final Function(int page)? onPageChange;

  final int initTabIndexSelected;

  @override
  State<MarketManagerBottomView> createState() =>
      _MarketManagerBottomViewState();
}

class _MarketManagerBottomViewState extends State<MarketManagerBottomView> {
  late int indexSelected = widget.initTabIndexSelected;

  void onPageChanged(int page) {
    setState(() => indexSelected = page);
    widget.onPageChange?.call(page);
  }

  @override
  Widget build(BuildContext context) {
    final AutoSizeGroup autoSizeGroup = AutoSizeGroup();
    return VPBottomAppBar(
      color: themeData.bgBottomBar,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            flex: 1,
            child: NavItem(
              isSelected: indexSelected == 0,
              package: VpMarketAssets.package,
              selectIcon: VpMarketAssets.icons.icOverview.path,
              unselectIcon: VpMarketAssets.icons.icOverview2.path,
              title: VPMarketInfoLocalize.current.overview,
              onTap: () => onPageChanged(0),
              autoSizeGroup: autoSizeGroup,
              haveHeadDivider: true,
            ),
          ),
          Expanded(
            flex: 1,
            child: NavItem(
              isSelected: indexSelected == 1,
              package: VpMarketAssets.package,
              selectIcon: VpMarketAssets.icons.icTopStock.path,
              unselectIcon: VpMarketAssets.icons.icTopStock2.path,
              title: VPMarketInfoLocalize.current.topStocks,
              onTap: () => onPageChanged(1),
              autoSizeGroup: autoSizeGroup,
              haveHeadDivider: true,
            ),
          ),
          Expanded(
            flex: 1,
            child: NavItem(
              isSelected: indexSelected == 2,
              package: VpMarketAssets.package,
              selectIcon: VpMarketAssets.icons.icHeatmap.path,
              unselectIcon: VpMarketAssets.icons.icHeatmap2.path,
              title: VPMarketInfoLocalize.current.heatmap,
              onTap: () => onPageChanged(2),
              autoSizeGroup: autoSizeGroup,
              haveHeadDivider: true,
            ),
          ),
        ],
      ),
    );
  }
}
