import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/data/transaction_statistic_chart.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/cashflow_analysis/cashflow_analysis_state.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class CashFlowAnalysisBloc extends Cubit<CashFlowAnalysisState>
    with TransactionStatisticSocketMixin {
  CashFlowAnalysisBloc({required MarketInfoModel market})
    : super(
        CashFlowAnalysisState(
          timeFrame: ItemSelect(
            id: MarketInfoConstants.oneDay,
            title: '1 ${VPMarketInfoLocalize.current.day.toLowerCase()}',
            selected: true,
          ),
          market: market,
          status: ApiStatus.initial(),
        ),
      );

  final _repository = GetIt.instance<MarketRepository>();

  void selectTimeTransactionStatistic(ItemSelect value) {
    if (value.id != state.timeFrame.id) {
      emit(state.copyWith(timeFrame: value));
      loadData();
    }
  }

  Future loadData() async {
    try {
      emit(state.copyWith(status: ApiStatus.loading()));

      final result = await _repository.getTransactionStatistic(
        marketCode: state.market.indexCode.marketCode,
        timeFrame: state.timeFrame.id,
      );

      emit(state.copyWith(status: ApiStatus.done(), charts: result));

      subscribeTransactionStatistic({state.market.indexCode.socketChannel});
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }

  @override
  void onSocketTransactionStatisticListener(VPTransactionStatisticData data) {
    if (data.timeFrame == state.timeFrame.id) {
      if (data.s != null) {
        final charts = transformListTransactionStatistic(data);
        emit(state.copyWith(charts: charts));
      }
    }
  }

  @override
  Future<void> close() {
    unsubscribeTransactionStatistic();
    return super.close();
  }
}
