import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/data/transaction_statistic_chart.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class CashFlowAnalysisState {
  final ApiStatus status;

  final ItemSelect timeFrame;

  final List<TransactionStatisticChart> charts;

  final MarketInfoModel market;

  CashFlowAnalysisState({
    required this.status,
    required this.market,
    required this.timeFrame,
    this.charts = const [],
  });

  CashFlowAnalysisState copyWith({
    ApiStatus? status,
    MarketInfoModel? market,
    List<TransactionStatisticChart>? charts,
    ItemSelect? timeFrame,
  }) {
    return CashFlowAnalysisState(
      status: status ?? this.status,
      market: market ?? this.market,
      charts: charts ?? this.charts,
      timeFrame: timeFrame ?? this.timeFrame,
    );
  }
}
