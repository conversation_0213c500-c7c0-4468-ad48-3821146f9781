import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/none_widget.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/data/transaction_statistic_chart.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/cashflow_analysis/cashflow_analysis_bloc.dart';
import 'package:vp_market/screen/overview/cashflow_analysis/cashflow_analysis_state.dart';
import 'package:vp_market/screen/widget/chart_loading_widget.dart';
import 'package:vp_market/screen/widget/list_option_expand.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

List<ItemSelect> listItem = [
  ItemSelect(
    id: MarketInfoConstants.halfHour,
    title: '30 ${VPMarketInfoLocalize.current.minute.toLowerCase()}',
  ),
  ItemSelect(
    id: MarketInfoConstants.twoHours,
    title: '2 ${VPMarketInfoLocalize.current.hour.toLowerCase()}',
  ),
  ItemSelect(
    id: MarketInfoConstants.oneDay,
    title: '1 ${VPMarketInfoLocalize.current.day.toLowerCase()}',
    selected: true,
  ),
];

class CashFlowAnalysisWidget extends StatefulWidget {
  const CashFlowAnalysisWidget({required this.market, super.key});

  final MarketInfoModel market;

  @override
  State<CashFlowAnalysisWidget> createState() => _CashFlowAnalysisWidgetState();
}

class _CashFlowAnalysisWidgetState extends State<CashFlowAnalysisWidget> {
  @override
  Widget build(BuildContext context) {
    final double size = (MediaQuery.of(context).size.width - 16 * 3) / 2;
    return BlocProvider<CashFlowAnalysisBloc>(
      create: (_) => CashFlowAnalysisBloc(market: widget.market)..loadData(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            VPMarketInfoLocalize.current.cashFlowAnalysis,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ),

          const SizedBox(height: 8),

          BlocBuilder<CashFlowAnalysisBloc, CashFlowAnalysisState>(
            builder:
                (context, state) => ListOptionExpand(
                  listItem: listItem,
                  selected: state.timeFrame,
                  voidCallBack: (item) {
                    context
                        .read<CashFlowAnalysisBloc>()
                        .selectTimeTransactionStatistic(item);
                  },
                  enable: !state.status.isLoading,
                ),
          ),

          const SizedBox(height: 12),

          BlocBuilder<CashFlowAnalysisBloc, CashFlowAnalysisState>(
            builder: (context, state) {
              if (state.status.isLoading) {
                return const ChartLoadingWidget();
              }

              if (state.charts.isEmpty) {
                return NoneWidget(
                  desc: VPMarketInfoLocalize.current.noneTransaction,
                  image: VpMarketAssets.icons.imNoContent.path,
                  package: VpMarketAssets.package,
                  padding: EdgeInsets.zero,
                  isSvgImage: true,
                );
              }

              return Row(
                children: [
                  SizedBox(
                    height: size,
                    width: size,
                    child: Center(
                      child: SfCircularChart(
                        margin: EdgeInsets.zero,
                        series: <CircularSeries>[
                          DoughnutSeries<TransactionStatisticChart, String>(
                            animationDuration: 2000,
                            pointColorMapper:
                                (TransactionStatisticChart data, _) =>
                                    data.color,
                            strokeColor: themeData.white,
                            strokeWidth: 0.5,
                            dataSource: state.charts,
                            xValueMapper:
                                (TransactionStatisticChart data, _) =>
                                    data.name,
                            yValueMapper:
                                (TransactionStatisticChart data, _) =>
                                    data.value,
                            cornerStyle: CornerStyle.bothFlat,
                            radius: '100%',
                            innerRadius: '65%',
                          ),
                        ],
                        annotations: [
                          CircularChartAnnotation(
                            widget: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  VPMarketInfoLocalize.current.cashFlowIn,
                                  style: vpTextStyle.captionRegular?.copyWith(
                                    color: themeData.gray700,
                                  ),
                                ),
                                Text(
                                  state.timeFrame.title,
                                  style: vpTextStyle.subtitle14?.copyWith(
                                    color: themeData.gray900,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: state.charts.length,
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemBuilder:
                          (context, index) => ItemLegend(
                            data: state.charts[index],
                            index: index,
                          ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

class ItemLegend extends StatelessWidget {
  const ItemLegend({super.key, required this.data, required this.index});

  final TransactionStatisticChart data;
  final int index;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: index % 3 == 0,
          child: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              index == 0
                  ? VPMarketInfoLocalize.current.proactiveBuy
                  : VPMarketInfoLocalize.current.proactiveSell,
              style: vpTextStyle.captionMedium?.copyWith(
                color: themeData.gray900,
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 3),
                child: SizedBox(
                  width: 8,
                  height: 8,
                  child: CircleAvatar(radius: 8, backgroundColor: data.color),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  data.name ?? '',
                  style: vpTextStyle.captionRegular?.copyWith(
                    color: themeData.gray900,
                  ),
                  textAlign: TextAlign.start,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                data.value.getMarketChangePercentDisplay(),
                style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
