import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_common/extensions/date_extensions.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/market_liquidity_model.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/liquidity/legend_liquidity_widget.dart';
import 'package:vp_market/screen/overview/liquidity/market_liquidity_bloc.dart';
import 'package:vp_market/screen/overview/liquidity/market_liquidity_state.dart';
import 'package:vp_market/screen/overview/liquidity/option_session_bottom_sheet.dart';
import 'package:vp_market/screen/overview/liquidity/type_session.dart';
import 'package:vp_market/screen/widget/trackball_behavior.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'liquidity_track_ball_view.dart';

class LiquidityWidget extends StatefulWidget {
  const LiquidityWidget({required this.market, super.key});

  final MarketInfoModel market;

  @override
  State<LiquidityWidget> createState() => _LiquidityWidgetState();
}

class _LiquidityWidgetState extends State<LiquidityWidget> {
  MarketInfoModel get market => widget.market;

  late TrackballBehavior _trackballBehavior;

  final DateTime now = DateTime.now();

  late DateTime min = DateTime(now.year, now.month, now.day, 9, 0);

  late DateTime max = DateTime(now.year, now.month, now.day, 15, 0);

  @override
  void initState() {
    super.initState();

    initTrackBall();
  }

  void initTrackBall() {
    _trackballBehavior = trackballBehavior(
      builder: (BuildContext context, TrackballDetails trackballDetails) {
        late DateTime time;

        if (trackballDetails.groupingModeInfo != null) {
          time = trackballDetails.groupingModeInfo?.points[0].x;
        }

        return time.inSession()
            ? LiquidityTrackBallView(time: time)
            : const SizedBox();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MarketLiquidityBloc>(
      create: (_) => MarketLiquidityBloc(market: market)..loadData(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  VPMarketInfoLocalize.current.marketLiquidity,
                  style: vpTextStyle.subtitle16?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              BlocBuilder<MarketLiquidityBloc, MarketLiquidityState>(
                builder:
                    (context, state) => GestureDetector(
                      onTap: () {
                        if (!state.apiStatus.isLoading) {
                          onChangeSession(context);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(8, 4, 8, 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(40),
                          color: themeData.highlightBg,
                        ),
                        child: Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 2),
                              child: Text(
                                '${state.typeSession.value} ${VPMarketInfoLocalize.current.session}',
                                style: vpTextStyle.captionRegular?.copyWith(
                                  color: vpColor.textPrimary,
                                ),
                              ),
                            ),

                            const SizedBox(width: 16),

                            VpMarketAssets.icons.arrowDrop.svg(
                              colorFilter: ColorFilter.mode(
                                themeData.black,
                                BlendMode.srcIn,
                              ),
                              width: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 280,
            child: BlocBuilder<MarketLiquidityBloc, MarketLiquidityState>(
              builder: (context, state) {
                return SfCartesianChart(
                  trackballBehavior: _trackballBehavior,
                  margin: const EdgeInsets.only(left: 2),
                  series: <CartesianSeries>[
                    StackedAreaSeries<MarketLiquidityModel, DateTime>(
                      dataSource: state.listToday,
                      xValueMapper:
                          (MarketLiquidityModel data, _) => data.dateTime,
                      yValueMapper:
                          (MarketLiquidityModel data, _) => data.totalValueB,
                      color: themeData.greenChart.withOpacity(0.16),
                      borderWidth: 2,
                      borderColor: themeData.greenChart,
                    ),
                    LineSeries<MarketLiquidityModel, DateTime>(
                      dataSource: state.listYesterday,
                      xValueMapper:
                          (MarketLiquidityModel data, _) => data.dateTime,
                      yValueMapper:
                          (MarketLiquidityModel data, _) => data.totalValueB,
                      pointColorMapper:
                          (MarketLiquidityModel data, _) => themeData.redChart,
                      color: themeData.redChart,
                      width: 2,
                    ),
                    LineSeries<MarketLiquidityModel, DateTime>(
                      dataSource: state.listSession,
                      xValueMapper:
                          (MarketLiquidityModel data, _) => data.dateTime,
                      yValueMapper:
                          (MarketLiquidityModel data, _) => data.totalValueB,
                      pointColorMapper:
                          (MarketLiquidityModel data, _) =>
                              themeData.yellowChart,
                      color: themeData.yellowChart,
                      width: 2,
                    ),
                  ],
                  primaryXAxis: DateTimeAxis(
                    isVisible: true,
                    edgeLabelPlacement: EdgeLabelPlacement.shift,
                    majorGridLines: const MajorGridLines(
                      width: 1,
                      color: Colors.transparent,
                    ),
                    dateFormat: DateFormat.Hm(),
                    rangePadding: ChartRangePadding.additional,
                    minimum: min,
                    maximum: max,
                    maximumLabels: 7,
                    placeLabelsNearAxisLine: false,
                  ),
                  primaryYAxis: NumericAxis(
                    isVisible: true,
                    axisLine: const AxisLine(
                      color: Colors.transparent,
                      width: 1,
                    ),
                    majorTickLines: const MajorTickLines(
                      width: 0,
                      color: Colors.transparent,
                    ),
                    minimum: 0,
                    numberFormat: AppNumberFormatUtils.shared.currencyFormatter,
                    rangePadding: ChartRangePadding.round,
                    placeLabelsNearAxisLine: false,
                  ),
                  plotAreaBorderWidth: 0,
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          const LegendLiquidityWidget(),
        ],
      ),
    );
  }

  void onChangeSession(BuildContext context) async {
    final result = await showOptionSessionBottomSheet(context, listTypeSession);

    if (result == null || !mounted) return;

    context.read<MarketLiquidityBloc>().selectSession(result);
  }
}
