import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/data/market_liquidity_model.dart';
import 'package:vp_market/screen/overview/liquidity/market_liquidity_state.dart';
import 'package:vp_market/screen/overview/liquidity/type_session.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class MarketLiquidityBloc extends Cubit<MarketLiquidityState> {
  MarketLiquidityBloc({required MarketInfoModel market})
    : super(
        MarketLiquidityState(
          apiStatus: ApiStatus.initial(),
          typeSession: listTypeSession[0],
          market: market,
        ),
      );

  final _repository = GetIt.instance<MarketRepository>();

  void reset() {}

  ///chọn phiên thanh khoản
  void selectSession(ItemSelect? value) {
    if (value != null && value.id != state.typeSession.id) {
      emit(state.copyWith(typeSession: value));
      loadData();
    }
  }

  ///thanh khoản thị trường
  Future loadData() async {
    try {
      emit(state.copyWith(status: ApiStatus.loading(), listSession: []));

      final values = await Future.wait([
        _getDataMarketLiquidityToday(),
        _getDataMarketLiquidityYesterday(),
        _getDataMarketLiquiditySession(),
      ]);

      emit(
        state.copyWith(
          status: ApiStatus.done(),
          listToday: values.getElementAt(0),
          listYesterday: values.getElementAt(1),
          listSession: values.getElementAt(2),
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }

  Future<List<MarketLiquidityModel>> _getDataMarketLiquidityToday() async {
    return _getApiMarketLiquidity(TypeSessionEnum.nowSession.name);
  }

  Future<List<MarketLiquidityModel>?> _getDataMarketLiquidityYesterday() async {
    final listYesterday = state.listYesterday;

    if (listYesterday.isEmpty) {
      return _getApiMarketLiquidity(TypeSessionEnum.preSessions.name);
    }

    return null;
  }

  Future<List<MarketLiquidityModel>> _getDataMarketLiquiditySession() async {
    return _getApiMarketLiquidity(state.typeSession.id);
  }

  Future<List<MarketLiquidityModel>> _getApiMarketLiquidity(
    String typeSession,
  ) {
    return _repository.getMarketLiquidity(
      marketCode: state.market.indexCode.marketCode,
      type: typeSession,
    );
  }
}
