import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/data/market_liquidity_model.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class MarketLiquidityState {
  final ApiStatus apiStatus;

  final ItemSelect typeSession;

  final MarketInfoModel market;

  final List<MarketLiquidityModel> listToday;
  final List<MarketLiquidityModel> listYesterday;
  final List<MarketLiquidityModel> listSession;

  MarketLiquidityState({
    required this.apiStatus,
    required this.typeSession,
    required this.market,
    this.listToday = const [],
    this.listYesterday = const [],
    this.listSession = const [],
  });

  MarketLiquidityState copyWith({
    ApiStatus? status,
    ItemSelect? typeSession,
    MarketInfoModel? market,
    List<MarketLiquidityModel>? listToday,
    List<MarketLiquidityModel>? listYesterday,
    List<MarketLiquidityModel>? listSession,
  }) {
    return MarketLiquidityState(
      apiStatus: status ?? apiStatus,
      market: market ?? this.market,
      typeSession: typeSession ?? this.typeSession,
      listToday: listToday ?? this.listToday,
      listYesterday: listYesterday ?? this.listYesterday,
      listSession: listSession ?? this.listSession,
    );
  }
}
