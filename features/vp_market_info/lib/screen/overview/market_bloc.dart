import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'market_state.dart';

class MarketBloc extends Cubit<MarketState> {
  MarketBloc() : super(MarketState());

  void onMarketChanged(MarketInfoModel market) {
    emit(state.copyWith(market: market));
  }

  switchChartType(bool isPriceChart) {}

  String getTradingViewUrl() {
    // return stockConfig.tradingWebviewMarket(
    //   state.market?.marketSymbol ?? '',
    //   theme: state.theme,
    //   embeddedIframe: true,
    // );

    return '';
  }
}
