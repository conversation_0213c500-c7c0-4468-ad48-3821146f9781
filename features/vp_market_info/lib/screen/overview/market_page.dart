import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/cashflow_analysis/cashflow_analysis_widget.dart';
import 'package:vp_market/screen/overview/liquidity/liquidity_widget.dart';
import 'package:vp_market/screen/overview/market_bloc.dart';
import 'package:vp_market/screen/overview/supply_demand/supply_demand_widget.dart';
import 'package:vp_market/screen/overview/transaction_classification/transaction_classification_widget.dart';
import 'package:vp_market/screen/overview/volatility/volatility_widget.dart';
import 'package:vp_market/screen/widget/app_lifecycle_listener.dart';
import 'package:vp_market/screen/widget/index_chart.dart';
import 'package:vp_market/screen/widget/market_chart_header_widget.dart';
import 'package:vp_market/screen/widget/market_loading.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class MarketPage extends StatefulWidget {
  const MarketPage({super.key, this.indexCode});

  final IndexCode? indexCode;

  @override
  State<MarketPage> createState() => _MarketPageState();
}

class _MarketPageState extends AppLifeCycleListener<MarketPage>
    with AutomaticKeepAliveClientMixin<MarketPage> {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider<MarketBloc>(
      create: (_) => MarketBloc(),
      child: BlocListener<MarketBloc, MarketState>(
        listener: (context, state) {},
        child: Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                HeaderWidget(
                  subTitle: VPMarketInfoLocalize.current.stock,
                  title: VPMarketInfoLocalize.current.marketInfo,
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      spacing: 16,
                      children: [
                        Builder(
                          builder: (context) {
                            return MarketIndexList(
                              onChanged:
                                  (market) => context
                                      .read<MarketBloc>()
                                      .onMarketChanged(market),
                              padding: EdgeInsets.zero,
                              indexCode: widget.indexCode,
                              primaryBuilder:
                                  (market) => MarketIndexNoChartView(
                                    marketInfo: market,
                                  ),
                              enableSelection: true,
                            );
                          },
                        ),

                        const MarketBodyView(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class MarketBodyView extends StatelessWidget {
  const MarketBodyView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MarketBloc, MarketState>(
      buildWhen: (p, c) => p.market != c.market,
      builder: (context, state) {
        final market = state.market;

        if (market == null) {
          return const MarketLoading();
        }

        return Column(
          spacing: 16,
          key: ValueKey(market.indexCode),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarketChartHeaderWidget(market: market),
            const IndexChart(),
            VolatilityWidget(market: market),
            LiquidityWidget(market: market),
            SupplyDemandWidget(market: market),
            CashFlowAnalysisWidget(market: market),
            TransactionClassificationWidget(market: market),
          ],
        );
      },
    );
  }
}
