

import 'package:vp_market/data/market_entity.dart';

class MarketUtils {
  // MarketEntity? updateMarketValue(IMarketInfoData data, MarketEntity? market) {
  //   bool needUpdate = false;
  //
  //   final currentData = market;
  //
  //   if (currentData == null) return null;
  //
  //   final advances = data.advances?.toInt();
  //   final declines = data.declines?.toInt();
  //   final noChange = data.noChange?.toInt();
  //   final numOfFloor = data.numOfFloor?.toInt();
  //   final numOfCeiling = data.numOfCeiling?.toInt();
  //   final marketStatus = data.marketStatus;
  //   final oddLotTotalValue = data.oddLotTotalValue;
  //   final oddLotTotalVolume = data.oddLotTotalVolume;
  //
  //   if (advances != null && currentData.advances != advances) {
  //     needUpdate = true;
  //     currentData.advances = advances;
  //   }
  //   if (declines != null && currentData.declines != declines) {
  //     needUpdate = true;
  //     currentData.declines = declines;
  //   }
  //   if (noChange != null && currentData.noChange != noChange) {
  //     needUpdate = true;
  //     currentData.noChange = noChange;
  //   }
  //   if (numOfFloor != null && currentData.numberOfFl != numOfFloor) {
  //     needUpdate = true;
  //     currentData.numberOfFl = numOfFloor;
  //   }
  //   if (numOfCeiling != null && currentData.numberOfCe != numOfCeiling) {
  //     needUpdate = true;
  //     currentData.numberOfCe = numOfCeiling;
  //   }
  //   if (marketStatus != null && currentData.marketStatus != marketStatus) {
  //     needUpdate = true;
  //     currentData.marketStatus = marketStatus;
  //   }
  //   if (oddLotTotalValue != null &&
  //       currentData.oddLotTotalValue != oddLotTotalValue) {
  //     needUpdate = true;
  //     currentData.oddLotTotalValue = oddLotTotalValue;
  //   }
  //   if (oddLotTotalVolume != null &&
  //       currentData.oddLotTotalVolume != oddLotTotalVolume) {
  //     needUpdate = true;
  //     currentData.oddLotTotalVolume = oddLotTotalVolume;
  //   }
  //
  //   if (needUpdate) {
  //     return currentData;
  //   }
  //   return null;
  // }
}
