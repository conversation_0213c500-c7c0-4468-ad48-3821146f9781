import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/data/supply_demand_model.dart';
import 'package:vp_market/screen/overview/supply_demand/supply_demand_state.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class SupplyDemandBloc extends Cubit<SupplyDemandState>
    with IntradaySupplyDemandChartSocketMixin {
  SupplyDemandBloc({required MarketInfoModel market})
    : super(SupplyDemandState(status: ApiStatus.initial(), market: market));

  final _repository = GetIt.instance<MarketRepository>();

  Future loadData() async {
    try {
      emit(state.copyWith(status: ApiStatus.loading()));

      final value = await _repository.getIntradaySupplyDemandChart(
        marketCode: state.market.indexCode.marketCode,
      );

      subscribeIntradaySupplyDemandChartData({
        state.market.indexCode.socketChannel,
      });

      emit(state.copyWith(status: ApiStatus.done(), list: value));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }

  @override
  void onSocketIntradaySupplyDemandChartListener(
    VPIntradaySupplyDemandChartData data,
  ) {
    final model = SupplyDemandModel(
      timeStamp: data.timeStamp,
      sumBuyUpValue: data.sumBuyUpValue,
      sumSellDownValue: data.sumSellDownValue,
      sumMarketValue: data.sumMarketValue,
      rateSumBuyUpValue: data.rateSumBuyUpValue,
      rateSumSellDownValue: data.rateSumSellDownValue,
      buyUpOnSellDown: data.buyUpOnSellDown,
    );
    List<SupplyDemandModel> charts = state.list;

    if (charts.lastOrNull?.timeStamp != data.timeStamp) {
      charts.add(model);
      emit(state.copyWith(list: charts));
    }
  }

  @override
  Future<void> close() {
    unsubscribeIntradaySupplyDemandChartData();
    return super.close();
  }
}
