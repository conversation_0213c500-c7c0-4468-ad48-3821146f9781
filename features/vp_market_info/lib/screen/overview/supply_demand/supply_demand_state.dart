import 'package:vp_core/vp_core.dart';
import 'package:vp_market/data/supply_demand_model.dart';
import 'package:vp_market/screen/overview/supply_demand/supply_demand_utils.dart';
import 'package:vp_stock_common/vp_stock_common.dart' hide ChartData;

class SupplyDemandState {
  final ApiStatus status;

  final MarketInfoModel market;

  final List<SupplyDemandModel> list;

  SupplyDemandState({
    required this.status,
    required this.market,
    this.list = const [],
  });

  SupplyDemandState copyWith({
    ApiStatus? status,
    MarketInfoModel? market,
    List<SupplyDemandModel>? list,
  }) {
    return SupplyDemandState(
      status: status ?? this.status,
      list: list ?? this.list,
      market: market ?? this.market,
    );
  }

  double getMax() {
    num max = 0;

    if (list.isNotEmpty) {
      final maxBuyObj = list.reduce((a, b) => a.yValue > b.yValue ? a : b);
      max = maxBuyObj.yValue;
    }

    return (max + 0.5).toDouble();
  }

  double get getMin => getMax() * (-1);

  bool get typeChart =>
      list.isEmpty
          ? false
          : (list[0].dateTime.hour == 9 && list[0].dateTime.minute == 15);

  List<ChartData> get charts2 => SupplyDemandUtils().convertChartData(list);
}
