import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/data/supply_demand_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/supply_demand/supply_demand_bloc.dart';
import 'package:vp_market/screen/overview/supply_demand/supply_demand_state.dart';
import 'package:vp_market/screen/overview/supply_demand/supply_demand_track_ball_view.dart';
import 'package:vp_market/screen/overview/supply_demand/supply_demand_utils.dart';
import 'package:vp_market/screen/widget/axis_y_chart_widget.dart';
import 'package:vp_market/screen/widget/chart_loading_widget.dart';
import 'package:vp_market/screen/widget/title_axis_y_chart_widget.dart';
import 'package:vp_market/screen/widget/trackball_behavior.dart';
import 'package:vp_stock_common/vp_stock_common.dart' hide ChartData;

class SupplyDemandWidget extends StatefulWidget {
  const SupplyDemandWidget({required this.market, super.key});

  final MarketInfoModel market;

  @override
  State<SupplyDemandWidget> createState() => _SupplyDemandWidgetState();
}

class _SupplyDemandWidgetState extends State<SupplyDemandWidget> {
  TrackballBehavior initTrackBall(List<SupplyDemandModel> list) {
    return trackballBehavior(
      builder: (context, trackballDetails) {
        num? yValue = 0;
        if (trackballDetails.groupingModeInfo != null) {
          yValue = trackballDetails.groupingModeInfo?.points[0].y;
        }
        final model = list.where((e) => e.yValue == yValue).firstOrNull;

        return model != null
            ? SupplyDemandTrackBallView(model: model)
            : const SizedBox.shrink();
      },
    );
  }

  TrackballBehavior? behavior;

  @override
  Widget build(BuildContext context) {
    const double height = 280;
    final buyTitle = '${VPMarketInfoLocalize.current.buy} CĐ';
    final sellTitle = '${VPMarketInfoLocalize.current.sell} CĐ';

    return BlocProvider<SupplyDemandBloc>(
      create: (_) => SupplyDemandBloc(market: widget.market)..loadData(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            VPMarketInfoLocalize.current.supplyDemand,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ),

          const SizedBox(height: 8),

          BlocBuilder<SupplyDemandBloc, SupplyDemandState>(
            builder: (context, state) {
              if (state.status.isLoading) {
                return const ChartLoadingWidget();
              }

              behavior ??= initTrackBall(state.list);

              return SizedBox(
                height: height,
                child: Row(
                  children: [
                    TitleAxisYChartWidget(
                      buyTitle: buyTitle,
                      sellTitle: sellTitle,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Stack(
                        children: [
                          const AxisYChartWidget(height: height),
                          const Center(child: VPDividerView()),
                          SfCartesianChart(
                            trackballBehavior: behavior,
                            margin: const EdgeInsets.only(left: 2),
                            series: <CartesianSeries>[
                              SplineSeries<ChartData, num>(
                                dataSource: state.charts2,
                                splineType: SplineType.monotonic,
                                xValueMapper: (ChartData data, _) => data.x,
                                yValueMapper: (ChartData data, _) => data.y,
                                pointColorMapper:
                                    (ChartData data, _) => data.color,
                              ),
                            ],
                            primaryXAxis: const NumericAxis(
                              isVisible: false,
                              maximum: 60,
                              minimum: 0,
                            ),
                            primaryYAxis: NumericAxis(
                              isVisible: false,
                              maximum: state.getMax(),
                              minimum: state.getMin,
                            ),
                            plotAreaBorderWidth: 0,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
