import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/data/chart_buy_up_sell_entity.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class TransactionClassificationState {
  final ItemSelect typeTransaction;

  final ChartBuyUpSellEntity chartEntity;

  final ApiStatus status;

  final MarketInfoModel market;

  TransactionClassificationState({
    required this.status,
    required this.chartEntity,
    required this.market,
    required this.typeTransaction,
  });

  TransactionClassificationState copyWith({
    MarketInfoModel? market,
    ItemSelect? typeTransaction,
    ChartBuyUpSellEntity? chartEntity,
    ApiStatus? status,
  }) {
    return TransactionClassificationState(
      typeTransaction: typeTransaction ?? this.typeTransaction,
      chartEntity: chartEntity ?? this.chartEntity,
      market: market ?? this.market,
      status: status ?? this.status,
    );
  }

  num getMaxMin() {
    switch (typeTransaction.id) {
      case ChartBuyUpSellType.small:
        return chartEntity.getMaxMinCharts([
          chartEntity.sChartDataBuy,
          chartEntity.sChartDataSell,
        ]);
      case ChartBuyUpSellType.medium:
        return chartEntity.getMaxMinCharts([
          chartEntity.mChartDataBuy,
          chartEntity.mChartDataSell,
        ]);
      case ChartBuyUpSellType.long:
        return chartEntity.getMaxMinCharts([
          chartEntity.lChartDataBuy,
          chartEntity.lChartDataSell,
        ]);
      default:
        return chartEntity.getMaxMinCharts([
          chartEntity.lChartDataBuy,
          chartEntity.lChartDataSell,
          chartEntity.mChartDataBuy,
          chartEntity.mChartDataSell,
          chartEntity.sChartDataBuy,
          chartEntity.sChartDataSell,
        ]);
    }
  }
}
