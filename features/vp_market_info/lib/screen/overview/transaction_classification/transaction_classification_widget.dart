import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/data/chart_buy_up_sell_entity.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/transaction_classification/transaction_classification_bloc.dart';
import 'package:vp_market/screen/overview/transaction_classification/transaction_classification_chart.dart';
import 'package:vp_market/screen/overview/transaction_classification/transaction_classification_state.dart';
import 'package:vp_market/screen/widget/chart_loading_widget.dart';
import 'package:vp_market/screen/widget/list_option_expand.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

List<ItemSelect> listItem = [
  ItemSelect(id: null, title: VPMarketInfoLocalize.current.all),
  ItemSelect(
    id: ChartBuyUpSellType.long,
    title: VPMarketInfoLocalize.current.largeOrder,
  ),
  ItemSelect(
    id: ChartBuyUpSellType.medium,
    title: VPMarketInfoLocalize.current.mediumOrder,
  ),
  ItemSelect(
    id: ChartBuyUpSellType.small,
    title: VPMarketInfoLocalize.current.smallOrder,
  ),
];

class TransactionClassificationWidget extends StatelessWidget {
  const TransactionClassificationWidget({required this.market, super.key});

  final MarketInfoModel market;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TransactionClassificationBloc>(
      create: (_) => TransactionClassificationBloc(market: market)..loadData(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            VPMarketInfoLocalize.current.classify,
            style: vpTextStyle.subtitle16?.copyWith(color: themeData.black),
          ),
          const SizedBox(height: 8),
          BlocBuilder<
            TransactionClassificationBloc,
            TransactionClassificationState
          >(
            buildWhen: (p, c) => p.typeTransaction != c.typeTransaction,
            builder: (context, state) {
              return ListOptionExpand(
                listItem: listItem,
                selected: state.typeTransaction,
                voidCallBack: (item) {
                  context
                      .read<TransactionClassificationBloc>()
                      .selectTypeTransactionClassification(item);
                },
                // enable: state.transactionState.status != StatusEnum.loading,
              );
            },
          ),
          const SizedBox(height: 16),
          BlocBuilder<
            TransactionClassificationBloc,
            TransactionClassificationState
          >(
            builder: (context, state) {
              if (state.status.isLoading) {
                return const ChartLoadingWidget();
              } else {
                return const TransactionClassificationChart();
              }
            },
          ),
        ],
      ),
    );
  }
}
