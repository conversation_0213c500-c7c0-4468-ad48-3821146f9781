import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/screen/overview/volatility/volatility_state.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class VolatilityBloc extends Cubit<VolatilityState> {
  VolatilityBloc({required MarketInfoModel market})
    : super(VolatilityState(market: market, status: ApiStatus.initial()));

  final _repository = GetIt.instance<MarketRepository>();

  Future loadData() async {
    try {
      emit(state.copyWith(status: ApiStatus.loading()));

      final data = await _repository.getMarketVolatility(
        state.market.indexCode.marketCode,
      );

      emit(state.copyWith(status: ApiStatus.done(), entity: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }
}
