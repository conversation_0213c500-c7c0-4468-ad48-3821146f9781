import 'package:vp_core/vp_core.dart';
import 'package:vp_market/data/market_volatility_entity.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class VolatilityState {
  final ApiStatus status;

  final MarketVolatilityEntity? entity;

  final MarketInfoModel market;

  VolatilityState({required this.status, required this.market, this.entity});

  VolatilityState copyWith({
    ApiStatus? status,
    MarketInfoModel? market,
    MarketVolatilityEntity? entity,
  }) {
    return VolatilityState(
      status: status ?? this.status,
      market: market ?? this.market,
      entity: entity ?? this.entity,
    );
  }
}
