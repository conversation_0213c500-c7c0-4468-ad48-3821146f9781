import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/core/const/market_data_default.dart';
import 'package:vp_market/data/market_volatility_entity.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/volatility/volatility_bloc.dart';
import 'package:vp_market/screen/overview/volatility/volatility_state.dart';
import 'package:vp_market/screen/widget/chart_loading_widget.dart';
import 'package:vp_market/screen/widget/horizontal_volatility_chart.dart';
import 'package:vp_market/screen/widget/market_pillar.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class VolatilityWidget extends StatefulWidget {
  const VolatilityWidget({required this.market, super.key});

  final MarketInfoModel market;

  @override
  State<VolatilityWidget> createState() => _VolatilityWidgetState();
}

class _VolatilityWidgetState extends State<VolatilityWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider<VolatilityBloc>(
      create: (_) => VolatilityBloc(market: widget.market)..loadData(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            VPMarketInfoLocalize.current.marketStockVolatility,
            style: vpTextStyle.subtitle16?.copyWith(color: themeData.black),
          ),
          const SizedBox(height: 16),
          BlocBuilder<VolatilityBloc, VolatilityState>(
            builder: (context, state) {
              final data = state.entity;

              if (state.status.isLoading) {
                return const ChartLoadingWidget(height: 200);
              }

              if (state.status.isError || data == null) {
                return buildMarketVolatility(
                  MarketDataDefault.getVolatilityEntityDefault(),
                  isDefault: true,
                );
              }

              int? advancesHeight;
              int? noChangeHeight;
              int? declinesHeight;
              int total;
              int totalReduce = data.declines;
              int totalIncrease = data.advances;

              final listAdvances = [
                data.countStockPerchangeLevel1,
                data.countStockPerchangeLevel2,
                data.countStockPerchangeLevel3,
                data.countStockPerchangeLevel4,
                data.countStockPerchangeLevel5,
              ];

              final listDeclines = [
                data.countStockPerchangeLevel6,
                data.countStockPerchangeLevel7,
                data.countStockPerchangeLevel8,
                data.countStockPerchangeLevel9,
                data.countStockPerchangeLevel10,
              ];
              advancesHeight = (listAdvances).length;
              noChangeHeight = ([data.countStockPerchangeLevel0]).length;
              declinesHeight = (listDeclines).length;
              total = (advancesHeight) + (noChangeHeight) + (declinesHeight);

              return buildMarketVolatility(
                data,
                advancesHeight: advancesHeight,
                noChangeHeight: noChangeHeight,
                declinesHeight: declinesHeight,
                total: total,
                totalReduce: totalReduce,
                totalIncrease: totalIncrease,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget buildMarketVolatility(
    MarketVolatilityEntity data, {
    int? advancesHeight,
    int? noChangeHeight,
    int? declinesHeight,
    int? total,
    int? totalReduce,
    int? totalIncrease,
    bool isDefault = false,
  }) {
    return SizedBox(
      height: 200,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: HorizontalVolatilityChart(data: data, isDefault: isDefault),
          ),
          const SizedBox(height: 16),
          MarketPillar(
            data: data,
            advances: advancesHeight ?? 5,
            noChanges: noChangeHeight ?? 1,
            declines: declinesHeight ?? 5,
            total: total ?? 11,
            totalReduce: totalReduce ?? 0,
            totalIncrease: totalIncrease ?? 0,
          ),
        ],
      ),
    );
  }
}
