// Sàn giao dịch
enum TopFeaturedStocksExchange {
  all('Tất cả'),
  hose('HOSE'),
  hnx('HNX'),
  upcom('UPCOM');

  final String label;

  String get floorCode {
    switch (this) {
      case all:
        return 'ALL';
      case hose:
        return '10';
      case hnx:
        return '02';
      case upcom:
        return '04';
    }
  }

  const TopFeaturedStocksExchange(this.label);
}

// Tiêu chí lọc
enum TopFeaturedStocksCriteria {
  klgdTb('KLGD TB'),
  gtgdTb('GTGD TB'),
  percentUp('% tăng'),
  percentDown('% giảm');

  final String label;

  const TopFeaturedStocksCriteria(this.label);
}

// Thời gian
enum TopFeaturedStocksTime {
  oneDay('1D'),
  oneWeek('1W'),
  oneMonth('1M'),
  threeMonths('3M'),
  oneYear('1Y');

  final String label;

  String get chartTypeWithTime {
    switch (this) {
      case oneWeek:
        return '1W';
      case oneMonth:
        return '1M';
      case threeMonths:
        return '3M';
      case oneYear:
        return '1Y';
      case oneDay:
        return '1D';
    }
  }

  const TopFeaturedStocksTime(this.label);
}
