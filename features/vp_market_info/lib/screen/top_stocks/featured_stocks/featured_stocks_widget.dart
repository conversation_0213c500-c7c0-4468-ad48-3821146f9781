import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/filter/stock_filter_row.dart';
import 'package:vp_market/screen/top_stocks/enum/top_featured_stocks_enum.dart';
import 'package:vp_market/screen/top_stocks/featured_stocks/top_featured_stocks_bloc.dart';
import 'package:vp_market/screen/top_stocks/featured_stocks/top_featured_stocks_state.dart';
import 'package:vp_market/screen/widget/home_stock_loading.dart';
import 'package:vp_market/screen/widget/market_top_stock_list/market_top_stock_list.dart';

class FeaturedStocksWidget extends StatelessWidget {
  const FeaturedStocksWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TopFeaturedStocksBloc>(
      create: (_) => TopFeaturedStocksBloc()..loadData(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            VPMarketInfoLocalize.current.featuredStocks,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ),
          const SizedBox(height: 8),
          Builder(
            builder: (context) {
              return FilterRow<TopFeaturedStocksExchange>(
                initValue: TopFeaturedStocksExchange.all,
                onChanged: (item) {
                  context.read<TopFeaturedStocksBloc>().updateTopTradingMarket(
                    item,
                  );
                },
                title: VPMarketInfoLocalize.current.exchange,
                items: TopFeaturedStocksExchange.values,
                builder: (item) => item.label,
              );
            },
          ),
          const SizedBox(height: 8),
          Builder(
            builder: (context) {
              return FilterRow<TopFeaturedStocksCriteria>(
                initValue: TopFeaturedStocksCriteria.gtgdTb,
                onChanged: (item) {
                  context
                      .read<TopFeaturedStocksBloc>()
                      .updateTopTradingCriteria(item);
                },
                title: VPMarketInfoLocalize.current.criteria,
                items: TopFeaturedStocksCriteria.values,
                builder: (item) => item.label,
              );
            },
          ),
          const SizedBox(height: 8),
          Builder(
            builder: (context) {
              return FilterRow<TopFeaturedStocksTime>(
                initValue: TopFeaturedStocksTime.oneDay,
                builder: (item) => item.label,
                onChanged: (item) {
                  context.read<TopFeaturedStocksBloc>().updateTopTradingTime(
                    item,
                  );
                },
                title: VPMarketInfoLocalize.current.time,
                items: TopFeaturedStocksTime.values,
              );
            },
          ),
          const SizedBox(height: 8),
          BlocBuilder<TopFeaturedStocksBloc, TopFeaturedStocksState>(
            builder: (context, state) {
              if (state.status.isLoading) {
                return HomeStockLoading(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                );
              }

              if (state.status.isError) {
                return const SizedBox.shrink();
              }

              return SizedBox(
                height: 432,
                child: MarketTopStockList(
                  isToday: state.selectedTime == TopFeaturedStocksTime.oneDay,
                  displayMode: state.displayMode,
                  data: state.data,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
