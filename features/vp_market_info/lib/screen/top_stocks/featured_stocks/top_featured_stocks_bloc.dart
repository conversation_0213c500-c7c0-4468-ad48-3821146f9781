import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/data/market_top_trading_entity.dart';
import 'package:vp_market/data/stock_with_chart_entity.dart';
import 'package:vp_market/screen/top_stocks/enum/top_featured_stocks_enum.dart';
import 'package:vp_market/screen/top_stocks/featured_stocks/top_featured_stocks_state.dart';

class TopFeaturedStocksBloc extends Cubit<TopFeaturedStocksState> {
  TopFeaturedStocksBloc()
    : super(TopFeaturedStocksState(status: ApiStatus.initial()));

  final _repository = GetIt.instance<MarketRepository>();

  MarketTopTradingEntity? _cachedTopTradingData;

  Future loadData() => _loadMarketTopTrading();

  List<StockTopTradingEntity> _getTopTradingList() {
    switch (state.selectedCriteria) {
      case TopFeaturedStocksCriteria.gtgdTb:
        return _cachedTopTradingData?.topValue ?? [];
      case TopFeaturedStocksCriteria.klgdTb:
        return _cachedTopTradingData?.topVolume ?? [];
      case TopFeaturedStocksCriteria.percentUp:
        return _cachedTopTradingData?.topPercentUp ?? [];
      case TopFeaturedStocksCriteria.percentDown:
        return _cachedTopTradingData?.topPercentDown ?? [];
    }
  }

  void updateTopTradingMarket(TopFeaturedStocksExchange exchange) {
    emit(state.copyWith(selectedTopTradingMarket: exchange));
    _loadMarketTopTrading();
  }

  void updateTopTradingCriteria(TopFeaturedStocksCriteria criteria) {
    emit(state.copyWith(selectedCriteria: criteria));
    _updateMarketTopTrading();
  }

  void updateTopTradingTime(TopFeaturedStocksTime time) {
    emit(state.copyWith(selectedTime: time));
    _loadMarketTopTrading();
  }

  void _updateMarketTopTrading() async {
    try {
      emit(state.copyWith(data: [], status: ApiStatus.loading()));

      final data = await _loadStockDataWithTopTradingList(_getTopTradingList());

      emit(state.copyWith(data: data, status: ApiStatus.done()));
    } catch (e) {
      emit(state.copyWith(data: [], status: ApiStatus.error(e)));
    }
  }

  Future _loadMarketTopTrading() async {
    try {
      emit(state.copyWith(data: [], status: ApiStatus.loading()));

      _cachedTopTradingData =
          state.selectedTime == TopFeaturedStocksTime.oneDay
              ? await _repository.getMarketTopTradingDay(
                state.selectedTopTradingMarket.floorCode,
              )
              : await _repository.getMarketTopTradingHistory(
                state.selectedTopTradingMarket.floorCode,
                state.selectedTime.chartTypeWithTime,
              );

      final data = await _loadStockDataWithTopTradingList(_getTopTradingList());

      emit(state.copyWith(data: data, status: ApiStatus.done()));
    } catch (e) {
      emit(state.copyWith(data: [], status: ApiStatus.error(e)));
      showError(e);
    }
  }

  Future<List<StockWithChartEntity>> _loadStockDataWithTopTradingList(
    List<StockTopTradingEntity> data,
  ) async {
    String symbols = '';
    for (int i = 0; i < data.length; i++) {
      if (i < data.length - 1) {
        symbols += '${data[i].symbol},';
      } else {
        symbols += data[i].symbol;
      }
    }

    final stockDetailEntities = await _repository.getStockList(symbols);

    final list = <StockWithChartEntity>[];
    for (int i = 0; i < data.length; i++) {
      final topTradingItem = data[i];
      final element = stockDetailEntities.findFirstOrNull(
        (element) => element.symbol == topTradingItem.symbol,
      );
      if (element != null) {
        list.add(
          StockWithChartEntity(
            stockDetail: element,
            chartPriceData: [],
            stockTopTrading: topTradingItem,
          ),
        );
      }
    }

    return list;
  }
}
