import 'package:vp_core/vp_core.dart';
import 'package:vp_market/data/market_top_trading_entity.dart';
import 'package:vp_market/data/stock_with_chart_entity.dart';
import 'package:vp_market/screen/top_stocks/enum/top_featured_stocks_enum.dart';

class TopFeaturedStocksState {
  final List<StockWithChartEntity> data;

  final ApiStatus status;

  final TopFeaturedStocksExchange selectedTopTradingMarket;

  final TopFeaturedStocksCriteria selectedCriteria;

  final TopFeaturedStocksTime selectedTime;

  TopTradingDisplayMode get displayMode {
    switch (selectedCriteria) {
      case TopFeaturedStocksCriteria.klgdTb:
        return TopTradingDisplayMode.volume;
      case TopFeaturedStocksCriteria.gtgdTb:
        return TopTradingDisplayMode.tradingValue;
      case TopFeaturedStocksCriteria.percentUp:
        return TopTradingDisplayMode.changePercent;
      case TopFeaturedStocksCriteria.percentDown:
        return TopTradingDisplayMode.changePercent;
    }
  }

  TopFeaturedStocksState({
    List<StockWithChartEntity>? data,
    required this.status,
    this.selectedTopTradingMarket = TopFeaturedStocksExchange.all,
    this.selectedCriteria = TopFeaturedStocksCriteria.gtgdTb,
    this.selectedTime = TopFeaturedStocksTime.oneDay,
  }) : data = data ?? [];

  TopFeaturedStocksState copyWith({
    List<StockWithChartEntity>? data,
    ApiStatus? status,
    TopFeaturedStocksExchange? selectedTopTradingMarket,
    TopFeaturedStocksCriteria? selectedCriteria,
    TopFeaturedStocksTime? selectedTime,
  }) {
    return TopFeaturedStocksState(
      data: data ?? this.data,
      status: status ?? this.status,
      selectedTopTradingMarket:
          selectedTopTradingMarket ?? this.selectedTopTradingMarket,
      selectedCriteria: selectedCriteria ?? this.selectedCriteria,
      selectedTime: selectedTime ?? this.selectedTime,
    );
  }
}
