import 'package:flutter/material.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/top_stocks/featured_stocks/featured_stocks_widget.dart';
import 'package:vp_market/screen/widget/app_lifecycle_listener.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/market_top_foreign_trading.dart';

class TopStockMarketPage extends StatefulWidget {
  const TopStockMarketPage({super.key});

  @override
  State<TopStockMarketPage> createState() => _TopStockMarketPageState();
}

class _TopStockMarketPageState extends AppLifeCycleListener<TopStockMarketPage>
    with AutomaticKeepAliveClientMixin<TopStockMarketPage> {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: Column(
        children: [
          HeaderWidget(
            subTitle: VPMarketInfoLocalize.current.stock,
            title: VPMarketInfoLocalize.current.marketInfo,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: PullToRefreshView(
                onRefresh: () async {},
                child: const SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FeaturedStocksWidget(),
                      MarketTopForeignTrading(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
