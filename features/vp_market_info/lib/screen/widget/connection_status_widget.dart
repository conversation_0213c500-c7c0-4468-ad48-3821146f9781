import 'package:flutter/material.dart';

class ConnectionStatusWidget extends StatefulWidget {
  final Widget? errorWidget;
  final Widget child;

  const ConnectionStatusWidget(
      {Key? key, this.errorWidget, required this.child})
      : super(key: key);

  @override
  State<ConnectionStatusWidget> createState() => _ConnectionStatusWidgetState();
}

class _ConnectionStatusWidgetState extends State<ConnectionStatusWidget> {
  bool? _networkAvailable;

  @override
  void initState() {
    super.initState();
    // ConnectionUtils().addListener(_onConnectionChange);
    _checkNetwork();
  }

  @override
  Widget build(BuildContext context) {
    if (_networkAvailable != null) {
      return SizedBox(
        child: _networkAvailable! ? widget.child : widget.errorWidget,
      );
    }
    return const SizedBox();
  }

  void _onConnectionChange(bool isActive) {
    setState(() {
      _networkAvailable = isActive;
    });
  }

  @override
  void dispose() {
    // ConnectionUtils().removeListener(_onConnectionChange);
    super.dispose();
  }

  Future<void> _checkNetwork() async {
    // _networkAvailable =
    // await ConnectionUtils().checkConnection(defaultValue: false);
    // setState(() {});
  }
}
