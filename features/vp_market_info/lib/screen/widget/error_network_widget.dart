import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/button_widget.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/generated/l10n.dart';

class ErrorNetworkWidget extends StatelessWidget {
  const ErrorNetworkWidget({
    super.key,
    this.onPressed,
    this.physics,
    this.tryAgain,
    this.scrollPadding,
    this.message,
    this.center = false,
    this.imageFit,
    this.imageHeight,
    this.imageWidth = 240,
    this.padding = const EdgeInsets.only(top: 32, left: 16, right: 16),
  });

  final VoidCallback? onPressed;

  final ScrollPhysics? physics;

  final bool? tryAgain;

  final double imageWidth;

  final double? imageHeight;

  final BoxFit? imageFit;

  final bool center;

  final EdgeInsetsGeometry padding;

  final EdgeInsetsGeometry? scrollPadding;

  final String? message;

  @override
  Widget build(BuildContext context) {
    final child = Padding(
      padding: padding,
      child: SingleChildScrollView(
        padding: scrollPadding,
        physics: physics,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VpMarketAssets.images.error.image(
                width: imageWidth,
                height: imageHeight,
                fit: imageFit,
              ),
              Text(
                message ?? VPMarketInfoLocalize.current.error,
                style: vpTextStyle.body14?.copyWith(color: themeData.gray500),
              ),
              const SizedBox(height: 24),
              if (tryAgain ?? true)
                ButtonWidget(
                  action: VPMarketInfoLocalize.current.retry,
                  onPressed: onPressed,
                ),
            ],
          ),
        ),
      ),
    );

    if (center) return Center(child: child);

    return child;
  }
}
