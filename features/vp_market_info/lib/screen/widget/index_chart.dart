import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_market/screen/overview/market_bloc.dart';

class IndexChart extends StatefulWidget {
  const IndexChart({Key? key}) : super(key: key);

  @override
  State<IndexChart> createState() => _IndexChartState();
}

class _IndexChartState extends State<IndexChart> {
  late MarketBloc _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = context.read<MarketBloc>();
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
    // const offsetPadding = 32.0;
    // final priceChart = Column(
    //   children: [
    //     StreamBuilder<List<ChartPriceMarketEntity>>(
    //       stream: _bloc.streamMarketPriceChart,
    //       builder: (context, snapshot) {
    //         final data = snapshot.data ?? [];
    //
    //         if (_bloc.selectedMarket == null) {
    //           return const SizedBox(height: 137, child: EmptyChartView());
    //         }
    //
    //         return ChartMarketView(
    //           chartData: data,
    //           marketEntity: _bloc.selectedMarket!,
    //         );
    //       },
    //     ),
    //     const Padding(
    //       padding: EdgeInsets.only(top: 8),
    //       child: MarketTimeChart(offset: offsetPadding),
    //     ),
    //   ],
    // );
    // return BlocBuilder<MarketBloc, MarketState>(
    //   builder:
    //       (context, state) => SizedBox(
    //         height: 230,
    //         child: Stack(
    //           children: [
    //             priceChart,
    //             StreamBuilder<bool>(
    //               initialData: true,
    //               stream: _bloc.streamIsPriceChartType,
    //               builder: (_, snapshot) {
    //                 if (snapshot.data == true) {
    //                   return const SizedBox();
    //                 }
    //                 final url = _bloc.getTradingViewUrl();
    //                 return ConnectionStatusWidget(
    //                   errorWidget: const NoneTransactionWidget(),
    //                   child: TradingView(
    //                     key: ValueKey(url),
    //                     url: url,
    //                     showCloseIcon: false,
    //                   ),
    //                 );
    //               },
    //             ),
    //             SwitchChartWidget(
    //               onSwitchChartType:
    //                   (isPriceChart) => _bloc.switchChartType(isPriceChart),
    //             ),
    //           ],
    //         ),
    //       ),
    // );
  }
}
