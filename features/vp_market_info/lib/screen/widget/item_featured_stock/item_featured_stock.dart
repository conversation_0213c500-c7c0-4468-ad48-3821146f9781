import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/common/extension/num_extensions.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/data/market_top_trading_entity.dart';
import 'package:vp_market/data/stock_detail_entity.dart';
import 'package:vp_market/data/stock_with_chart_entity.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class ItemFeaturedStock extends StatefulWidget {
  const ItemFeaturedStock({
    super.key,
    required this.stockWithChart,
    required this.displayMode,
    required this.isToday,
  });

  final StockWithChartEntity stockWithChart;

  final TopTradingDisplayMode displayMode;

  /// Item không phải trong ngày
  final bool isToday;

  @override
  ItemFeaturedStockState createState() => ItemFeaturedStockState();
}

class ItemFeaturedStockState extends State<ItemFeaturedStock> {
  StockDetailEntity get stock => widget.stockWithChart.stockDetail;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Text(
                      widget.stockWithChart.stockTopTrading!.symbol,
                      style: vpTextStyle.subtitle14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                    const SizedBox(width: 8),

                    VPSocketInvestmentBuilder<VPStockInfoData>(
                      symbol: stock.symbol,
                      channel: VPSocketChannel.stockInfo.name,
                      buildWhen: (preData, data) {
                        return (preData?.priceChange ?? stock.changeValue) !=
                            data?.priceChange;
                      },
                      builder: (_, __, data, child) {
                        final change =
                            data?.priceChange ?? stock.changeValue ?? 0;

                        final changeValue =
                            FormatUtils.formatClosePrice(
                              change,
                              showSign: false,
                            ) ??
                            '-';

                        return Text(
                          changeValue,
                          textAlign: TextAlign.end,
                          style: vpTextStyle.subtitle14.copyColor(
                            data.color ?? stock.colorByPrice,
                          ),
                        );
                      },
                    ),

                    const SizedBox(width: 4),

                    VPProfitIconView(
                      symbol: stock.symbol,
                      closePrice: stock.closePrice,
                      refPrice: stock.referencePrice,
                      initColor: stock.colorByPrice,
                    ),

                    const SizedBox(width: 4),

                    VPSocketInvestmentBuilder<VPStockInfoData>(
                      symbol: stock.symbol,
                      channel: VPSocketChannel.stockInfo.name,
                      buildWhen: (preData, data) {
                        final oldPercent =
                            preData?.percentChange ?? stock.changePercent;

                        return oldPercent != data?.percentChange;
                      },
                      builder: (_, __, data, child) {
                        final percent =
                            data?.percentChange ?? stock.changePercent ?? 0;

                        final changePercent = FormatUtils.formatPercent(
                          percent,
                          showSign: false,
                        );

                        return Text(
                          changePercent ?? '-',
                          textAlign: TextAlign.end,
                          style: vpTextStyle.body14.copyColor(
                            data.color ?? stock.colorByPrice,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                SizedBox(height: 2),
                Text(
                  stock.companyName,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: themeData.gray700,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Container(
            constraints: const BoxConstraints(minWidth: 120),
            alignment: Alignment.center,
            height: 40,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: getBackgroundColor(),
            ),
            child: displayValueByMode(stock),
          ),
        ],
      ),
    );
  }

  Color getTextColor() {
    final percent = widget.stockWithChart.stockTopTrading?.percentChange ?? 0;
    return widget.displayMode == TopTradingDisplayMode.changePercent
        ? (percent < 0)
            ? themeData.decreaseColor
            : themeData.increaseColor
        : themeData.gray700;
  }

  Color getBackgroundColor() {
    final percent = widget.stockWithChart.stockTopTrading?.percentChange ?? 0;
    return widget.displayMode == TopTradingDisplayMode.changePercent
        ? (percent < 0)
            ? themeData.decreaseColor.withOpacity(0.16)
            : themeData.increaseColor.withOpacity(0.16)
        : themeData.highlightBg;
  }

  Widget displayValueByMode(StockDetailEntity stockDetail) {
    switch (widget.displayMode) {
      case TopTradingDisplayMode.changePercent:
        if (widget.isToday) {
          return VPSocketInvestmentBuilder<VPStockInfoData>(
            symbol: stock.symbol,
            channel: VPSocketChannel.stockInfo.name,
            buildWhen: (preData, data) {
              final oldPercent = preData?.percentChange ?? stock.changePercent;

              return oldPercent != data?.percentChange;
            },
            builder: (_, __, data, child) {
              final percent = data?.percentChange ?? stock.changePercent ?? 0;

              final changePercent = FormatUtils.formatPercent(
                percent,
                showSign: true,
                showPositiveSign: false,
              );

              return AutoSizeText(
                changePercent ?? '0.00%',
                style: vpTextStyle.subtitle14?.copyWith(
                  color: getTextColor(),
                  height: 1,
                ),
              );
            },
          );
        }
        return AutoSizeText(
          MarketInfoConstants.formatPercent(
            widget.stockWithChart.stockTopTrading?.percentChange,
          ),
          style: vpTextStyle.subtitle14?.copyWith(
            color: getTextColor(),
            height: 1,
          ),
        );

      case TopTradingDisplayMode.tradingValue:
        if (widget.isToday) {
          return VPTotalTradingValueItemView(
            symbol: stock.symbol,
            initTotalValue: stock.totalTradingValue,
            builder: (value) {
              return AutoSizeText(
                value?.toVolDisplay() ?? '',
                style: vpTextStyle.subtitle14?.copyWith(
                  color: getTextColor(),
                  height: 1,
                ),
              );
            },
          );
        }

        return AutoSizeText(
          widget.stockWithChart.stockTopTrading?.topTradingValue
                  ?.toVolDisplay() ??
              '',
          style: vpTextStyle.subtitle14?.copyWith(
            color: getTextColor(),
            height: 1,
          ),
        );

      default:
        if (widget.isToday) {
          return VPTotalTradingVolumeItemView(
            symbol: stock.symbol,
            initTotalVolume: stock.totalVolume,
            builder: (value) {
              return AutoSizeText(
                MarketInfoConstants.formatVol(value),
                style: vpTextStyle.subtitle14?.copyWith(
                  color: getTextColor(),
                  height: 1,
                ),
              );
            },
          );
        }

        return AutoSizeText(
          MarketInfoConstants.formatVol(
            widget.stockWithChart.stockTopTrading?.topVolume,
          ),
          style: vpTextStyle.subtitle14?.copyWith(
            color: getTextColor(),
            height: 1,
          ),
        );
    }
  }
}
