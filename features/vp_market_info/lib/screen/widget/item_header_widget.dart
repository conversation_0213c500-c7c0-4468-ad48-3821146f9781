import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/generated/assets.gen.dart';

class ItemHeaderWidget extends StatelessWidget {
  const ItemHeaderWidget({
    super.key,
    required this.text,
    this.icon,
    this.onTap,
    this.textStyle,
    this.isFull = false,
    this.isHome = false,
  });

  final String text;
  final String? icon;
  final VoidCallback? onTap;
  final TextStyle? textStyle;
  final bool isFull;
  final bool isHome;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(40),
        color: isHome ? Colors.transparent : themeData.highlightBg,
      ),
      padding: EdgeInsets.symmetric(horizontal: isHome ? 0 : 12, vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            isFull
                ? Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 2),
                    child: Text(
                      text,
                      style:
                          textStyle ??
                          vpTextStyle.captionRegular?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                )
                : Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 2),
                    child: Text(
                      text,
                      style:
                          textStyle ??
                          vpTextStyle.captionRegular?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
            SizedBox(width: isHome ? 8 : 4),
            if (isHome)
              Icon(
                Icons.arrow_forward_ios_sharp,
                color: themeData.icon,
                size: 16,
              ),
            if (icon != null)
              SvgPicture.asset(
                icon!,
                package: VpMarketAssets.package,
                color: Theme.of(context).focusColor,
                width: 16,
              ),
          ],
        ),
      ),
    );
  }
}
