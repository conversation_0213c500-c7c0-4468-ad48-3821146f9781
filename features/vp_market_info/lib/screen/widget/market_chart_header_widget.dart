import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/common/extension/num_extensions.dart';
import 'package:vp_market/data/chart_price_market_entity.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/market_bloc.dart';
import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class MarketChartHeaderWidget extends StatelessWidget {
  const MarketChartHeaderWidget({required this.market, super.key});

  final MarketInfoModel market;

  @override
  Widget build(BuildContext context) {
    final style1 = vpTextStyle.captionRegular.copyColor(vpColor.textSecondary);

    final style2 = vpTextStyle.captionSemiBold.copyColor(vpColor.textPrimary);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              flex: 1,
              child: VPSocketInvestmentBuilder<VPMarketInfoData>(
                symbol: market.indexCode.socketChannel,
                channel: VPSocketChannel.marketInfo.name,
                buildWhen:
                    (pData, data) =>
                        pData?.totalVolume != data?.totalVolume ||
                        pData?.oddLotTotalVolume != data?.oddLotTotalVolume,
                builder: (_, __, data, ___) {
                  final totalVolume =
                      data?.totalVolume ?? market.totalVolume ?? 0;
                  final oddLotTotalVolume =
                      data?.oddLotTotalVolume ?? market.oddLotTotalVolume ?? 0;

                  final total = totalVolume + oddLotTotalVolume;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('KLGD', style: style1),

                      Text(
                        FormatUtils.formatVolWithTrailing(total),
                        style: style2,
                      ),
                    ],
                  );
                },
              ),
            ),

            Expanded(
              flex: 1,
              child: VPSocketInvestmentBuilder<VPMarketInfoData>(
                symbol: market.indexCode.socketChannel,
                channel: VPSocketChannel.marketInfo.name,
                buildWhen:
                    (pData, data) =>
                        pData?.totalValue != data?.totalValue ||
                        pData?.oddLotTotalValue != data?.oddLotTotalValue,
                builder: (_, __, data, ___) {
                  final totalValue = data?.totalValue ?? market.totalValue ?? 0;
                  final oddLotTotalValue =
                      data?.oddLotTotalValue ?? market.oddLotTotalValue ?? 0;

                  final total = totalValue + oddLotTotalValue;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('GTGD', style: style1),

                      Text(
                        FormatUtils.formatVolWithTrailing(total),
                        style: style2,
                      ),
                    ],
                  );
                },
              ),
            ),
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(VPMarketInfoLocalize.current.ts, style: style1),
                  Text(market.marketStatus ?? '', style: style2),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: market.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (pData, data) => pData?.advances != data?.advances,
              builder: (context, _, data, __) {
                final advances = data?.advances ?? market.advances ?? 0;

                return itemPrice(
                  icon: VpMarketAssets.icons.icUp.path,
                  color: themeData.greenChart,
                  value: advances.toString(),
                );
              },
            ),
            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: market.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (pData, data) => pData?.declines != data?.declines,
              builder: (context, _, data, __) {
                final declines = data?.declines ?? market.declines ?? 0;

                return itemPrice(
                  icon: VpMarketAssets.icons.icDown.path,
                  color: themeData.redChart,
                  value: declines.toString(),
                );
              },
            ),
            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: market.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (pData, data) => pData?.noChange != data?.noChange,
              builder: (context, _, data, __) {
                final noChange = data?.noChange ?? market.noChange ?? 0;

                return itemPrice(
                  icon: VpMarketAssets.icons.icCircle.path,
                  color: themeData.yellowChart,
                  value: noChange.toString(),
                );
              },
            ),

            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: market.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (pData, data) => pData?.numberOfCe != data?.numberOfCe,
              builder: (context, _, data, __) {
                final numberOfCe = data?.numberOfCe ?? market.numberOfCe ?? 0;

                return itemPrice(
                  icon: VpMarketAssets.icons.icUp.path,
                  color: themeData.purple,
                  value: numberOfCe.toString(),
                );
              },
            ),

            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: market.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (pData, data) => pData?.numberOfFl != data?.numberOfFl,
              builder: (context, _, data, __) {
                final numberOfFl = data?.numberOfFl ?? market.numberOfFl ?? 0;

                return itemPrice(
                  icon: VpMarketAssets.icons.icDown.path,
                  color: themeData.blue,
                  value: numberOfFl.toString(),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget itemPrice({
    required String icon,
    required Color color,
    required String value,
  }) {
    return Expanded(
      child: Row(
        children: [
          SvgPicture.asset(
            icon,
            colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
            width: 16,
            height: 16,
            package: VpMarketAssets.package,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: AutoSizeText(
              value,
              style: vpTextStyle.captionMedium?.copyWith(color: color),
            ),
          ),
        ],
      ),
    );
  }
}
