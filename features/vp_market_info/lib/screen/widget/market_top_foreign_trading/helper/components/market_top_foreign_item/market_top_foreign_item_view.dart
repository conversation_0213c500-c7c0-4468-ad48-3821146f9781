import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/data/stock_detail_entity.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/entity/top_foreign_entity.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class MarketTopForeignItemView extends StatefulWidget {
  const MarketTopForeignItemView({super.key, required this.topForeignEntity});

  final TopForeignEntity topForeignEntity;

  @override
  MarketTopForeignItemViewState createState() =>
      MarketTopForeignItemViewState();
}

class MarketTopForeignItemViewState extends State<MarketTopForeignItemView> {
  TopForeignEntity get topForeignEntity => widget.topForeignEntity;

  StockDetailEntity get stock => topForeignEntity.stockDetailEntity!;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      stock.symbol,
                      style: vpTextStyle.subtitle14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                    const SizedBox(width: 8),

                    VPSocketInvestmentBuilder<VPStockInfoData>(
                      symbol: stock.symbol,
                      channel: VPSocketChannel.stockInfo.name,
                      buildWhen: (preData, data) {
                        return (preData?.priceChange ?? stock.changeValue) !=
                            data?.priceChange;
                      },
                      builder: (_, __, data, child) {
                        final change = data?.priceChange ?? stock.changeValue ?? 0;

                        final changeValue =
                            FormatUtils.formatClosePrice(
                              change,
                              showSign: false,
                            ) ??
                            '-';

                        return Text(
                          changeValue,
                          textAlign: TextAlign.end,
                          style: vpTextStyle.subtitle14.copyColor(
                            data.color ?? stock.colorByPrice,
                          ),
                        );
                      },
                    ),

                    const SizedBox(width: 4),

                    VPProfitIconView(
                      symbol: stock.symbol,
                      closePrice: stock.closePrice,
                      refPrice: stock.referencePrice,
                      initColor: stock.colorByPrice,
                    ),

                    const SizedBox(width: 4),

                    VPSocketInvestmentBuilder<VPStockInfoData>(
                      symbol: stock.symbol,
                      channel: VPSocketChannel.stockInfo.name,
                      buildWhen: (preData, data) {
                        final oldPercent =
                            preData?.percentChange ?? stock.changePercent;

                        return oldPercent != data?.percentChange;
                      },
                      builder: (_, __, data, child) {
                        final percent =
                            data?.percentChange ?? stock.changePercent ?? 0;

                        final changePercent = FormatUtils.formatPercent(
                          percent,
                          showSign: false,
                        );

                        return Text(
                          changePercent ?? '-',
                          textAlign: TextAlign.end,
                          style: vpTextStyle.body14.copyColor(
                            data.color ?? stock.colorByPrice,
                          ),
                        );
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 2),

                Text(
                  widget.topForeignEntity.getFullName(),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: themeData.gray700,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Container(
            constraints: const BoxConstraints(minWidth: 120),
            alignment: Alignment.center,
            height: 40,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: themeData.highlightBg,
            ),
            child: AutoSizeText(
              widget.topForeignEntity.getValue(),
              style: vpTextStyle.subtitle14?.copyWith(
                color: themeData.gray700,
                height: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
