import 'dart:async';
import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/router/market_router.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/components/market_top_foreign_item/market_top_foreign_item_view.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/entity/top_foreign_entity.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class MarketTopForeignSlideView extends StatefulWidget {
  const MarketTopForeignSlideView({super.key, required this.listData});

  final List<TopForeignEntity> listData;

  @override
  MarketTopForeignSlideViewState createState() =>
      MarketTopForeignSlideViewState();
}

class MarketTopForeignSlideViewState extends State<MarketTopForeignSlideView> {
  int currentPage = 0;

  final controllerIndex = StreamController();
  final controllerReloadPage = StreamController();

  final PageController pageController = PageController();

  List<List<TopForeignEntity>> listForeignEntity = [];

  @override
  void initState() {
    super.initState();
    final list = widget.listData;
    listForeignEntity.clear();
    if (list.length <= 5) {
      listForeignEntity.add(list);
    }
    if (list.length > 5) {
      final list1 = list.sublist(0, 5);
      final list2 = list.sublist(5);
      listForeignEntity.add(list1);
      listForeignEntity.add(list2);
    }
    controllerIndex.sink.add(true);
    controllerReloadPage.sink.add(true);
  }

  @override
  void dispose() {
    pageController.dispose();
    controllerIndex.close();
    controllerReloadPage.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            buildPageView(),
            const SizedBox(height: 8),
            buildListDotView(),
          ],
        ),
      ),
    );
  }

  // Build List DotView
  Padding buildListDotView() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: StreamBuilder(
        stream: controllerIndex.stream,
        builder: (context, snapshot) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              listForeignEntity.length,
              (index) => buildDot(index: index),
            ),
          );
        },
      ),
    );
  }

  // Build PageView
  Expanded buildPageView() {
    return Expanded(
      child: StreamBuilder(
        stream: controllerReloadPage.stream,
        builder: (context, snapshot) {
          return PageView.builder(
            onPageChanged: (index) {
              currentPage = index;
              controllerIndex.sink.add(true);
            },
            controller: pageController,
            itemCount: listForeignEntity.length,
            itemBuilder: (context, index) {
              final listData = listForeignEntity[index];
              return ListView.separated(
                // shrinkWrap: true,
                itemBuilder: (_, index) {
                  final topForeignEntity = listData[index];
                  return InkWell(
                    onTap: () {
                      context.pushNamed(
                        MarketRouter.stockDetail.routeName,
                        queryParameters:
                            StockDetailArgs(
                              symbol: topForeignEntity.getSymbol(),
                            ).toQueryParams(),
                      );
                    },
                    child: MarketTopForeignItemView(
                      key: ValueKey(topForeignEntity.getSymbol()),
                      topForeignEntity: topForeignEntity,
                    ),
                  );
                },
                separatorBuilder: (_, index) => const SizedBox(),
                physics: const NeverScrollableScrollPhysics(),
                itemCount: listData.length,
              );
            },
          );
        },
      ),
    );
  }

  // Dot View
  AnimatedContainer buildDot({int? index}) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(right: 8),
      height: 8,
      width: currentPage == index ? 24 : 8,
      decoration: BoxDecoration(
        color: currentPage == index ? themeData.primary : themeData.gray300,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
