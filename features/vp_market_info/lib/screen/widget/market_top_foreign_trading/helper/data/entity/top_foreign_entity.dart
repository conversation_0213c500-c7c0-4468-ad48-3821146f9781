import 'package:flutter/material.dart';
import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_common/extensions/price_exts.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/common/extension/num_extensions.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/data/stock_detail_entity.dart';
import 'package:vp_market/generated/assets.gen.dart';

class TopForeignEntity {
  final String? symbol;
  final String? fullName;
  final num? value;
  StockDetailEntity? stockDetailEntity;

  TopForeignEntity({
    this.symbol,
    this.fullName,
    this.value,
    this.stockDetailEntity,
  });

  String getPrice() {
    final price = stockDetailEntity?.price;
    return price?.getPriceFormatted(currency: '', convertToThousand: true) ??
        '';
  }

  String getValue() {
    return value?.toVolDisplay() ?? MarketInfoConstants.formatVol(value);
  }

  String getFullName() {
    return fullName ?? '';
  }

  String getSymbol() {
    return symbol ?? '';
  }

  String getPercent() {
    final changePercent = stockDetailEntity?.changePercent?.abs();
    return changePercent?.getMarketChangePercentDisplay() ?? '';
  }

  Color getColor() {
    return stockDetailEntity?.colorByPrice ?? themeData.gray500;
  }
}
