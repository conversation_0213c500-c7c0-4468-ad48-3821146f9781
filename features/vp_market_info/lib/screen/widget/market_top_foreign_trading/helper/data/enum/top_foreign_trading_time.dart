enum TopForeignTradingTime {
  oneDay('1D'),
  oneWeek('1W'),
  oneMonth('1M'),
  threeMonths('3M'),
  oneYear('1Y');

  final String code;

  String get value {
    switch (this) {
      case oneDay:
        return '';
      case oneWeek:
        return '1W';
      case oneMonth:
        return '1M';
      case threeMonths:
        return '3M';
      case oneYear:
        return '1Y';
    }
  }

  const TopForeignTradingTime(this.code);
}

enum TopTradingCriteria {
  mua<PERSON>ong('Mua ròng'),
  ban<PERSON><PERSON>('Bán ròng'),
  muaNhieuNhat('Mua nhiều nhất'),
  ban<PERSON><PERSON><PERSON>Nhat('Bán nhiều nhất');

  final String label;

  const TopTradingCriteria(this.label);
}
