import 'package:flutter/widgets.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/filter/stock_filter_row.dart';
import 'package:vp_market/screen/widget/home_stock_loading.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/components/market_top_foreign_slide_view.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/enum/top_foreign_trading_time.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/market_top_foreign_trading_bloc.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/market_top_foreign_trading_state.dart';

class MarketTopForeignTrading extends StatelessWidget {
  const MarketTopForeignTrading({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MarketTopForeignTradingBloc>(
      create: (_) => MarketTopForeignTradingBloc()..loadData(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            VPMarketInfoLocalize.current.topForeign,
            style: vpTextStyle.subtitle16?.copyWith(color: themeData.black),
          ),

          const SizedBox(height: 8),

          Builder(
            builder: (context) {
              return FilterRow<TopTradingCriteria>(
                initValue: TopTradingCriteria.muaRong,
                onChanged: (criteria) {
                  context.read<MarketTopForeignTradingBloc>().onSelectCriteria(
                    criteria,
                  );
                },
                title: VPMarketInfoLocalize.current.criteria,
                items: TopTradingCriteria.values,
                builder: (item) => item.label,
              );
            }
          ),

          const SizedBox(height: 8),

          Builder(
            builder: (context) {
              return FilterRow<TopForeignTradingTime>(
                initValue: TopForeignTradingTime.oneDay,
                onChanged: (time) {
                  context.read<MarketTopForeignTradingBloc>().onSelectTime(time);
                },
                title: VPMarketInfoLocalize.current.time,
                items: TopForeignTradingTime.values,
                builder: (item) => item.code,
              );
            }
          ),

          const SizedBox(height: 8),

          BlocBuilder<
            MarketTopForeignTradingBloc,
            MarketTopForeignTradingState
          >(
            builder: (context, state) {
              if (state.apiStatus.isLoading) {
                return HomeStockLoading(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                );
              }

              if (state.apiStatus.isError) {
                return const SizedBox.shrink();
              }

              return SizedBox(
                height: 432,
                child: MarketTopForeignSlideView(listData: state.listData),
              );
            },
          ),
        ],
      ),
    );
  }
}
