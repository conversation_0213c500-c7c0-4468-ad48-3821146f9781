import 'package:flutter/foundation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/data/stock_detail_entity.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/entity/top_foreign_entity.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/entity/top_foreign_trading_entity.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/enum/top_foreign_trading_time.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/market_top_foreign_trading_state.dart';

class MarketTopForeignTradingBloc extends Cubit<MarketTopForeignTradingState> {
  MarketTopForeignTradingBloc()
    : super(
        MarketTopForeignTradingState(
          apiStatus: ApiStatus.initial(),
          tradingTime: TopForeignTradingTime.oneDay,
          tradingCriteria: TopTradingCriteria.muaRong,
        ),
      );

  ListForeignNetEntity? _entity;

  final _repository = GetIt.instance<MarketRepository>();

  Future loadData() async {
    if (state.tradingTime == TopForeignTradingTime.oneDay) {
      apiTopForeignTradingD();
      return;
    }

    apiTopForeignTrading();
  }

  /// Call api lay StockDetailEntity de gan vao gia tri trong mang
  Future<List<TopForeignEntity>> transformListData(
    ListForeignNetEntity? listForeignNetEntity,
  ) async {
    _entity = listForeignNetEntity;

    final mapData = {
      TopTradingCriteria.muaRong: listForeignNetEntity?.listForeignNetBuy,
      TopTradingCriteria.banRong: listForeignNetEntity?.listForeignNetSell,
      TopTradingCriteria.muaNhieuNhat: listForeignNetEntity?.listTopValueBuy,
      TopTradingCriteria.banNhieuNhat: listForeignNetEntity?.listTopValueSell,
    };

    final listTopForeignEntity = mapData[state.tradingCriteria] ?? [];

    List<TopForeignEntity> listTopForeignEntityStream = [];

    String symbols = '';
    for (int i = 0; i < listTopForeignEntity.length; i++) {
      if (i < listTopForeignEntity.length - 1) {
        symbols += '${listTopForeignEntity[i].getSymbol()},';
      } else {
        symbols += listTopForeignEntity[i].getSymbol();
      }
    }
    final stockDetailEntities = await _repository.getStockList(symbols);

    for (int i = 0; i < listTopForeignEntity.length; i++) {
      TopForeignEntity topForeignEntity = listTopForeignEntity[i];
      StockDetailEntity? element = stockDetailEntities.findFirstOrNull(
        (element) => element.symbol == topForeignEntity.symbol,
      );
      if (element != null) {
        topForeignEntity.stockDetailEntity = element;
        listTopForeignEntityStream.add(topForeignEntity);
      }
    }

    return listTopForeignEntityStream;
  }

  /// Call API topForeignTradingD
  void apiTopForeignTradingD() async {
    try {
      emit(state.copyWith(status: ApiStatus.loading()));

      final objD = await _repository.getTopForeignTradingD();

      final data = await transformListData(ListForeignNetEntity.initObjD(objD));

      emit(state.copyWith(status: ApiStatus.done(), listData: data));
    } catch (e) {
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }

  /// Call API topForeignTrading
  void apiTopForeignTrading() async {
    try {
      emit(state.copyWith(status: ApiStatus.loading()));

      final obj = await _repository.getTopForeignTrading(
        state.tradingTime.value,
      );

      final data = await transformListData(ListForeignNetEntity.initObj(obj));

      emit(state.copyWith(status: ApiStatus.done(), listData: data));
    } catch (e) {
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }

  /// onSelectCriteria
  void onSelectCriteria(TopTradingCriteria criteria) async {
    try {
      emit(
        state.copyWith(status: ApiStatus.loading(), tradingCriteria: criteria),
      );

      final data = await transformListData(_entity);

      emit(state.copyWith(status: ApiStatus.done(), listData: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: ApiStatus.error(e)));
    }
  }

  /// onSelectTime
  void onSelectTime(TopForeignTradingTime time) {
    emit(state.copyWith(tradingTime: time));

    loadData();
  }
}
