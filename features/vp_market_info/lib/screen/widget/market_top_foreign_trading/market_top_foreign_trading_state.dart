import 'package:vp_core/vp_core.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/entity/top_foreign_entity.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/enum/top_foreign_trading_time.dart';

class MarketTopForeignTradingState {
  final TopForeignTradingTime tradingTime;

  final TopTradingCriteria tradingCriteria;

  final List<TopForeignEntity> listData;

  final ApiStatus apiStatus;

  MarketTopForeignTradingState({
    this.listData = const [],
    required this.apiStatus,
    required this.tradingTime,
    required this.tradingCriteria,
  });

  MarketTopForeignTradingState copyWith({
    TopForeignTradingTime? tradingTime,
    TopTradingCriteria? tradingCriteria,
    List<TopForeignEntity>? listData,
    ApiStatus? status,
  }) {
    return MarketTopForeignTradingState(
      tradingCriteria: tradingCriteria ?? this.tradingCriteria,
      tradingTime: tradingTime ?? this.tradingTime,
      listData: listData ?? this.listData,
      apiStatus: status ?? apiStatus,
    );
  }
}
