import 'dart:async';
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/market_top_trading_entity.dart';
import 'package:vp_market/data/stock_with_chart_entity.dart';
import 'package:vp_market/router/market_router.dart';
import 'package:vp_market/screen/widget/item_featured_stock/item_featured_stock.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class MarketTopStockList extends StatefulWidget {
  const MarketTopStockList({
    super.key,
    required this.data,
    required this.displayMode,
    required this.isToday,
  });

  final List<StockWithChartEntity> data;
  final TopTradingDisplayMode displayMode;
  final bool isToday;

  @override
  MarketTopStockListState createState() => MarketTopStockListState();
}

class MarketTopStockListState extends State<MarketTopStockList> {
  int currentPage = 0;

  final controllerUI = StreamController();

  final PageController controllerPageView = PageController();

  List<List<StockWithChartEntity>> listDataStock = [];

  @override
  void initState() {
    super.initState();
    final list = widget.data;
    if (list.length <= 5) {
      listDataStock.add(list);
    }
    if (list.length > 5) {
      final list1 = list.sublist(0, 5);
      final list2 = list.sublist(5);
      listDataStock.add(list1);
      listDataStock.add(list2);
    }
  }

  @override
  void dispose() {
    controllerPageView.dispose();
    controllerUI.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            buildPageView(),
            const SizedBox(height: 8),
            buildListDotView(),
          ],
        ),
      ),
    );
  }

  // Build List DotView
  Padding buildListDotView() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: StreamBuilder(
        stream: controllerUI.stream,
        builder: (context, snapshot) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              listDataStock.length,
              (index) => buildDot(index: index),
            ),
          );
        },
      ),
    );
  }

  // Build PageView
  Expanded buildPageView() {
    return Expanded(
      child: PageView.builder(
        onPageChanged: (index) {
          currentPage = index;
          controllerUI.sink.add(true);
        },
        controller: controllerPageView,
        itemCount: listDataStock.length,
        itemBuilder: (context, index) {
          final listData = listDataStock[index];
          return ListView.separated(
            shrinkWrap: true,
            itemBuilder: (_, index) {
              StockWithChartEntity item = listData[index];
              return InkWell(
                onTap: () {
                  context.pushNamed(
                    MarketRouter.stockDetail.routeName,
                    queryParameters:
                        StockDetailArgs(
                          symbol: item.stockDetail.symbol,
                        ).toQueryParams(),
                  );
                },
                child: ItemFeaturedStock(
                  isToday: widget.isToday,
                  stockWithChart: item,
                  displayMode: widget.displayMode,
                  key: ValueKey(item.stockDetail.symbol),
                ),
              );
            },
            separatorBuilder: (_, index) => const SizedBox(),
            physics: const NeverScrollableScrollPhysics(),
            itemCount: listData.length,
          );
        },
      ),
    );
  }

  // Dot View
  AnimatedContainer buildDot({int? index}) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(right: 8),
      height: 8,
      width: currentPage == index ? 24 : 8,
      decoration: BoxDecoration(
        color: currentPage == index ? themeData.primary : themeData.gray300,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
