import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_icon_bg.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/screen/overview/market_bloc.dart';

class SwitchChartWidget extends StatelessWidget {
  const SwitchChartWidget({super.key, required this.onSwitchChartType});

  final Function onSwitchChartType;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.topRight,
      padding: const EdgeInsets.only(right: 50),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          _SwitchChartTypeButton(onTap: (value) => onSwitchChartType(value)),
          const SizedBox(width: 8),
          BlocBuilder<MarketBloc, MarketState>(
            builder:
                (context, state) => InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: () {},
                  child: AppIconBg(icon: VpMarketAssets.icons.icOpen.svg()),
                ),
          ),
        ],
      ),
    );
  }
}

class _SwitchChartTypeButton extends StatefulWidget {
  final bool isPriceChart;
  final Function(bool) onTap;

  const _SwitchChartTypeButton({
    super.key,
    this.isPriceChart = true,
    required this.onTap,
  });

  @override
  State<_SwitchChartTypeButton> createState() => _SwitchChartTypeButtonState();
}

class _SwitchChartTypeButtonState extends State<_SwitchChartTypeButton> {
  late bool _isPriceChart;

  @override
  void initState() {
    super.initState();
    _isPriceChart = widget.isPriceChart;
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          _isPriceChart = !_isPriceChart;
        });
        widget.onTap(_isPriceChart);
      },
      child: AppIconBg(
        icon:
            _isPriceChart
                ? VpMarketAssets.icons.icTradingView.svg()
                : VpMarketAssets.icons.icPriceChart.svg(),
      ),
    );
  }
}
