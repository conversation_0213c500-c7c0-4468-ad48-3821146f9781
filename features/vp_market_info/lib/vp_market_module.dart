import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/core/repository/market_info_repository.dart';
import 'package:vp_market/core/repository/market_info_service.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/generated/intl/messages_all.dart';
import 'package:vp_common/vp_common.dart' hide AppLocalizationDelegate;
import 'package:vp_market/router/market_router.dart';
import 'package:vp_market/screen/market_maneger/market_manager_page.dart';
import 'package:vp_market/screen/trading_view/trading_page.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class VpMarketModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerLazySingleton<MarketRepository>(
      () => MarketRepoImpl(restClient: service()),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: MarketRouter.market.routeName,
        name: MarketRouter.market.routeName,
        builder: (context, state) {
          return MarketManagerPage(
            args: MarketDetailArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),
      GoRoute(
        path: MarketRouter.tradingView.routeName,
        name: MarketRouter.tradingView.routeName,
        builder: (context, state) {
          return TradingPage(tradingViewUrlFull: state.extra as String?);
        },
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return "vp_market";
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<VPMarketInfoLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VPMarketInfoLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
