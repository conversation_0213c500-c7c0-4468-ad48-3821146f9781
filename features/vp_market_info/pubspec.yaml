name: vp_market
description: "A new Flutter package project."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  vp_design_system:
    path: ../../library/vp_design_system
  vp_common:
    path: ../../library/vp_common
  vp_core:
    path: ../../library/vp_core
  vp_stock_common:
    path: ../../library/vp_stock_common
  vp_socket:
    path: ../../library/vp_socket

  shimmer:
  syncfusion_flutter_treemap: 31.1.19
  dio_cache_interceptor:


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner:
  custom_lint:
  json_serializable:
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:
  syncfusion_flutter_charts: 31.1.19
  fl_chart: 0.68.0


dependency_overrides:
  intl: ^0.19.0

flutter_gen:
  output: lib/generated/
  line_length: 80
  assets:
    outputs:
      class_name: VpMarketAssets
      package_parameter_enabled: true

    # Optional
  integrations:
    image: true
    flutter_svg: true
    
 
flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/

flutter_intl:
  enabled: true
  class_name: VPMarketInfoLocalize