import 'package:vp_common/error/handle_error.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/base/base_response/base_response.dart';
import 'package:vp_money/core/service/money_service.dart';
import 'package:vp_money/model/request/money_cash_in/bank_register_initialize_param.dart';
import 'package:vp_money/model/request/money_statement/statement_of_money_param.dart';
import 'package:vp_money/model/request/money_transfer/money_transfer_in_request.dart';
import 'package:vp_money/model/response/money_cash_in/account_balance.dart';
import 'package:vp_money/model/response/money_cash_in/account_cash_in.dart';
import 'package:vp_money/model/response/money_cash_in/initial_cash_in_model.dart';
import 'package:vp_money/model/response/money_cash_in/money_bank.dart';
import 'package:vp_money/model/response/money_cash_in/money_vir_account.dart';
import 'package:vp_money/model/response/money_cash_in/webview_status.dart';
import 'package:vp_money/model/response/money_history/money_cash_transfer_hist_obj.dart';
import 'package:vp_money/model/response/money_statement/derivative_account.dart';
import 'package:vp_money/model/response/money_statement/derivative_statement_of_money_model.dart';
import 'package:vp_money/model/response/money_statement/money_statement_model.dart';
import 'package:vp_money/model/response/money_transfer/money_account_recived_obj.dart';
import 'package:vp_money/model/response/money_transfer/money_available_cash_model.dart';
import 'package:vp_money/model/response/money_transfer/money_fee_atm_obj.dart';

abstract class MoneyRepository {
  Future<List<TransferAccountModel>> transferAccountList(
    String accountId,
    String transferType,
  );

  Future<MoneyAvailableCashModel> availableCashTransferOnline(
    String subAccount,
  );
  // Future<DerivativeAvailableCashOnHand> getDerivativeAvailableCashOnHand(
  //   String accountId,
  // );

  // Check chuyen tien noi bo
  // Future<MoneyCheckTransferResponseObj> checkTransfer({
  //   required String subAccount,
  //   required MoneyCheckTransferRequestObj param,
  //   required bool isInternal,
  // });

  // Giao dich chuyen tien noi bo
  // Future<BaseResponse?> internalTransfer(
  //   String subAccount,
  //   MoneyTransferRequestObj param,
  // );

  // Future<BaseResponse> derivativeDepositCashOnHand(
  //   String accountId,
  //   DepositCashOnHandParams params,
  // );

  // chuyển tiền
  // Future<BondBalanceAmountModel> getBondBalance();

  //check chuyển tiền từ BOND sang FCBOND
  // Future<BondToBondModel> bondToBondTransfer(BondToBondTransferRequest request);

  // Future<BaseResponse> derivativeWithDrawCashOnHand(
  //   String accountId,
  //   DepositCashOnHandParams params,
  // );

  // Chuyen tien ra ngoai Phai Sinh
  // Future<BaseResponse?> externalTransferDerivative(
  //   String accountId,
  //   MoneyTransferRequestObj param,
  //   String custodycd,
  // );

  //Chuyen tien ra ngoai
  // Future<BaseResponse?> externalTransfer(
  //   String subAccount,
  //   MoneyTransferRequestObj param,
  // );

  // Future<List<MoneyAccountRecivedObj>> transferAccountDerivativeList(
  //   String accountId,
  //   String custodycd,
  // );
  // Lấy phí chuyển tiền
  Future<MoneyFeeAmtObj> feeAmt(String accountId, num amount);

  //* api moi: Lịch sử chuyển tiền
  Future<List<MoneyCashTransferHistResponseObj>> cashTransferHist(
    String accountId,
    String fromDate,
    String toDate,
    String transtype,
  );
  //* api moi: Lay danh sach tai khoan khem theo thong tin ngan hang
  Future<List<MoneyVirAccount>> getVirAccountList();

  // Lay danh sach bank co the lien ket va bank da lien ket voi tai khoan cua minh
  // su dung status 'active' de hien thi nhung bank da lien ket
  Future<List<MoneyBank>> getListBankPartner();

  // Lay so du cua tai khoan lien ket
  Future<AccountBalance> getAccountBalance();

  // check trang thai lien ket cua tai khoan VPBANK
  Future<bool> getCheckLinkRelationWithVPBank();

  // Khoi tao thu ho, lien ket voi bank de lay otp tu webview
  Future<BaseResponse<InitalCashInModel>> postInitialCashInInput({
    required num amount,
    required String accountType,
  });

  // Dung de log lai nhung thao tac loi tu phia webview
  Future<BaseResponse> postLogStepLinkRelationWithVPBank({
    required StatusWebView statusWebView,
  });

  // Sau khi dang nhap thanh cong webview se tra ve state & code de call api nay
  // Dung de khoi tao dang ky lien ket voi bank
  Future<BaseResponse<InitalCashInModel>> postInitialLinkRelationWithVPBank(
    BankRegisterInitializeParam bankRegister,
  );

  // Dung de lay link tu api de mo webview dang nhap = tai khoan VPBank NEO
  // Truong hop KH chua co tai khoan thi se mo webview dang ky
  Future<BaseResponse<InitalCashInModel>> postConfirmLinkRelationWithVPBank();

  // Sau khi dang ky lien ket thanh cong se lay link tu api phia tren de mo webview
  // Dung de dang ky lien ket voi bank.
  // Thanh cong hay khong se kiem tra tren param cua webview
  Future<BaseResponse> putRegisterLinkRelationWithVPBank();

  // Sau khi hoan thanh otp lay session code lam param de put
  Future<BaseResponse<AccountCashIn>> putSumbitCashInInput(
    String requestCashId,
  );

  //* api mới: chuyển tiền nội bộ
  Future<BaseResponse?> cashInternalTransfer(MoneyTransferInRequest request);

  //  Huy trang thai lien ket cua tai khoan
  Future<BaseResponse?> putDeregister({required String idBank});

  // Lich su giao dich tien
  Future<List<MoneyStatementModel>> getCashStatementHist({
    required String subAccountId,
    required String fromDate,
    required String toDate,
  });

  Future<BaseResponse<List<DerivativeAccount>>> getAccounts();

  Future<BaseResponse<DerivativeStatementOfMoneyResponse>>
  getListStatementOfMoney(StatementOfMoneyParam param);
}

class MoneyRepositoryImpl extends MoneyRepository {
  final MoneyService moneyService;

  MoneyRepositoryImpl({required this.moneyService});

  @override
  Future<List<MoneyStatementModel>> getCashStatementHist({
    required String subAccountId,
    required String fromDate,
    required String toDate,
  }) async {
    try {
      var result = await moneyService.getCashStatementHist(
        subAccountId,
        fromDate,
        toDate,
      );
      return result.data ?? [];
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<TransferAccountModel>> transferAccountList(
    String accountId,
    String transferType,
  ) async {
    try {
      final result = await moneyService.transferAccountList(
        accountId,
        transferType,
      );
      return result.result ?? [];
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  // @override
  // Future<DerivativeAvailableCashOnHand> getDerivativeAvailableCashOnHand(
  //   String accountId,
  // ) async {
  //   try {
  //     final result = await moneyService.getDerivativeAvailableCashOnHand(
  //       accountId,
  //     );
  //     return result.data ?? DerivativeAvailableCashOnHand();
  //   } catch (err) {
  //     throw HandleError.from(err);
  //   }
  // }

  // @override
  // Future<MoneyCheckTransferResponseObj> checkTransfer({
  //   required String subAccount,
  //   required MoneyCheckTransferRequestObj param,
  //   required bool isInternal,
  // }) async {
  //   try {
  //     final result =
  //         isInternal
  //             ? await moneyService.checkInternalTransfer(subAccount, param)
  //             : await moneyService.checkExternalTransfer(subAccount, param);
  //     return result.data ?? MoneyCheckTransferResponseObj();
  //   } catch (err) {
  //     throw HandleError.from(err);
  //   }
  // }

  // @override
  // Future<BaseResponse> derivativeDepositCashOnHand(
  //   String accountId,
  //   DepositCashOnHandParams params,
  // ) async {
  //   try {
  //     return await moneyService.derivativeDepositCashOnHand(
  //       accountId,
  //       params,
  //       AppHelper().genXRequestID(),
  //     );
  //   } catch (err) {
  //     throw HandleError.from(err);
  //   }
  // }

  // @override
  // Future<BondBalanceAmountModel> getBondBalance() async {
  //   try {
  //     final result = await moneyService.getBondBalance(
  //       AppHelper().genXRequestID(),
  //     );
  //     return result.data ?? BondBalanceAmountModel();
  //   } catch (err) {
  //     throw HandleError.from(err);
  //   }
  // }

  // @override
  // Future<BondToBondModel> bondToBondTransfer(
  //   BondToBondTransferRequest request,
  // ) async {
  //   try {
  //     final result = await moneyService.bondToBondTransfer(
  //       request,
  //       AppHelper().genXRequestID(),
  //     );
  //     return result.data ?? BondToBondModel();
  //   } catch (err) {
  //     throw HandleError.from(err);
  //   }
  // }

  // @override
  // Future<BaseResponse> derivativeWithDrawCashOnHand(
  //   String accountId,
  //   DepositCashOnHandParams params,
  // ) {
  //   try {
  //     return moneyService.derivativeWithDrawCashOnHand(
  //       accountId,
  //       params,
  //       AppHelper().genXRequestID(),
  //     );
  //   } catch (err) {
  //     throw HandleError.from(err);
  //   }
  // }

  // @override
  // Future<List<MoneyAccountRecivedObj>> transferAccountDerivativeList(
  //   String accountId,
  //   String custodycd,
  // ) async {
  //   try {
  //     final result = await moneyService.transferAccountDerivativeList(
  //       accountId,
  //       'outernal',
  //       20,
  //       1,
  //       AppHelper().genXRequestID(),
  //       custodycd,
  //     );
  //     return result.data ?? [];
  //   } catch (err) {
  //     throw HandleError.from(err);
  //   }
  // }

  @override
  Future<MoneyFeeAmtObj> feeAmt(String accountId, num amount) async {
    try {
      var result = await moneyService.feeAmt(accountId, amount);
      return result.data ?? MoneyFeeAmtObj();
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<MoneyCashTransferHistResponseObj>> cashTransferHist(
    String accountId,
    String fromDate,
    String toDate,
    String transtype,
  ) async {
    try {
      var result = await moneyService.cashTransferHist(
        accountId,
        fromDate,
        toDate,
        transtype,
      );
      return result.result ?? [];
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<MoneyVirAccount>> getVirAccountList() async {
    try {
      var result = await moneyService.getVirAccountList();
      return result.result ?? [];
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<MoneyBank>> getListBankPartner() async {
    try {
      var result = await moneyService.getListBankPartner(
        AppHelper().genXRequestID(),
        "VPB,MB",
      );
      return result.result ?? [];
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<AccountBalance> getAccountBalance() async {
    try {
      var result = await moneyService.getAccountBalance(
        AppHelper().genXRequestID(),
      );
      return result.data ?? AccountBalance();
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<bool> getCheckLinkRelationWithVPBank() async {
    try {
      var result = await moneyService.getCheckLinkRelationWithVPBank(
        AppHelper().genXRequestID(),
      );
      return result.data["registed"] == "true";
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse<InitalCashInModel>> postInitialCashInInput({
    required num amount,
    required String accountType,
  }) async {
    try {
      final Map<String, dynamic> form = {
        "referenceNumber": AppHelper().genXRequestID(),
        "amount": amount,
        "accountType": accountType,
      };
      var result = await moneyService.postInitialCashInInput(
        AppHelper().genXRequestID(),
        form,
      );
      return result;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse<InitalCashInModel>>
  postConfirmLinkRelationWithVPBank() async {
    try {
      var result = await moneyService.postConfirmLinkRelationWithVPBank(
        AppHelper().genXRequestID(),
      );
      return result;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse<InitalCashInModel>> postInitialLinkRelationWithVPBank(
    BankRegisterInitializeParam bankRegister,
  ) async {
    try {
      var result = await moneyService.postInitialLinkRelationWithVPBank(
        AppHelper().genXRequestID(),
        bankRegister,
      );
      return result;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse> putRegisterLinkRelationWithVPBank() async {
    try {
      var result = await moneyService.putRegisterLinkRelationWithVPBank(
        AppHelper().genXRequestID(),
      );
      return result;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse<AccountCashIn>> putSumbitCashInInput(
    String requestCashId,
  ) async {
    try {
      var result = await moneyService.putSumbitCashInInput(
        AppHelper().genXRequestID(),
        requestCashId,
      );
      return result;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse> postLogStepLinkRelationWithVPBank({
    required StatusWebView statusWebView,
  }) async {
    try {
      var result = await moneyService.postLogStepLinkRelationWithVPBank(
        AppHelper().genXRequestID(),
        statusWebView,
      );
      return result;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<MoneyAvailableCashModel> availableCashTransferOnline(
    String subAccount,
  ) async {
    try {
      var result = await moneyService.getAvailableCashTransferOnline(
        subAccount,
      );
      return result.data ?? MoneyAvailableCashModel();
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse?> cashInternalTransfer(MoneyTransferInRequest request) {
    try {
      return moneyService.cashInternalTransfer(request);
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse?> putDeregister({required String idBank}) {
    try {
      return moneyService.putDeregister(AppHelper().genXRequestID());
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BaseResponse<List<DerivativeAccount>>> getAccounts() {
    return moneyService.getAccounts();
  }

  @override
  Future<BaseResponse<DerivativeStatementOfMoneyResponse>>
  getListStatementOfMoney(StatementOfMoneyParam param) {
    return moneyService.getListStatementOfMoney(
      param.accountId,
      param.pageIndex,
      param.pageSize,
      param.fromDate,
      param.toDate,
    );
  }
}
