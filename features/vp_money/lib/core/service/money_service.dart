import 'package:vp_core/vp_core.dart';
import 'package:vp_money/model/base_model/money_base_paging_response.dart';
import 'package:vp_money/model/request/money_cash_in/bank_register_initialize_param.dart';
import 'package:vp_money/model/request/money_statement/statement_of_money_param.dart';
import 'package:vp_money/model/request/money_transfer/money_transfer_in_request.dart';
import 'package:vp_money/model/response/money_cash_in/account_balance.dart';
import 'package:vp_money/model/response/money_cash_in/account_cash_in.dart';
import 'package:vp_money/model/response/money_cash_in/initial_cash_in_model.dart';
import 'package:vp_money/model/response/money_cash_in/money_bank.dart';
import 'package:vp_money/model/response/money_cash_in/money_vir_account.dart';
import 'package:vp_money/model/response/money_cash_in/webview_status.dart';
import 'package:vp_money/model/response/money_history/money_cash_transfer_hist_obj.dart';
import 'package:vp_money/model/response/money_statement/derivative_account.dart';
import 'package:vp_money/model/response/money_statement/derivative_statement_of_money_model.dart';
import 'package:vp_money/model/response/money_statement/money_statement_model.dart';
import 'package:vp_money/model/response/money_transfer/money_account_recived_obj.dart';
import 'package:vp_money/model/response/money_transfer/money_available_cash_model.dart';
import 'package:vp_money/model/response/money_transfer/money_fee_atm_obj.dart';

part 'money_service.g.dart';

@RestApi()
abstract class MoneyService {
  factory MoneyService(Dio dio, {String baseUrl}) = _MoneyService;

  //* Api moi: Lay danh sach tai khoan thu huong
  @GET(
    "https://neopro-uat.vpbanks.com.vn/neo-inv-customer/public/v1/accounts/transferAccountList",
  )
  Future<BaseResponseMoneyApp<TransferAccountModel>> transferAccountList(
    @Query("accountId") String accountId,
    @Query("transferType") String transferType,
  );

  //* Api moi: Lay so tien toi da
  @GET(
    "https://neopro-uat.vpbanks.com.vn/neo-inv-customer/public/v1/accounts/availableCashTransferOnline",
  )
  Future<BaseResponse<MoneyAvailableCashModel>> getAvailableCashTransferOnline(
    @Query("accountId") String accountId,
  );

  // @GET(
  //   "/derivative/external/v1/inquiry/accounts/{accountId}/cash/avlCashOnHand",
  // )
  // Future<BaseResponse<DerivativeAvailableCashOnHand>>
  // getDerivativeAvailableCashOnHand(@Path("accountId") String accountId);

  // Check chuyen tien noi bo
  // @POST("/flex/tran/accounts/{subAccount}/checkInternalTransfer")
  // Future<BaseResponse<MoneyCheckTransferResponseObj>> checkInternalTransfer(
  //   @Path("subAccount") String subAccount,
  //   @Body() MoneyCheckTransferRequestObj param,
  // );
  // Check chuyen tien noi bo
  // @POST("/flex/tran/accounts/{subAccount}/externalTransfer")
  // Future<BaseResponse<MoneyCheckTransferResponseObj>> checkExternalTransfer(
  //   @Path("subAccount") String subAccount,
  //   @Body() MoneyCheckTransferRequestObj param,
  // );

  // // Giao dic chuyen tien noi bo
  // @POST("/flex/tran/accounts/{subAccount}/internalTransfer")
  // Future<BaseResponse> internalTransfer(
  //   @Path("subAccount") String subAccount,
  //   @Body() Map<String, dynamic> map,
  // );

  // @POST(
  //   "/derivative/external/v1/transaction/accounts/{accountId}/withdrawCashonhand",
  // )
  // Future<BaseResponse> derivativeWithDrawCashOnHand(
  //   @Path("accountId") String accountId,
  //   @Body() DepositCashOnHandParams params,
  //   @Header('x-request-id') String requestId,
  // );

  // @POST(
  //   "/derivative/external/v1/transaction/accounts/{accountId}/depositCashonhand",
  // )
  // Future<BaseResponse> derivativeDepositCashOnHand(
  //   @Path("accountId") String accountId,
  //   @Body() DepositCashOnHandParams params,
  //   @Header('x-request-id') String requestId,
  // );

  // /// new list bond APIs from BE
  // @GET(
  //   '/cash-api/api/v1/cash-bond/cash-accounting/transaction/bond/getAvailable',
  // )
  // Future<BaseResponse<BondBalanceAmountModel>> getBondBalance(
  //   @Header('x-request-id') String requestId,
  // );

  //check chuyển tiền từ BOND sang FCBOND
  // @POST('/cash-api/api/v1/cash-bond/transaction/transfer/bond-to-bond/app')
  // Future<BaseResponse<BondToBondModel>> bondToBondTransfer(
  //   @Body() BondToBondTransferRequest request,
  //   @Header('x-request-id') String requestId,
  // );

  // Chuyen tien ra ngoai Phai Sinh
  // @POST('/cash-api/api/v1/external/fds/{accountId}/externalTransfer')
  // Future<BaseResponse?> externalTransferDerivative(
  //   @Path("accountId") String accountId,
  //   @Body() Map<String, dynamic> map,
  //   @Header('x-request-id') String requestId,
  //   @Header('custodyCd') String custodyCd,
  // );

  // -> giao dich chuyen tien ra ngoai
  // @POST('/flex/tran/accounts/{accountId}/externalTransfer')
  // Future<BaseResponse> externalTransfer(
  //   @Path("accountId") String accountId,
  //   @Body() Map<String, dynamic> map,
  // );

  // @POST("/cash-api/api/v1/external/fds/{accountId}/transferAccountList")
  // // Lay danh sach tai khoan thu huong Phai Sinh
  // Future<BaseResponseMoneyApp<MoneyAccountRecivedObj>>
  // transferAccountDerivativeList(
  //   @Path("accountId") String accountId,
  //   @Query('transferType') String transferType,
  //   @Query('pageSize') int pageSize,
  //   @Query('pageIndex') int pageIndex,
  //   @Header('x-request-id') String requestId,
  //   @Header('custodyCd') String custodyCd,
  // );
  // Lay phi chuyen tien
  @GET("/flex/inq/accounts/{accountId}/externalTransferFee")
  Future<BaseResponse<MoneyFeeAmtObj>> feeAmt(
    @Path("accountId") String accountId,
    @Query('amount') num amount,
  );

  //* api mới: Lich su chuyen tien
  @GET(
    "https://neopro-uat.vpbanks.com.vn/neo-inv-cash/public/v1/trans/cashTransferHist",
  )
  Future<BaseResponseMoneyApp<MoneyCashTransferHistResponseObj>>
  cashTransferHist(
    @Query("accountId") String accountId,
    @Query('fromDate') String fromDate,
    @Query('toDate') String toDate,
    @Query('transferType') String transtype,
  );

  //  //* api mới:  Lấy thông tin tk qrcode màn nộp tiền
  @GET(
    "https://neopro-uat.vpbanks.com.vn/neo-inv-customer/public/v1/accounts/virAccountList",
  )
  Future<BaseResponseMoneyApp<MoneyVirAccount>> getVirAccountList();

  // Path lay danh sach bank va bank da lien ket voi tai khoan cua minh
  @GET("/cash-api/api/v1/cash-neo/delegations/banks")
  Future<BaseResponseMoneyApp<MoneyBank>> getListBankPartner(
    @Header('x-request-id') String requestId,
    @Query("partner") String partner,
  );

  @GET("/cash-api/api/v1/cash-neo/delegations/get-balance")
  Future<BaseResponse<AccountBalance>> getAccountBalance(
    @Header('x-request-id') String requestId,
  );

  // Path check trang thai lien ket cua tai khoan
  @GET("/cash-api/api/v1/cash-neo/delegations/register-get-status")
  Future<BaseResponse> getCheckLinkRelationWithVPBank(
    @Header('x-request-id') String requestId,
  );

  // Path khoi tao thu ho tu phia bank
  @POST("/cash-api/api/v1/cash-neo/delegations/cashin-init")
  Future<BaseResponse<InitalCashInModel>> postInitialCashInInput(
    @Header('x-request-id') String requestId,
    @Body() Map<String, dynamic> request,
  );

  // Path xac thuc thong tin lien ket
  @GET("/cash-api/api/v1/cash-neo/delegations/register-authenticate")
  Future<BaseResponse<InitalCashInModel>> postConfirmLinkRelationWithVPBank(
    @Header('x-request-id') String requestId,
  );

  // Path khoi tao thong tin lien ket
  @POST('/cash-api/api/v1/cash-neo/delegations/register-initialize')
  Future<BaseResponse<InitalCashInModel>> postInitialLinkRelationWithVPBank(
    @Header('x-request-id') String requestId,
    @Body() BankRegisterInitializeParam bankRegister,
  );

  // Path lay log step fail
  @POST('/cash-api/api/v1/cash-neo/delegations/register-update-status')
  Future<BaseResponse> postLogStepLinkRelationWithVPBank(
    @Header('x-request-id') String requestId,
    @Body() StatusWebView request,
  );

  // Path dang ky lien ket
  @PUT('/cash-api/api/v1/cash-neo/delegations/register-submit')
  Future<BaseResponse> putRegisterLinkRelationWithVPBank(
    @Header('x-request-id') String requestId,
  );

  // Path gui yeu cau thu ho
  @PUT('/cash-api/api/v1/cash-neo/delegations/cashin-submit/{requestCashId}')
  Future<BaseResponse<AccountCashIn>> putSumbitCashInInput(
    @Header('x-request-id') String requestId,
    @Path("requestCashId") String requestCashId,
  );

  //* API mới: Chuyển tiền nội bộ
  @POST(
    'https://neopro-uat.vpbanks.com.vn/neo-inv-cash/public/v1/trans/cashInternalTransfer',
  )
  Future<BaseResponse?> cashInternalTransfer(
    @Body() MoneyTransferInRequest request,
  );

  // Path Huy trang thai lien ket cua tai khoan
  @PUT('/cash-api/api/v1/cash-neo/delegations/deregister')
  Future<BaseResponse?> putDeregister(@Header('x-request-id') String requestId);

  // Lich su giao dich tien
  @GET(
    "/flex/report/accounts/{idSubAccount}/cashStatementHist?fromDate={fromDate}&toDate={toDate}",
  )
  Future<BaseResponse<List<MoneyStatementModel>>> getCashStatementHist(
    @Path("idSubAccount") String idSubAccount,
    @Path("fromDate") String fromDate,
    @Path("toDate") String toDate,
  );

  @GET("/derivative/external/v1/inquiry/accounts")
  Future<BaseResponse<List<DerivativeAccount>>> getAccounts();

  @GET(
    "/derivative/external/v1/report/accounts/{accountId}/cashOnHandHist?fromDate={fromDate}&toDate={toDate}&pageSize={pageSize}&pageIndex={pageIndex}",
  )
  Future<BaseResponse<DerivativeStatementOfMoneyResponse>>
  getListStatementOfMoney(
    @Path("accountId") String accountId,
    @Path("pageIndex") int pageIndex,
    @Path("pageSize") int pageSize,
    @Path("fromDate") String fromDate,
    @Path("toDate") String toDate,
  );
}
