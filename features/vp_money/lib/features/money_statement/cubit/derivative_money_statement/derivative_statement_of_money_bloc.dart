import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_money/core/repository/money_repository.dart';
import 'package:vp_money/features/money_statement/view/derivative_money_statement/dialog/system_process_derivative_account.dart';
import 'package:vp_money/model/request/money_statement/statement_of_money_param.dart';
import 'package:vp_money/model/response/money_statement/adapter/money_statement_adapter.dart';
import 'package:vp_money/model/response/money_statement/derivative_account.dart';
import 'package:vp_money/model/response/money_statement/derivative_statement_of_money_model.dart';
import 'package:vp_money/model/response/money_statement/statement_of_money_entity.dart';
import 'package:vp_money/core/constant/constant_app.dart';
part 'derivative_statement_of_money_state.dart';

class DerivativeStatementOfMoneyBloc
    extends Cubit<DerivativeStatementOfMoneyState> {
  DerivativeStatementOfMoneyBloc() : super(DerivativeStatementOfMoneyState());

  final _repository = GetIt.instance<MoneyRepository>();

  int _pageIndex = 1;
  final int _pageSize = ****************;
  int _currentLen = 0;
  bool hasLoadMore = true;
  String fromDate = '';
  String toDate = '';
  String? idDerivativeAccount =
      GetIt.instance<SubAccountCubit>().derivativeAccount?.id;

  List<StatementOfMoneyEntity> moneyStatements = [];
  List<StatementOfMoneyEntity> listItemStatement = [];

  List<StatementOfMoneyEntity> listStatement = [];

  Future<void> initFilterDefault() async {
    await getDerivativeAccount();
  }

  Future<void> loadDataWhenSearch() async {
    if (state.isLoading) return;
    await initFilterDefault();
    onLoadData();
  }

  Future<void> clearData() async {
    moneyStatements.clear();
    listItemStatement.clear();
  }

  Future<void> getDerivativeAccount() async {
    try {
      final result = await _repository.getAccounts();
      if (result.isSuccess) {
        idDerivativeAccount = result.data?.firstOrNull?.afacctno;
      } else {
        switch (result.code) {
          case 'DEERR50000':
          case 'DEERR401':
          case 'DEERR504':
            showSystemProcessDerivativeAccountDialog(context: getContext);
            break;
        }
      }
    } catch (e) {
      showError(e);
    }
  }

  void onLoadData({bool isLoadMore = false}) async {
    await getListDerivativeStatement(isLoadMore: isLoadMore);
  }

  Future<List<DerivativeStatementOfMoneyModel>?> getListDerivativeStatement({
    bool isLoadMore = false,
  }) async {
    try {
      if (!isLoadMore) {
        emit(state.copyWith(isLoading: true));
        _pageIndex = 1;
      }
      final param = _createParam();
      final result = await _repository.getListStatementOfMoney(param);
      if (result.isSuccess) {
        return onGetListSuccess1(
          result: result.data?.content ?? [],
          isLoadMore: isLoadMore,
        );
      } else {
        if (!isLoadMore) listStatement.clear();
        emit(state.copyWith(isLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isLoading: false));
      showError(error);
    }
    return null;
  }

  StatementOfMoneyParam _createParam() {
    return StatementOfMoneyParam(
      accountId: idDerivativeAccount ?? derivativeAccountIdDontUsing,
      pageIndex: _pageIndex,
      pageSize: _pageSize,
      fromDate: fromDate,
      toDate: toDate,
    );
  }

  Future<List<DerivativeStatementOfMoneyModel>> onGetListSuccess({
    required BaseResponse<List<DerivativeStatementOfMoneyModel>> result,
  }) async {
    _currentLen = (result.data ?? []).length;
    final entityList = _mapMoneyStatement(result.data ?? []);

    List<StatementOfMoneyEntity> filteredData =
        entityList.where((item) {
          return item.txdate != null &&
              item.txtime != null &&
              item.txnum != null;
        }).toList();
    await clearData();
    listItemStatement.addAll(filteredData);
    moneyStatements.addAll(entityList);
    emit(
      state.copyWith(
        isLoading: false,
        listStatementOfMoney: moneyStatements,
        listItemStatement: listItemStatement,
      ),
    );
    return result.data ?? [];
  }

  Future<List<DerivativeStatementOfMoneyModel>> onGetListSuccess1({
    required List<DerivativeStatementOfMoneyModel> result,
    required bool isLoadMore,
  }) async {
    _currentLen = result.length;

    final newEntityList = _mapMoneyStatement(result);
    final newFilteredData =
        newEntityList
            .where(
              (item) =>
                  item.txdate != null &&
                  item.txtime != null &&
                  item.txnum != null,
            )
            .toList();

    if (isLoadMore) {
      final existingIds = listStatement.map((e) => e.txnum).toSet();
      listStatement.addAll(
        newEntityList.where((item) => !existingIds.contains(item.txnum)),
      );
      listItemStatement.addAll(
        newFilteredData.where((item) => !existingIds.contains(item.txnum)),
      );
    } else {
      listStatement = newEntityList;
      listItemStatement = newFilteredData;
    }
    // listItemStatement = await _sortDataByDateAndTime(listItemStatement);

    emit(
      state.copyWith(
        isLoading: false,
        listStatementOfMoney: listStatement,
        listItemStatement: listItemStatement,
      ),
    );
    return result;
  }

  num beginCodeTransaction() {
    // Find the transaction with desc == "BEGIN_CODE"
    StatementOfMoneyEntity? beginCodeTransaction = state.listStatementOfMoney
        ?.firstWhere(
          (transaction) => transaction.desc == "BEGIN_CODE",
          orElse:
              () => StatementOfMoneyEntity(
                desc: "Not found",
                credit: 0,
                debit: 0,
                balance: 0,
              ),
        );
    return beginCodeTransaction?.balance ?? 0;
  }

  num endCodeTransaction() {
    // Find the transaction with desc == "END_CODE"
    StatementOfMoneyEntity? endCodeTransaction = state.listStatementOfMoney
        ?.firstWhere(
          (transaction) => transaction.desc == "END_CODE",
          orElse:
              () => StatementOfMoneyEntity(
                desc: "Not found",
                credit: 0,
                debit: 0,
                balance: 0,
              ),
        );
    return endCodeTransaction?.balance ?? 0;
  }

  num getOccurredDuringPeriod() {
    return endCodeTransaction() - beginCodeTransaction();
  }

  List<StatementOfMoneyEntity> _mapMoneyStatement(
    List<DerivativeStatementOfMoneyModel> modelList,
  ) {
    return List.generate(
      modelList.length,
      (index) => MoneyStatementAdapter(modelList[index]).genderForUi(),
    );
  }

  Future onLoadMore() async {
    if (!hasLoadMore || state.isLoading) {
      return;
    }
    emit(state.copyWith(isLoading: true));
    if (_currentLen < _pageSize) {
      hasLoadMore = false;
    }
    _pageIndex++;
    onLoadData(isLoadMore: true);
    emit(state.copyWith(isLoading: false));
  }

  Future onRefresh() async {
    _pageIndex = 1;
    hasLoadMore = true;
    moneyStatements.clear();
    listItemStatement.clear();
    listStatement.clear();
    onLoadData();
  }
}
