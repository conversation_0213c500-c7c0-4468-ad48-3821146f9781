import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_money/features/money_history/enum/money_transfer_status_enum.dart';
import 'package:vp_money/generated/l10n.dart';

part 'money_cash_transfer_hist_obj.g.dart';

@JsonSerializable()
class MoneyCashTransferHistResponseObj {
  @Json<PERSON>ey(name: 'transDate')
  String? txdate;

  @JsonKey(name: 'fromAccount')
  String? fracctno;

  @Json<PERSON><PERSON>(name: 'benefitAccount')
  String? toacctno;

  @Json<PERSON><PERSON>(name: 'transType')
  String? typeci;

  @JsonKey(name: 'amount')
  num? amt;

  @Json<PERSON>ey(name: 'status')
  String? rmstatus;

  @JsonKey(name: 'benefitBankName')
  String? benefbank;

  @Json<PERSON>ey(name: 'benefitAcctName')
  String? benefcustname;

  @Json<PERSON>ey(name: 'benefitBankCode')
  String? bankcode;

  @Json<PERSON>ey(name: 'busDate')
  String? busdate;

  @JsonKey(name: 'txNum')
  String? txnum;

  @JsonKey(name: 'description')
  String? description;

  MoneyCashTransferHistResponseObj({
    this.txdate,
    this.fracctno,
    this.toacctno,
    this.typeci,
    this.amt,
    this.rmstatus,
    this.benefbank,
    this.benefcustname,
    this.bankcode,
    this.busdate,
    this.txnum,
    this.description,
  });

  factory MoneyCashTransferHistResponseObj.fromJson(
    Map<String, dynamic> json,
  ) => _$MoneyCashTransferHistResponseObjFromJson(json);

  Map<String, dynamic> toJson() =>
      _$MoneyCashTransferHistResponseObjToJson(this);

  final typeIN = 'IN';
  final typeOUT = 'OUT';
  final typeINT = 'INT';
  // Loại giao dịch
  // typeci
  // IN: Nộp tiền
  // INTChuyển tiền nội bộ
  // OUT: Chuyển tiền ra ngân hàng
  // Kieu giao dịch: Nop tien, rut tien, noi bo
  String getTypeTransfer(BuildContext context) {
    if (typeci != null) {
      if (compare(a: typeci, b: typeIN)) {
        return S.current.money_input_money;
      }

      if (compare(a: typeci, b: typeOUT)) {
        return S.current.money_output_money;
      }

      if (compare(a: typeci, b: typeINT)) {
        return S.current.money_internal_money;
      }
    }
    return '';
  }

  // Thoi gian chuyen tien
  String getTimeTransfer() {
    if (txdate == null || txdate!.isEmpty) return '';
    try {
      final dateTime = DateTime.parse(txdate!).toLocal();
      final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);
      return AppTimeUtils.formatTime(
        dateOnly.toString(),
        format: AppTimeUtilsFormat.dateNormal,
      );
    } catch (e) {
      return '';
    }
  }

  // So tien da giao dich
  String getMoneyTransfer() {
    return MoneyUtils.formatMoney(amt?.toDouble());
  }

  // Kiem tra bang nhau hay khong
  bool compare({String? a, String? b}) {
    return a?.toLowerCase() == b?.toLowerCase();
  }

  // Lay tai khoan chuyen
  String getFromAcc() {
    if (typeci != null) {
      if (compare(a: typeci, b: typeIN)) {
        return '-';
      }
      return fracctno ?? '';
    }
    return '';
  }

  // Lay tai khoan nhan
  String getToAcc() {
    if (typeci != null) {
      if (compare(a: typeci, b: typeOUT)) {
        return '$bankcode - $benefcustname';
      }
      return toacctno ?? '';
    }
    return '';
  }

  MoneyTransferStatusEnum? get getStatusFromText =>
      moneyTransferStatusFromText(rmstatus ?? '');
}
