class DerivativeStatementOfMoneyResponse {
  DerivativeStatementOfMoneyResponse({
    this.totalPage,
    this.totalItem,
    this.content,
    this.pageSize,
    this.pageCurrent,
  });

  DerivativeStatementOfMoneyResponse.fromJson(dynamic json) {
    totalPage = json['totalPage'];
    totalItem = json['totalItem'];
    if (json['content'] != null) {
      content = [];
      json['content'].forEach((v) {
        content?.add(DerivativeStatementOfMoneyModel.fromJson(v));
      });
    }
    pageSize = json['pageSize'];
    pageCurrent = json['pageCurrent'];
  }

  int? totalPage;
  int? totalItem;
  List<DerivativeStatementOfMoneyModel>? content;
  int? pageSize;
  int? pageCurrent;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['totalPage'] = totalPage;
    map['totalItem'] = totalItem;
    if (content != null) {
      map['content'] = content?.map((v) => v.toJson()).toList();
    }
    map['pageSize'] = pageSize;
    map['pageCurrent'] = pageCurrent;
    return map;
  }
}

class DerivativeStatementOfMoneyModel {
  String? txdate;
  String? txtime;
  String? txnum;
  String? desc;
  int? credit;
  int? debit;
  int? balance;
  String? txdesc;

  DerivativeStatementOfMoneyModel({
    this.txdate,
    this.txtime,
    this.txnum,
    this.desc,
    this.credit,
    this.debit,
    this.balance,
    this.txdesc,
  });

  DerivativeStatementOfMoneyModel.fromJson(Map<String, dynamic> json) {
    txdate = json['txdate'];
    txtime = json['txtime'];
    txnum = json['txnum'];
    desc = json['desc'];
    credit = json['credit'];
    debit = json['debit'];
    balance = json['balance'];
    txdesc = json['txdesc'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['txdate'] = this.txdate;
    data['txtime'] = this.txtime;
    data['txnum'] = this.txnum;
    data['desc'] = this.desc;
    data['credit'] = this.credit;
    data['debit'] = this.debit;
    data['balance'] = this.balance;
    data['txdesc'] = this.txdesc;
    return data;
  }

  static List<DerivativeStatementOfMoneyModel> fromListJson(dynamic json) {
    final list = <DerivativeStatementOfMoneyModel>[];
    final array = json?['content'] ?? [];
    array?.map((v) {
      list.add(DerivativeStatementOfMoneyModel.fromJson(v));
    }).toList();
    return list;
  }
}
