enum VpPriceBoardRouter {
  priceBoard('/noauth-priceBoard');

  final String routeName;

  const VpPriceBoardRouter(this.routeName);
}

enum PriceBoardRouterName {
  priceBoard('/noauth-priceBoard'),
  mainStockHome('/mainStockHome'),
  signIn('/signIn'),
  stockDetail('/noauth-stockDetail'),
  marketDetail('/market'),
  fuStockDetail('/noauth-fuStockDetail'),
  codePilotGetToken('/code_pilot_get_token'),
  market('/market');

  final String routeName;

  const PriceBoardRouterName(this.routeName);
}
