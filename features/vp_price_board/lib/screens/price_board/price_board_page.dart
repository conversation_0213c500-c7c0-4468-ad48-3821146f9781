import 'package:flutter/material.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/model/enum/priceboard_tab.dart';
import 'package:vp_price_board/screens/price_board/price_board_stack_page.dart';
import 'package:vp_price_board/vp_price_board_navigator.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

import 'tabs/price_board_tab_view.dart';

class PriceBoardPage extends StatefulWidget {
  const PriceBoardPage({super.key});

  @override
  State<PriceBoardPage> createState() => _PriceBoardPageState();
}

class _PriceBoardPageState extends State<PriceBoardPage>
    with
        AppLifecycleMixin<PriceBoardPage>,
        SingleTickerProviderStateMixin,
        MarketEventSocketMixin {
  int key = 0;

  PriceBoardTabViewEnum? tab;

  late TabController tabController = TabController(
    length: PriceBoardTabViewEnum.values.length,
    vsync: this,
  );

  final throttle = Throttle(const Duration(seconds: 2));

  @override
  void initState() {
    super.initState();

    subscribeMarketEvent();

    tabController.addListener(tabListener);

    context.read<WatchlistBloc>().refresh();
  }

  void tabListener() {
    final index = tabController.index;

    tab = PriceBoardTabViewEnum.values.getElementAt(index);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (pState, state) => pState.isLoggedIn != state.isLoggedIn,
      listener: (_, state) {
        onRefresh();
      },
      child: PriceBoardStackPage(
        child: VPScaffold(
          backgroundColor: vpColor.backgroundElevationMinus1,
          appBar: VPAppBar.layer(
            title: 'Bảng giá',
            leading: BlocBuilder<AuthCubit, AuthState>(
              builder: (context, state) {
                return state.status != AuthStatus.logined
                    ? IconButton(
                      onPressed: () async {
                        context.push('/signIn');
                      },
                      icon: Icon(Icons.person, color: themeData.gray500),
                    )
                    : IconButton(
                      onPressed: () {
                        context.safePop();
                      },
                      icon: DesignAssets.icons.appbar.icBack.svg(
                        colorFilter: ColorFilter.mode(
                          vpColor.iconPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                    );
              },
            ),
            actions: [
              IconButton(
                onPressed: () => openSearchPage(context),
                icon: DesignAssets.icons.icSearch.svg(
                  colorFilter: ColorFilter.mode(
                    vpColor.iconPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ],
          ),
          body: Column(
            key: ValueKey(key),
            children: [
              const SizedBox(height: 12),

              MarketIndexList.simple(
                onTap: (market) => onOpenMarketDetailPage(market),
              ),

              const SizedBox(height: 12),

              Expanded(child: PriceBoardTabView(controller: tabController)),
            ],
          ),
        ),
      ),
    );
  }

  void onOpenMarketDetailPage(MarketInfoModel market) {
    stockCommonNavigator.openMarketDetailPage(
      context,
      args: MarketDetailArgs(indexCode: market.indexCode),
    );
  }

  Future openSearchPage(BuildContext context) {
    if (tab == PriceBoardTabViewEnum.bond) {
      return stockCommonNavigator.openBondSearchPage(context);
    }

    return priceBoardNavigator.openSearchPage(
      context,
      args: SearchArgs(
        marketCodes: [
          MarketCode.HOSE,
          MarketCode.HNX,
          MarketCode.UPCOM,
          MarketCode.FU,
        ],
        itemAction: SearchItemAction.openDetail,
      ),
    );
  }

  @override
  void onEnterForeground() {
    super.onEnterForeground();

    onRefresh();
  }

  @override
  void onSocketMarketEventListener(VPMarketEventData data) {
    if (data.isPreOpen) onRefresh();
  }

  @override
  void networkUp() {
    super.networkUp();

    onRefresh();
  }

  void onRefresh() {
    throttle.call(() {
      if (mounted) setState(() => key++);
    });
  }

  @override
  void dispose() {
    throttle.dispose();
    tabController.removeListener(tabListener);
    tabController.dispose();
    unsubscribeMarketEvent();

    super.dispose();
  }
}

extension NavigationExtensions on BuildContext {
  void safePop() {
    // If there is only one route on the stack, navigate to the initial
    // page instead of popping.
    if (canPop()) {
      pop();
    } else {
      go('/mainTabbar');
    }
  }
}
