name: vp_price_board
description: "A new Flutter project."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  retrofit:
  logger:
  json_annotation:
  dio: 5.8.0+1
  json_serializable:
  intl: ^0.19.0


  vp_design_system:
    path: ../../library/vp_design_system
  vp_common:
    path: ../../library/vp_common
  vp_core:
    path: ../../library/vp_core
  vp_stock_common:
    path: ../../library/vp_stock_common

  fl_chart: 0.71.0
  flutter_bloc:
  equatable:
  go_router: ^15.1.1
  syncfusion_flutter_charts: 31.1.19

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: 5.0.0
  retrofit_generator:
  build_runner:
  flutter_gen_runner:
  intl_utils:

flutter_intl:
  enabled: true
  class_name: S

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
