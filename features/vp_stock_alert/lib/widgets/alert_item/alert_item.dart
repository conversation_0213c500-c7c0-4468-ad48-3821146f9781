import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/popup/popup.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_alert/gen/assets.gen.dart';
import 'package:vp_stock_alert/generated/l10n.dart';
import 'package:vp_stock_alert/model/stock_alert_entity.dart';
import 'package:vp_stock_alert/pages/stock_alert/bloc/alert_config/alert_config_cubit.dart';
import 'package:vp_stock_alert/pages/stock_alert/bloc/stock_alert/stock_alert_cubit.dart';

import '../config_bottom_dialog/stock_alert_bottom_dialog.dart';
import 'alert_board.dart';

class AlertItem extends StatelessWidget {
  final StockAlertEntity item;

  const AlertItem({super.key, required this.item});

  void onTapItem(BuildContext context) => NoDuplicate(() {
    final stockAlertCubit = context.read<StockAlertCubit>();
    stockAlertCubit.getReferencePrice(
      item.symbol,
      onResult: (referencePrice) {
        final alertConfigCubit = AlertConfigCubit(
          entity: item,
          referencePrice: referencePrice,
        );
        return PopupBottomSheet(
              BlocProvider(
                create: (_) => alertConfigCubit,
                child: const StockAlertBottomDialog(),
              ),
              buttonSpacing: 16,
              marginTop: 0.0,
            )
            .button(
              Build.outline(
                label: VPStockAlertLocalize.current.relay,
                onPressed: alertConfigCubit.onReset,
              ),
            )
            .button(
              BlocBuilder<AlertConfigCubit, AlertConfigState>(
                bloc: alertConfigCubit,
                builder:
                    (_, state) => Build.normal(
                      label: VPStockAlertLocalize.current.save,
                      onPressed: () => context.pop(state.edit),
                      enable: state.isEnableSave,
                    ),
              ),
            )
            .show(context)
            .then(
              (value) =>
                  value != null ? stockAlertCubit.updateAlertItem(value) : null,
            );
      },
    );
  });

  @override
  Widget build(BuildContext context) {
    final stockAlertCubit = context.read<StockAlertCubit>();
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: GestureDetector(
            onTap: () => onTapItem(context),
            behavior: HitTestBehavior.translucent,
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.symbol,
                        style: vpTextStyle.subtitle14.copyColor(
                          vpColor.textPrimary,
                        ),
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          SvgPicture.asset(
                            VPStockAlertAssets.icons.icAlarm.path,
                            package: VPStockAlertAssets.package,
                            width: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            item.time,
                            style: vpTextStyle.captionMedium?.copyWith(
                              color: themeData.gray700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                CircleAvatar(
                  backgroundColor: themeData.highlightBg,
                  radius: 16,
                  child: IconButton(
                    onPressed: () => onTapItem(context),
                    icon: SvgPicture.asset(
                      VPStockAlertAssets.icons.icEditAlt.path,
                      package: VPStockAlertAssets.package,
                      width: 16,
                    ),
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                  ),
                ),
                const SizedBox(width: 8),
                CircleAvatar(
                  backgroundColor: themeData.highlightBg,
                  radius: 16,
                  child: IconButton(
                    onPressed: () {
                      PopupDialog(
                            title:
                                '${VPStockAlertLocalize.current.utils_stockAlert_clearWarning} - ${item.symbol}',
                            content:
                                VPStockAlertLocalize
                                    .current
                                    .utils_stockAlert_clearWarningConfirm,
                          ).trash.close
                          .button(
                            Build.normal(
                              label: VPStockAlertLocalize.current.agree,
                              onPressed: () {
                                context.pop();
                                stockAlertCubit.deleteAlert(item);
                              },
                              colorEnable: themeData.red,
                            ),
                          )
                          .show(context);
                    },
                    icon: SvgPicture.asset(
                      VPStockAlertAssets.icons.icTrashOutline.path,
                      package: VPStockAlertAssets.package,
                      width: 16,
                    ),
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                  ),
                ),
                const SizedBox(width: 8),
                CupertinoSwitch(
                  value: item.status,
                  activeColor: themeData.primary,
                  trackColor: themeData.bgButtonSwitch,
                  onChanged:
                      (status) => stockAlertCubit.changeStatus(status, item),
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () => onTapItem(context),
          behavior: HitTestBehavior.translucent,
          child: AlertBoard(item: item),
        ),
        const SizedBox(height: 12),
      ],
    );
  }
}
