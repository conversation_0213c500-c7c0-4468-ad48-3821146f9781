import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_alert/model/enum/config_tab.dart';

class AlertTab extends StatefulWidget {
  final ValueSetter<int> onIndexChange;

  const AlertTab({super.key, required this.onIndexChange});

  @override
  TabBarState createState() => TabBarState();
}

class TabBarState extends State<AlertTab> with SingleTickerProviderStateMixin {
  late final _tabController = TabController(
    length: ConfigTab.values.length,
    vsync: this,
  );

  @override
  Widget build(BuildContext context) => SizedBox(
    width: double.infinity,
    child: TabBar(
      isScrollable: true,
      controller: _tabController,
      onTap: widget.onIndexChange,
      labelPadding: const EdgeInsets.symmetric(horizontal: 8),
      unselectedLabelStyle: vpTextStyle.body14,
      labelStyle: vpTextStyle.subtitle14,
      tabs:
          ConfigTab.values
              .map((configTab) => Tab(text: configTab.tabName))
              .toList(),
    ),
  );

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
