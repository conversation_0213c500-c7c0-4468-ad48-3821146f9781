enum VPStockDetailRouter {
  stockDetail('/noauth-stockDetail'),
  fuStockDetail('/noauth-fuStockDetail'),
  tradingViewFullScreen('/noauth-tradingView');

  final String routeName;

  const VPStockDetailRouter(this.routeName);
}

enum StockDetailRouterName {
  signIn('/signIn'),
  search('/noauth-search'),
  placeOrder('/placeOrder'),
  stockDetail('/noauth-stockDetail'),
  fuStockDetail('/noauth-fuStockDetail'),
  tradingViewFullScreen('/noauth-tradingView'),
  pdfOrWebView('/noauth-pdfOrWebView'),
  codePilotGetToken('/code_pilot_get_token'),
  market('/market');

  final String routeName;

  const StockDetailRouterName(this.routeName);
}
