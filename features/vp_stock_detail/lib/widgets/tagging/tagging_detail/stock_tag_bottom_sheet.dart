import 'package:flutter/material.dart';
import 'package:stock_detail/model/stock_by_tag_model.dart';
import 'package:stock_detail/model/stock_tagging_model.dart';
import 'package:stock_detail/widgets/tagging/tagging_detail/stock_tag_cubit.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class StockTagBottomSheet extends StatelessWidget {
  const StockTagBottomSheet({required this.tag, super.key});

  final StockTaggingModel tag;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<StockTagCubit>(
      create: (_) => StockTagCubit(tag: tag)..loadData(),
      child: BlocBuilder<StockTagCubit, StockTagState>(
        builder: (BuildContext context, StockTagState state) {
          final symbols = state.symbols;

          final title =
              state.isIcbCodeLevel2
                  ? 'Nghành ${state.tag.name.toLowerCase()}'
                  : state.tag.name;

          return VPStatusBuilder(
            apiStatus: state.apiStatus,
            builder: (context, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: vpTextStyle.subtitle16.copyColor(
                      vpColor.textPrimary,
                    ),
                  ),

                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Mã',
                          style: vpTextStyle.captionRegular.copyColor(
                            vpColor.textTertiary,
                          ),
                        ),
                      ),

                      if (state.showVol)
                        Expanded(
                          child: Text(
                            'GT bán ròng (tỷ đồng)',
                            textAlign: TextAlign.end,
                            style: vpTextStyle.captionRegular.copyColor(
                              vpColor.textTertiary,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  Expanded(
                    child: ListView.separated(
                      separatorBuilder:
                          (_, index) => const SizedBox(height: 25),
                      itemBuilder: (_, index) {
                        final symbol = symbols![index];

                        return _StockTagItemView(
                          showVol: state.showVol,
                          stockInfoModel: state.stockDetails![symbol]!,
                          stockByTagModel: state.stockListByTag?[symbol],
                        );
                      },
                      itemCount: symbols?.length ?? 0,
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}

class _StockTagItemView extends StatelessWidget {
  const _StockTagItemView({
    required this.stockByTagModel,
    required this.stockInfoModel,
    this.showVol = false,
  });

  final StockByTagModel? stockByTagModel;

  final StockInfoModel stockInfoModel;

  final bool showVol;

  num? get vol => stockByTagModel?.value;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 10,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                spacing: 5,
                children: [
                  Text(
                    stockInfoModel.symbol,
                    style: vpTextStyle.subtitle14.copyColor(
                      vpColor.textPrimary,
                    ),
                  ),

                  VPPriceChangeItemView.stock(
                    stock: stockInfoModel,
                    undefineWhenClosePriceNull: false,
                    changeBuilder: (priceChange, closePrice) {
                      return FormatUtils.formatClosePrice(
                            priceChange,
                            showSign: false,
                          ) ??
                          '0.00';
                    },
                  ),

                  VPProfitIconView.stock(stock: stockInfoModel),

                  VPPercentChangeItemView.stock(
                    stock: stockInfoModel,
                    undefineWhenClosePriceNull: false,
                    percentBuilder: (percent, closePrice) {
                      return FormatUtils.formatPercent(
                            percent,
                            showPositiveSign: false,
                          ) ??
                          '0.00%';
                    },
                  ),
                ],
              ),
              Text(
                stockInfoModel.stockName,
                style: vpTextStyle.captionRegular.copyColor(
                  vpColor.textPrimary,
                ),
              ),
            ],
          ),
        ),

        if (vol != null && showVol)
          Container(
            height: 40,
            alignment: Alignment.center,
            constraints: const BoxConstraints(minWidth: 120),
            padding: const EdgeInsets.fromLTRB(24, 12, 24, 12),
            decoration: BoxDecoration(
              color: vpColor.backgroundElevation1,
              borderRadius: BorderRadius.circular(12),
            ),
            child: AutoSizeText(
              FormatUtils.formatVolWithTrailing(vol!),
              style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
              minFontSize: 8,
              maxLines: 14,
            ),
          ),
      ],
    );
  }
}
