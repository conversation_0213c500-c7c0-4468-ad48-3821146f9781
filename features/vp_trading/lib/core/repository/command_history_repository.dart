import 'package:vp_core/base/base_response/base_paging_response.dart';
import 'package:vp_core/base/base_response/base_response.dart';
import 'package:vp_trading/core/service/command_history_service.dart';
import 'package:vp_trading/model/order/command_history/command_history_model.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/model/request/order/update_order_request.dart';

abstract class CommandHistoryRepository {
  Future<BaseResponse<BasePagingResponse<OrderBookModel>>> getOrder({
    required OrderBookRequest queries,
  });
  Future<BaseResponse<BasePagingResponse<OrderCommandHistoryModel>>>
  getOrderHist(OrderBookRequest queries);

  Future<BaseResponse> deleteOrder(DeleteOrderRequest body);

  Future<BaseResponse> editOrder(UpdateOrderRequest body);

  Future<BaseResponse> deleteAllOrder(DeleteOrderRequest body);

  Future<BaseResponse> deleteBatchOrder(DeleteOrderRequest body);

  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getOrderCondition(OrderBookRequest queries);

  Future<BaseResponse> deleteConditionOrder(DeleteOrderRequest body);

  Future<BaseResponse> editConditonOrder(ConditionOrderRequestModel request);

  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getFuConditionOrderBook(OrderBookRequest queries);
}

class CommandHistoryRepositoryImpl implements CommandHistoryRepository {
  final CommandHistoryService commandHistoryService;

  CommandHistoryRepositoryImpl({required this.commandHistoryService});

  @override
  Future<BaseResponse<BasePagingResponse<OrderBookModel>>> getOrder({
    required OrderBookRequest queries,
  }) {
    return commandHistoryService.getOrder(queries);
  }

  @override
  Future<BaseResponse<BasePagingResponse<OrderCommandHistoryModel>>>
  getOrderHist(OrderBookRequest queries) {
    return commandHistoryService.getOrderHist(queries);
  }

  @override
  Future<BaseResponse> deleteOrder(DeleteOrderRequest body) {
    return commandHistoryService.deleteOrder(body);
  }

  @override
  Future<BaseResponse> editOrder(UpdateOrderRequest body) {
    return commandHistoryService.editOrder(body);
  }

  @override
  Future<BaseResponse> deleteAllOrder(DeleteOrderRequest body) {
    return commandHistoryService.deleteAllOrder(body);
  }

  @override
  Future<BaseResponse> deleteBatchOrder(DeleteOrderRequest body) {
    return commandHistoryService.deleteBatchOrder(body);
  }

  @override
  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getOrderCondition(OrderBookRequest queries) {
    return commandHistoryService.getConditionOrderBook(queries);
  }

  @override
  Future<BaseResponse> deleteConditionOrder(DeleteOrderRequest body) {
    return commandHistoryService.deleteConditionOrder(body);
  }

  @override
  Future<BaseResponse> editConditonOrder(ConditionOrderRequestModel request) {
    return commandHistoryService.editConditonOrder(request);
  }

  @override
  Future<BaseResponse<BasePagingResponse<ConditionOrderBookModel>>>
  getFuConditionOrderBook(OrderBookRequest queries) {
    return commandHistoryService.getFuConditionOrderBook(queries);
  }
}
