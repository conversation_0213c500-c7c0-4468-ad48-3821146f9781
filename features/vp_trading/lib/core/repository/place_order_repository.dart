import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/service/place_order_service.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';
import 'package:vp_trading/model/derivative/users_config_model.dart';
import 'package:vp_trading/model/market_status/market_status_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/order_request_model.dart';

abstract class PlaceOrderRepository {
  Future<BaseResponse<AvailableTradeModel>> getAvailableTrade(
    String accountId,
    String symbol,
    String? quotePrice,
  );

  Future<BaseResponse<AvailableTradeModel>> tradingOrder(
    OrderRequestModel request,
  );

  Future<BaseResponse<AvailableTradeModel>> tradingOrderBroker(
    OrderRequestModel request,
  );

  Future<BaseResponse> tradingConditionOrder(
    ConditionOrderRequestModel request,
  );

  Future<BaseResponse> tradingDerivativeConditionOrder(
    ConditionOrderRequestModel request,
  );

  Future<BaseResponse<List<MarketStatusModel>>> getMarketStatus();

  Future<BaseResponse<UsersConfigModel>> getFuUserConfigs();

  Future<BaseResponse<UsersConfigModel>> updateUserConfig(
    UsersConfigModel userConfigModel,
  );
}

class PlaceOrderRepositoryImpl implements PlaceOrderRepository {
  final PlaceOrderService placeOrderService;

  PlaceOrderRepositoryImpl({required this.placeOrderService});

  @override
  Future<BaseResponse<AvailableTradeModel>> getAvailableTrade(
    String accountId,
    String symbol,
    String? quotePrice,
  ) {
    var quotePriceInt =
        (quotePrice == "null" || quotePrice == null || quotePrice.isEmpty)
            ? null
            : quotePrice;
    Map<String, dynamic> mapQueries = {};
    if (quotePriceInt == null || quotePriceInt.isEmpty) {
      mapQueries = {'symbol': symbol};
    } else {
      mapQueries = {'symbol': symbol, 'quotePrice': quotePriceInt};
    }
    return placeOrderService.getAvailableTrade(accountId, mapQueries);
  }

  @override
  Future<BaseResponse<AvailableTradeModel>> tradingOrder(
    OrderRequestModel request,
  ) {
    return placeOrderService.tradingOrder(request);
  }

  @override
  Future<BaseResponse> tradingConditionOrder(
    ConditionOrderRequestModel request,
  ) {
    return placeOrderService.tradingConditionOrder(request);
  }

  @override
  Future<BaseResponse> tradingDerivativeConditionOrder(
    ConditionOrderRequestModel request,
  ) {
    return placeOrderService.tradingDerivativeConditionOrder(request);
  }

  @override
  Future<BaseResponse<List<MarketStatusModel>>> getMarketStatus() {
    return placeOrderService.getMarketStatus();
  }

  @override
  Future<BaseResponse<UsersConfigModel>> getFuUserConfigs() {
    return placeOrderService.getFuUserConfigs();
  }

  @override
  Future<BaseResponse<UsersConfigModel>> updateUserConfig(
    UsersConfigModel userConfigModel,
  ) {
    return placeOrderService.updateUserConfig(userConfigModel);
  }

  @override
  Future<BaseResponse<AvailableTradeModel>> tradingOrderBroker(
    OrderRequestModel request,
  ) {
    return placeOrderService.tradingOrderBroker(request);
  }
}
