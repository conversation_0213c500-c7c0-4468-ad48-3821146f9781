import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/constant/place_order_path_api.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';
import 'package:vp_trading/model/derivative/users_config_model.dart';
import 'package:vp_trading/model/market_status/market_status_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/order_request_model.dart';

part 'place_order_service.g.dart';

@RestApi()
abstract class PlaceOrderService {
  factory PlaceOrderService(Dio dio, {String baseUrl}) = _PlaceOrderService;

  @GET(PlaceOrderPathApi.availableTrade)
  Future<BaseResponse<AvailableTradeModel>> getAvailableTrade(
    @Path('accountId') String accountId,
    @Queries() Map<String, dynamic> queries,
  );

  @GET(PlaceOrderPathApi.tradingMarketStatus)
  Future<BaseResponse<List<MarketStatusModel>>> getMarketStatus();

  @POST(PlaceOrderPathApi.tradingOrder)
  Future<BaseResponse<AvailableTradeModel>> tradingOrder(
    @Body() OrderRequestModel request,
  );

  @POST(PlaceOrderPathApi.tradingOrderBroker)
  Future<BaseResponse<AvailableTradeModel>> tradingOrderBroker(
    @Body() OrderRequestModel request,
  );

  @POST(PlaceOrderPathApi.tradingConditionOrder)
  Future<BaseResponse> tradingConditionOrder(
    @Body() ConditionOrderRequestModel request,
  );

  @POST(PlaceOrderPathApi.tradingDerivativeConditionOrder)
  Future<BaseResponse> tradingDerivativeConditionOrder(
    @Body() ConditionOrderRequestModel request,
  );

  @GET(PlaceOrderPathApi.fuUserConfigs)
  Future<BaseResponse<UsersConfigModel>> getFuUserConfigs();

  @PUT(PlaceOrderPathApi.fuUserConfigs)
  Future<BaseResponse<UsersConfigModel>> updateUserConfig(
    @Body() UsersConfigModel userConfigModel,
  );
}
