part of 'edit_condition_order_cubit.dart';

enum EditConditionOrderStatus {
  initial,
  loading,
  success,
  failure,
}

class EditConditionOrderState extends Equatable {
  const EditConditionOrderState({
    this.status = EditConditionOrderStatus.initial,
    this.errorMessage,
    this.successMessage,
  });

  final EditConditionOrderStatus status;
  final String? errorMessage;
  final String? successMessage;

  @override
  List<Object?> get props => [
        status,
        errorMessage,
        successMessage,
      ];

  EditConditionOrderState copyWith({
    EditConditionOrderStatus? status,
    String? errorMessage,
    String? successMessage,
  }) {
    return EditConditionOrderState(
      status: status ?? this.status,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }
}
