import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:vp_common/shared_prefs/shared_prefs.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/repository/holding_portfolio_repository.dart';
import 'package:vp_trading/model/enum/order_lot_type.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

part 'place_order_state.dart';

class PlaceOrderCubit extends Cubit<PlaceOrderState> {
  PlaceOrderCubit({
    StockCommonRepository? repository,
    required String symbol,
    required this.action,
    this.subAccountType,
    this.accountModel,
  }) : repository = repository ?? GetIt.instance.get<StockCommonRepository>(),
       super(
         PlaceOrderState(
           subAccountType: subAccountType ?? SubAccountType.normal,
           accountModel: accountModel,
           action: action,
           symbol: symbol,
           orderType: OrderType.lo,
         ),
       );

  final StockCommonRepository repository;
  final HoldingPortfolioRepository _repositoryHolding =
      GetIt.instance<HoldingPortfolioRepository>();

  final OrderAction action;

  final SubAccountType? subAccountType;

  final SubAccountModel? accountModel;

  final StreamController<String> _priceStreamController =
      StreamController<String>.broadcast();

  Stream<String> get priceStream => _priceStreamController.stream;

  void updatePrice(String price) {
    _priceStreamController.add(price);
  }

  void clear() {
    _priceStreamController.add("");
  }

  void onOrderActionChange(OrderAction action) {
    if (action == state.action) return;
    if (action == OrderAction.buy && state.orderType.isCondition) {
      emit(state.copyWith(orderType: OrderType.lo));
    }

    emit(state.copyWith(action: action));
  }

  void onSwitchOrderLotType() {
    if (state.orderLotType == OrderLotType.oddLot) {
      emit(state.copyWith(orderLotType: OrderLotType.roundLot));
    } else {
      emit(state.copyWith(orderLotType: OrderLotType.oddLot));
    }
  }

  void onSymbolChange(String symbol) {
    if (symbol == state.symbol) return;
    emit(state.copyWith(symbol: symbol));
    fetchDataSymbolHoldingPortfolio();
  }

  void updateSupAccount(SubAccountModel accountModel) {
    emit(
      state.copyWith(
        subAccountType: accountModel.accountType,
        accountModel: accountModel,
      ),
    );
  }

  void updateOrderType(OrderType orderType) {
    if (orderType.isCondition && state.portfolioStockModel == null) {
      fetchDataSymbolHoldingPortfolio();
    }
    emit(state.copyWith(orderType: orderType));
  }

  Future<void> fetchDataSymbolHoldingPortfolio() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));
      var accountId = "";
      if (state.subAccountType != SubAccountType.all) {
        accountId = state.accountModel?.id ?? "";
      }
      final List<HoldingPortfolioStockModel>? portfolioList =
          await _repositoryHolding.getHoldingPortfolio(
            accountId: accountId,
            symbol: state.symbol,
          );

      if ((portfolioList ?? []).isNotEmpty) {
        emit(state.copyWith(portfolioStockModel: portfolioList?.first));
      }
      emit(state.copyWith(apiStatus: ApiStatus.done()));
    } catch (e) {
      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  initState() {
    saveCacheSymbolSearch();
  }

  void saveCacheSymbolSearch() {
    final symbol = state.symbol;
    final current = SharedPref.getString(keySymbolTradingCurrent);
    final items = current.split(",").where((e) => e.isNotEmpty).toList();
    items.remove(symbol); // Remove if already exists
    items.insert(0, symbol); // Add to the top
    SharedPref.setString(
      keySymbolTradingCurrent,
      items.take(maxValueSaveSymbol).join(","),
    );
  }

  @override
  Future<void> close() {
    _priceStreamController.close();
    return super.close();
  }

  void changeSaveCommand(bool isSaveCommand) {
    emit(state.copyWith(isSaveCommand: isSaveCommand));
  }
}
