import 'dart:math';

import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/place_order_repository.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/order_request_model.dart';

part 'place_order_submit_state.dart';

class PlaceOrderSubmitCubit extends Cubit<PlaceOrderSubmitState> {
  late bool showConfirmOrderMessage;

  bool openByBrokerRecommendation = false;

  // order broker
  String? stockRecommendationId;
  String? reCustodyCd;

  PlaceOrderSubmitCubit() : super(const PlaceOrderSubmitState()) {
    showConfirmOrderMessage = SharedPref.getBool(
      KeyShared.showConfirmOrderMessage,
      defValue: true,
    );
  }

  final PlaceOrderRepository _placeOrderRepository =
      GetIt.instance<PlaceOrderRepository>();

  String get _requestId => String.fromCharCodes(
    List.generate(20, (index) => Random().nextInt(33) + 89),
  );

  void submitPlaceOrder({required OrderRequestModel request}) async {
    try {
      emit(state.copyWith(status: PlaceOrderSubmitStatus.loading));

      final response =
          openByBrokerRecommendation
              ? await _placeOrderRepository.tradingOrderBroker(
                request.copyWith(
                  requestId: "app_$_requestId",
                  reCustodyCd: reCustodyCd,
                  stockRecommendationId: stockRecommendationId,
                ),
              )
              : await _placeOrderRepository.tradingOrder(
                request.copyWith(requestId: "app_$_requestId"),
              );
      if (response.isSuccess) {
        emit(state.copyWith(status: PlaceOrderSubmitStatus.success));
      } else {
        showError(response.message);
        emit(state.copyWith(status: PlaceOrderSubmitStatus.failure));
      }
    } catch (e) {
      emit(state.copyWith(status: PlaceOrderSubmitStatus.failure));
      showError(e);
    }
  }

  void submitPlaceConditionOrder({
    required ConditionOrderRequestModel request,
    bool isDerivative = false,
  }) async {
    try {
      emit(state.copyWith(status: PlaceOrderSubmitStatus.loading));

      final response =
          await (isDerivative
              ? _placeOrderRepository.tradingDerivativeConditionOrder(
                request.copyWith(requestId: "app_$_requestId"),
              )
              : _placeOrderRepository.tradingConditionOrder(
                request.copyWith(requestId: "app_$_requestId"),
              ));
      if (response.isSuccess) {
        emit(state.copyWith(status: PlaceOrderSubmitStatus.success));
      } else {
        showError(response.message);
        emit(state.copyWith(status: PlaceOrderSubmitStatus.failure));
      }
    } catch (e) {
      emit(state.copyWith(status: PlaceOrderSubmitStatus.failure));
      showError(e);
    }
  }

  void updateOpenByBrokerRecommendation(
    bool value, {
    String? stockRecommendationId,
    String? reCustodyCd,
  }) {
    openByBrokerRecommendation = value;
    // order broker
    this.stockRecommendationId = stockRecommendationId;
    this.reCustodyCd = reCustodyCd ?? "system";
  }
}
