import 'package:flutter/cupertino.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';
import 'package:vp_trading/utils/condition_command_util.dart';

part 'validate_order_state.dart';

enum ErrorPrice {
  outOfRange,
  invalid,
  step10Invalid,
  step50Invalid,
  step100Invalid,
  none,
  empty,
  init,
}

enum ErrorVolume {
  invalid,
  invalidMax,
  invalidBuy,
  invalidSell,
  none,
  empty,
  init,
  invalidOddLot,
  invalidEvenLot,
  // Thêm các lỗi mới cho màn sửa lệnh
  editVolumeExceedsBuyingPower,
  editVolumeMustBeGreaterThanExecuted,
  editVolumeExceedsHolding,
}

extension ErrorPriceExtension on ErrorPrice {
  bool get isError {
    return this != ErrorPrice.none &&
        this != ErrorPrice.empty &&
        this != ErrorPrice.init;
  }

  bool get isErrorEditOrder {
    return this != ErrorPrice.none && this != ErrorPrice.init;
  }

  String get message {
    switch (this) {
      case ErrorPrice.outOfRange:
        return 'Giá nhập phải nằm trong khoảng trần sàn';
      case ErrorPrice.invalid:
        return 'Giá đặt không hợp lệ';
      case ErrorPrice.step10Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 10 đ';
      case ErrorPrice.step50Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 50 đ';
      case ErrorPrice.step100Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 100 đ';
      case ErrorPrice.none:
        return '';
      case ErrorPrice.empty:
        return '';
      case ErrorPrice.init:
        return '';
    }
  }

  String get messageEditOrder {
    switch (this) {
      case ErrorPrice.outOfRange:
        return 'Giá nhập phải nằm trong khoảng trần sàn';
      case ErrorPrice.invalid:
        return 'Giá đặt không hợp lệ';
      case ErrorPrice.step10Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 10 đ';
      case ErrorPrice.step50Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 50 đ';
      case ErrorPrice.step100Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 100 đ';
      case ErrorPrice.none:
        return '';
      case ErrorPrice.empty:
        return 'Vui lòng nhập giá';
      case ErrorPrice.init:
        return '';
    }
  }
}

extension ErrorVolumeExtension on ErrorVolume {
  bool get isError {
    return this != ErrorVolume.none &&
        this != ErrorVolume.empty &&
        this != ErrorVolume.init;
  }

  bool get isEditOrderError {
    return this != ErrorVolume.none && this != ErrorVolume.init;
  }

  String message(String? value) {
    switch (this) {
      case ErrorVolume.invalid:
        return 'Khối lượng không hợp lệ';
      case ErrorVolume.invalidMax:
        return 'Vượt khối lượng tối đa là $value';
      case ErrorVolume.invalidBuy:
        return 'Vượt quá sức mua của tiểu khoản';
      case ErrorVolume.invalidSell:
        return 'Bạn không nắm giữ cổ phiếu này';
      case ErrorVolume.none:
        return '';
      case ErrorVolume.empty:
        return '';
      case ErrorVolume.init:
        return '';
      default:
        return '';
    }
  }

  String messageEditOrder(String? value, bool isOddLotByEditOrder) {
    switch (this) {
      // Thêm các thông báo lỗi mới cho màn sửa lệnh
      case ErrorVolume.invalidOddLot:
        return 'Vui lòng sửa giá trị trong khoảng từ 1 đến 99';
      case ErrorVolume.invalidEvenLot:
        return 'Khối lượng tối thiểu 100 và là bội số 100';
      case ErrorVolume.editVolumeExceedsBuyingPower:
        return 'Khối lượng sửa vượt quá sức mua';
      case ErrorVolume.editVolumeMustBeGreaterThanExecuted:
        return 'Khối lượng sửa phải lớn hơn khối lượng đã khớp';
      case ErrorVolume.editVolumeExceedsHolding:
        return 'Vượt quá số lượng nắm giữ của tài khoản';
      case ErrorVolume.empty:
        return isOddLotByEditOrder
            ? 'Khối lượng tối thiểu: 1'
            : 'Khối lượng tối thiểu: 100';
      default:
        return '';
    }
  }
}

class ValidateOrderCubit extends Cubit<ValidateOrderState> {
  ValidateOrderCubit() : super(const ValidateOrderState());
  StockInfoModel? _stockInfo;
  OrderType _orderType = OrderType.lo;
  OrderAction _actionType = OrderAction.buy;
  BuildContext? _context;

  bool get isLoOrGtcOrBuyIn =>
      _orderType.isLoOrGtc || _orderType == OrderType.buyIn;
  AvailableTradeModel? _availableTrade;

  // Thêm các field cho màn sửa lệnh
  bool? _isOpenByEditOrder;
  bool? _isOddLotByEditOrder;

  int? _originalOrderVolume; // Khối lượng ban đầu từ OrderBookModel.qty
  int? _executedVolume; // Khối lượng đã khớp từ OrderBookModel.execQty

  void updateParam({
    StockInfoModel? stockInfo,
    AvailableTradeModel? availableTrade,
    OrderAction? action,
    OrderType? orderType,
    BuildContext? context,
  }) {
    if (context != null) {
      _context = context;
    }
    if (stockInfo != null) {
      _stockInfo = stockInfo;
    }
    if (availableTrade != null) {
      _availableTrade = availableTrade;
    }
    if (action != null) {
      _actionType = action;
    }
    if (orderType != null) {
      _orderType = orderType;
    }

    onChangePrice(state.currentPrice ?? "");
    onChangeVolumne(state.currentVolume ?? "");
  }

  void validateError({ErrorPrice? errorPrice, ErrorVolume? errorVolume}) {
    emit(state.copyWith(errorPrice: errorPrice, errorVolume: errorVolume));
  }

  void clear() {
    emit(const ValidateOrderState());
  }

  void focusField(FocusKeyboard type) {
    emit(state.copyWith(focusKeyboard: type));
  }

  void caculateValue(String value) {
    emit(state.copyWith(calculateValue: value));
  }

  void setSessionType(SessionType sessionType) {
    emit(state.copyWith(sessionType: sessionType));
  }

  void clearSession() {
    emit(state.clearSession());
  }

  void clearData() {
    emit(state.clearData());
  }

  double _valuePrice(String text) {
    final floor = _stockInfo?.floor ?? 0.0;
    final ceiling = _stockInfo?.ceiling ?? 0.0;
    return state.sessionType == null
        ? text.price ?? 0.0
        : _actionType == OrderAction.buy
        ? ceiling.toDouble()
        : floor.toDouble();
  }

  double _quotePrice(String text) {
    final floor = _stockInfo?.floor ?? 0.0;
    final ceiling = _stockInfo?.ceiling ?? 0.0;
    return isLoOrGtcOrBuyIn
        ? _valuePrice(text)
        : _actionType == OrderAction.buy
        ? ceiling.toDouble()
        : floor.toDouble();
  }

  double _stepPrice(String text) {
    return _stockInfo?.stockType == StockType.CW ||
            (_stockInfo?.stockType == StockType.EF &&
                _stockInfo?.exchangeCode != ExchangeCode.UPCOM)
        ? 10.0
        : _stockInfo?.exchangeCode == ExchangeCode.HOSE
        ? (text.price ?? 0.0).stepHose
        : 100.0;
  }

  priceTap({
    required String text,
    bool increase = true,
    bool activation = false,
  }) {
    if (_stockInfo == null) return '';

    if (text.isEmpty) {
      final floor =
          double.tryParse(_stockInfo?.bidPrice1 ?? "0") ??
          _stockInfo?.floor ??
          0.0;
      final ceiling =
          double.tryParse(_stockInfo?.offerPrice1 ?? "0") ??
          _stockInfo?.ceiling ??
          0.0;
      var priceText = (_actionType == OrderAction.buy ? floor : ceiling)
          .getPriceFormatted(convertToThousand: true);
      if (activation) {
        onChangeActivationPrice(priceText);
      } else {
        onChangePrice(priceText);
      }
      return;
    }
    if (text.price == 0.0 && !increase) {
      return onChangePrice(0.0.getPriceFormatted(convertToThousand: true));
    }
    final newText = ConditionCommandUtil.updateValue(
      increase,
      _valuePrice(text),
      _stepPrice(text),
    ).toDouble().getPriceFormatted(convertToThousand: true);
    final finalText = newText.length > 8 ? text : newText;
    if (activation) {
      onChangeActivationPrice(finalText);
    } else {
      onChangePrice(finalText);
    }
  }

  onChangePrice(String value) {
    final error = validatePrice(text: value);

    String valueDisplay() {
      if (_orderType.isWaiting) {
        return ((value.price ?? 0) * (state.currentVolume?.volume ?? 0))
            .valueText;
      }
      return (_quotePrice(value) * (state.currentVolume?.volume ?? 0))
          .valueText;
    }

    emit(
      state.copyWith(
        errorPrice: error,
        calculateValue: valueDisplay(),
        currentPrice: value,
      ),
    );
  }

  onChangeActivationPrice(String value) {
    final error = validatePrice(text: value);

    emit(
      state.copyWith(
        errorActivationPrice: error,
        currentActivationPrice: value,
      ),
    );
  }

  ErrorPrice validatePrice({required String text}) {
    if (state.sessionType != null) {
      return ErrorPrice.none;
    }

    if (_stockInfo == null) return ErrorPrice.none;

    final floor = _stockInfo!.floor ?? 0.0;
    final ceiling = _stockInfo!.ceiling ?? 0.0;
    if (text.isEmpty) {
      return ErrorPrice.empty;
    }
    if (text.price == 0.0) return ErrorPrice.invalid;
    if (_orderType.isLo &&
        (_valuePrice(text) > ceiling || _valuePrice(text) < floor)) {
      return ErrorPrice.outOfRange;
    } else if (_valuePrice(text) % _stepPrice(text) == 0) {
      return ErrorPrice.none;
    } else if (_stepPrice(text) == 10.0) {
      return ErrorPrice.step10Invalid;
    } else if (_stepPrice(text) == 50.0) {
      return ErrorPrice.step50Invalid;
    } else if (_stepPrice(text) == 100.0) {
      return ErrorPrice.step100Invalid;
    }
    return ErrorPrice.none;
  }

  double maxVolumeGtc() {
    return _stockInfo?.exchangeCode == ExchangeCode.HOSE ? 500000.0 : 1000000.0;
  }

  num getMaxVolumeByAvailableTradeResponse(AvailableTradeModel availableTrade) {
    var maxValue =
        (_actionType == OrderAction.buy
            ? availableTrade.maxBuyQty
            : availableTrade.maxSellQty) ??
        0.0;
    if (maxValue > 500000.0) {
      maxValue = 500000;
    }

    if (_orderType.isWaiting) {
      return maxVolumeGtc();
    }

    return _orderType.isGtc ? maxVolumeGtc() : maxValue;
  }

  num maxVolume() {
    if (_availableTrade == null) return 0.0;
    final maxVolume = getMaxVolumeByAvailableTradeResponse(_availableTrade!);
    return maxVolume > 0.0 ? maxVolume : 0.0;
  }

  bool _invalidLot(num volume) {
    return volume.isOddLot &&
        !(_orderType.isLo && (state.sessionType?.isAtoAtc ?? true));
  }

  ErrorVolume validateVolume({required String text}) {
    if (text.isEmpty) {
      return ErrorVolume.empty;
    }
    final valueVolume = text.volume;

    if (_isOpenByEditOrder == true) {
      return validateVolumeByEditOrder(text: text);
    }

    if (_orderType.isWaiting || _orderType.isGtc) {
      if (text.volume == 0 ||
          (valueVolume >= 100 && valueVolume % 100.0 != 0.0)) {
        return ErrorVolume.invalid;
      }
    } else {
      if (text.volume == 0 ||
          (valueVolume >= 100 && valueVolume % 100.0 != 0.0) ||
          _invalidLot(valueVolume)) {
        return ErrorVolume.invalid;
      }
    }

    if (maxVolume() == 0 && _actionType == OrderAction.buy) {
      return ErrorVolume.invalidBuy;
    }

    if (maxVolume() == 0 && _actionType == OrderAction.sell) {
      return ErrorVolume.invalidSell;
    }

    if (text.volume > maxVolume() && !_orderType.isGtc) {
      return ErrorVolume.invalidMax;
    }

    return ErrorVolume.none;
  }

  // Dùng cho màn sửa lệnh - Logic mới theo yêu cầu
  ErrorVolume validateVolumeByEditOrder({required String text}) {
    // 1) Kiểm tra rỗng
    if (text.trim().isEmpty) return ErrorVolume.empty;

    final num newVolume = text.volume;

    // 2) Kiểm tra <= 0
    if (newVolume <= 0) return ErrorVolume.invalid;

    // 3) Kiểm tra khả dụng cơ bản trước
    final num mv = maxVolume();
    if (_actionType == OrderAction.buy && mv == 0)
      return ErrorVolume.invalidBuy;
    if (_actionType == OrderAction.sell && mv == 0)
      return ErrorVolume.invalidSell;

    // 4) Kiểm tra số nguyên và lô lẻ/chẵn cơ bản
    bool isInteger(num x) => x is int || x == x.roundToDouble();
    if (!isInteger(newVolume)) {
      return _isOddLotByEditOrder == true
          ? ErrorVolume.invalidOddLot
          : ErrorVolume.invalidEvenLot;
    }

    final int newVol = newVolume.toInt();
    final int originalVol = _originalOrderVolume ?? 0;
    final int executedVol = _executedVolume ?? 0;

    // 5) Kiểm tra lô lẻ/chẵn
    if (_isOddLotByEditOrder == true) {
      if (newVol < 1 || newVol >= 100) return ErrorVolume.invalidOddLot;
    } else {
      if (newVol < 100 || newVol % 100 != 0) return ErrorVolume.invalidEvenLot;
    }

    // 6) Logic validate theo yêu cầu mới
    if (_actionType == OrderAction.buy) {
      // Lệnh MUA
      if (newVol > originalVol) {
        // Sửa TĂNG: Check KL chênh lệch với sức mua
        final int volumeDifference = newVol - originalVol;
        if (volumeDifference > mv) {
          return ErrorVolume.editVolumeExceedsBuyingPower;
        }
      } else {
        // Sửa GIẢM: Check với KL đã khớp
        if (newVol <= executedVol) {
          return ErrorVolume.editVolumeMustBeGreaterThanExecuted;
        }
      }
    } else {
      // Lệnh BÁN
      if (newVol < originalVol) {
        // Sửa GIẢM: Check với KL đã khớp
        if (newVol <= executedVol) {
          return ErrorVolume.editVolumeMustBeGreaterThanExecuted;
        }
      }

      // Check KL sửa mới với chứng khoán sở hữu (luôn check cho lệnh bán)
      if (newVol > mv) {
        return ErrorVolume.editVolumeExceedsHolding;
      }
    }

    return ErrorVolume.none;
  }

  onChangeVolumne(String value) {
    final error = validateVolume(text: value);

    String valueDisplay() {
      if (_orderType.isTakeProfit || _orderType.isStopLoss) {
        var orderPrice =
            _context?.read<ValidateConditionOrderCubit>().state.orderPrice;
        return ((orderPrice ?? 0) * value.volume).valueText;
      }
      if (_orderType.isWaiting) {
        return (((state.currentPrice ?? "").price ?? 0) * value.volume)
            .valueText;
      }
      return (_quotePrice(state.currentPrice ?? "") * (value.volume)).valueText;
    }

    emit(
      state.copyWith(
        errorVolume: error,
        calculateValue: valueDisplay(),
        currentVolume: value,
      ),
    );
  }

  volumneTap({required String text, bool increase = true}) {
    String onTap({required String text, bool increase = true}) {
      if (text.volume == 0.0) {
        final newText =
            maxVolume() > 0.0 && maxVolume() < 100.0
                ? maxVolume().volumeString
                : (increase ? 100.0 : 0.0).volumeString;
        return newText;
      }
      final step =
          text.volume.isOddLot && !_invalidLot(text.volume) ? 1.0 : 100.0;
      final newText =
          ConditionCommandUtil.updateValue(
            increase,
            text.volume,
            step,
          ).volumeString;
      if (newText.length > 12) return text;
      return newText;
    }

    final newText = onTap(text: text, increase: increase);
    onChangeVolumne(newText);
  }

  void setIsOpenByEditOrder(bool isOpenByEditOrder, {bool isOddLot = false}) {
    _isOpenByEditOrder = isOpenByEditOrder;
    _isOddLotByEditOrder = isOddLot;
  }

  // Thêm method để set thông tin từ OrderBookModel cho màn sửa lệnh
  void setOrderBookInfo({
    required int originalVolume,
    required int executedVolume,
  }) {
    _originalOrderVolume = originalVolume;
    _executedVolume = executedVolume;
  }

  void onUpdateCalculateValue() {
    var orderPrice =
        _context?.read<ValidateConditionOrderCubit>().state.orderPrice;
    var value =
        ((orderPrice ?? 0) * (state.currentVolume?.volume ?? 0)).valueText;
    emit(state.copyWith(calculateValue: value));
  }
}
