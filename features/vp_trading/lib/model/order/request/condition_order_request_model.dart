// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';

part 'condition_order_request_model.g.dart';

@JsonSerializable(includeIfNull: false)
class ConditionOrderRequestModel {
  const ConditionOrderRequestModel({
    this.requestId,
    this.market,
    this.via = 'V',
    required this.orderType,
    required this.accountId,
    required this.conditionInfo,
    this.orderId,
  });

  final String? requestId;

  final String accountId;

  final String? market;

  final String via;

  final String orderType;

  final ConditionInfo conditionInfo;

  final String? orderId;

  factory ConditionOrderRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ConditionOrderRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConditionOrderRequestModelToJson(this);

  ConditionOrderRequestModel copyWith({
    String? requestId,
    String? accountId,
    String? market,
    String? via,
    String? orderType,
    String? type,
    ConditionInfo? conditionInfo,
    String? orderId,
  }) {
    return ConditionOrderRequestModel(
      requestId: requestId ?? this.requestId,
      accountId: accountId ?? this.accountId,
      market: market ?? this.market,
      via: via ?? this.via,
      orderType: orderType ?? this.orderType,
      conditionInfo: conditionInfo ?? this.conditionInfo,
      orderId: orderId ?? this.orderId,
    );
  }
}

@JsonSerializable(includeIfNull: false)
class ConditionInfo {
  const ConditionInfo({
    required this.symbol,
    required this.qty,
    required this.side,
    required this.type,
    required this.price,
    required this.fromDate,
    required this.toDate,
    this.activePrice,
    this.activeType,
    this.slipPagePrice,
    this.stopLossRate,
    this.stopLossPriceAmp,
    this.costPrice,

    /// Derivative
    this.priceTypeTP,
    this.priceTypeSL,
    this.priceTP,
    this.priceSL,
    this.activepriceTP,
    this.activepriceSL,
    this.timetype,
    this.split,
    this.priceStep,
    this.deltaValue,
    this.deltaType,
  });

  final String symbol;

  final num qty;

  final String side;

  final String type;

  final dynamic price;

  final num? activePrice;

  final String? activeType;

  final String fromDate;

  final String toDate;

  final num? slipPagePrice;

  final num? stopLossRate;

  final num? stopLossPriceAmp;

  final num? costPrice;

  /// Derivative
  final String? priceTypeTP;

  final String? priceTypeSL;

  final num? priceTP;

  final num? priceSL;

  final num? activepriceTP;

  final num? activepriceSL;

  final String? timetype;

  final String? split;

  final num? priceStep;

  final num? deltaValue;

  final String? deltaType;

  factory ConditionInfo.fromJson(Map<String, dynamic> json) =>
      _$ConditionInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ConditionInfoToJson(this);

  ConditionInfo copyWith({
    String? symbol,
    num? qty,
    String? side,
    String? type,
    num? price,
    String? fromDate,
    String? toDate,
    num? activePrice,
    String? activeType,
    num? slipPagePrice,
    num? stopLossRate,
    num? stopLossPriceAmp,
    num? costPrice,

    /// Derivative
    String? priceTypeTP,
    String? priceTypeSL,
    num? priceTP,
    num? priceSL,
    num? activepriceTP,
    num? activepriceSL,
    String? timetype,
    String? split,
    num? priceStep,
    num? deltaValue,
    String? deltaType,
  }) {
    return ConditionInfo(
      symbol: symbol ?? this.symbol,
      qty: qty ?? this.qty,
      side: side ?? this.side,
      type: type ?? this.type,
      price: price ?? this.price,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      activePrice: activePrice ?? this.activePrice,
      activeType: activeType ?? this.activeType,
      slipPagePrice: slipPagePrice ?? this.slipPagePrice,
      stopLossRate: stopLossRate ?? this.stopLossRate,
      stopLossPriceAmp: stopLossPriceAmp ?? this.stopLossPriceAmp,
      costPrice: costPrice ?? this.costPrice,

      /// Derivative
      priceTypeTP: priceTypeTP ?? this.priceTypeTP,
      priceTypeSL: priceTypeSL ?? this.priceTypeSL,
      priceTP: priceTP ?? this.priceTP,
      priceSL: priceSL ?? this.priceSL,
      activepriceTP: activepriceTP ?? this.activepriceTP,
      activepriceSL: activepriceSL ?? this.activepriceSL,
      timetype: timetype ?? this.timetype,
      split: split ?? this.split,
      priceStep: priceStep ?? this.priceStep,
      deltaValue: deltaValue ?? this.deltaValue,
      deltaType: deltaType ?? this.deltaType,
    );
  }
}
