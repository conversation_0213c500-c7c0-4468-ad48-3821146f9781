// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';

part 'order_request_model.g.dart';

@JsonSerializable(includeIfNull: false)
class OrderRequestModel {
  const OrderRequestModel({
    this.requestId,
    required this.market,
    this.via = 'V',
    required this.symbol,
    required this.side,
    required this.type,
    required this.accountId,
    required this.username,
    required this.qty,
    required this.price,
    this.stockRecommendationId,
    this.reCustodyCd,
  });

  final String? requestId;

  final String market;

  final String via;

  final String symbol;

  final String side;

  final String type;

  final String accountId;

  final String username;

  final num qty;

  final dynamic price;

  final String? stockRecommendationId;

  final String? reCustodyCd;

  factory OrderRequestModel.fromJson(Map<String, dynamic> json) =>
      _$OrderRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderRequestModelToJson(this);

  OrderRequestModel copyWith({
    String? requestId,
    String? market,
    String? via,
    String? symbol,
    String? side,
    String? type,
    String? accountId,
    String? username,
    num? qty,
    num? price,
    String? stockRecommendationId,
    String? reCustodyCd,
  }) {
    return OrderRequestModel(
      requestId: requestId ?? this.requestId,
      market: market ?? this.market,
      via: via ?? this.via,
      symbol: symbol ?? this.symbol,
      side: side ?? this.side,
      type: type ?? this.type,
      accountId: accountId ?? this.accountId,
      username: username ?? this.username,
      qty: qty ?? this.qty,
      price: price ?? this.price,
      stockRecommendationId:
          stockRecommendationId ?? this.stockRecommendationId,
      reCustodyCd: reCustodyCd ?? this.reCustodyCd,
    );
  }
}
