// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderRequestModel _$OrderRequestModelFromJson(Map<String, dynamic> json) =>
    OrderRequestModel(
      requestId: json['requestId'] as String?,
      market: json['market'] as String,
      via: json['via'] as String? ?? 'V',
      symbol: json['symbol'] as String,
      side: json['side'] as String,
      type: json['type'] as String,
      accountId: json['accountId'] as String,
      username: json['username'] as String,
      qty: json['qty'] as num,
      price: json['price'],
      stockRecommendationId: json['stockRecommendationId'] as String?,
      reCustodyCd: json['reCustodyCd'] as String?,
    );

Map<String, dynamic> _$OrderRequestModelToJson(OrderRequestModel instance) =>
    <String, dynamic>{
      if (instance.requestId case final value?) 'requestId': value,
      'market': instance.market,
      'via': instance.via,
      'symbol': instance.symbol,
      'side': instance.side,
      'type': instance.type,
      'accountId': instance.accountId,
      'username': instance.username,
      'qty': instance.qty,
      if (instance.price case final value?) 'price': value,
      if (instance.stockRecommendationId case final value?)
        'stockRecommendationId': value,
      if (instance.reCustodyCd case final value?) 'reCustodyCd': value,
    };
