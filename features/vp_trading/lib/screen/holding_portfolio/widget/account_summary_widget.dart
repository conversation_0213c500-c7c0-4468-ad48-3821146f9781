import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/utils/stock_color_utils.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';

class AccountSummaryWidget extends StatelessWidget {
  const AccountSummaryWidget({
    required this.marketValue,
    required this.capitalValue,
    required this.profit,
    required this.profitPercent,
    super.key,
  });

  final num marketValue;
  final num capitalValue;
  final num profit;
  final double profitPercent;

  @override
  Widget build(BuildContext context) {
    final isProfit = profit >= 0;
    final profitColor =
        isProfit
            ? context.colors.textAccentGreen
            : context.colors.textAccentRed;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          VPTradingLocalize.current.trading_market_value,
          style: context.textStyle.captionRegular?.copyWith(
            color: themeData.gray500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          marketValue.toMoney(),
          style: context.textStyle.headineBold6?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
        const SizedBox(height: 24),

        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    VPTradingLocalize.current.trading_capital_value,
                    style: context.textStyle.captionRegular?.copyWith(
                      color: themeData.gray500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    capitalValue.toMoney(),
                    style: context.textStyle.subtitle14.copyColor(
                      vpColor.textPrimary,
                    ),
                  ),
                ],
              ),
            ),

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  VPTradingLocalize.current.trading_expected_profit_loss,
                  style: context.textStyle.captionRegular?.copyWith(
                    color: themeData.gray500,
                  ),
                ),
                const SizedBox(height: 4),
                VPFlashingColorView(
                  flashColor: StockColorUtils.derivativeDiffColor(diff: profit),
                  data: profit,
                  builder: (child, status) {
                    final color =
                        (status == FlashingStatus.start)
                            ? vpColor.textWhite
                            : StockColorUtils.derivativeDiffColor(diff: profit);
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          profit.toMoney(addCharacter: true),
                          style: context.textStyle.subtitle14?.copyWith(
                            color: color,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          isProfit
                              ? Icons.arrow_drop_up
                              : Icons.arrow_drop_down,
                          color: profitColor,
                          size: 24,
                        ),
                        Text(
                          '${FormatUtils.formatPercent(profitPercent, showSign: true)}',
                          style: context.textStyle.subtitle14?.copyWith(
                            color: color,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}
