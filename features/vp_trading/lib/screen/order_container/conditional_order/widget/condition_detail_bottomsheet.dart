import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/condition_profit_stop_loss_order/condition_profit_stop_loss_order.dart';
import 'package:vp_trading/screen/order_container/conditional_order/edit_condition_order/condition_pending_order.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/utils/stock_utils.dart';

import '../../../../widgets/content_expansion_widget.dart';

class ConditionDetailBottomSheet extends StatefulWidget {
  const ConditionDetailBottomSheet({
    super.key,
    required this.model,
    this.onEditCommandSucces,
    this.onDeleteCommand,
  });

  final ConditionOrderBookModel model;

  final VoidCallback? onEditCommandSucces;

  final VoidCallback? onDeleteCommand;

  @override
  State<ConditionDetailBottomSheet> createState() =>
      _ConditionDetailBottomSheetState();
}

class _ConditionDetailBottomSheetState
    extends State<ConditionDetailBottomSheet> {
  @override
  Widget build(BuildContext context) {
    final titleStyle = vpTextStyle.subtitle14.copyColor(themeData.gray700);

    final valueStyle = vpTextStyle.subtitle14.copyColor(themeData.focus);

    final execprice = widget.model.execPrice ?? 0;

    final execqtty = widget.model.execQty ?? 0;

    final qtty = widget.model.qty ?? 0;

    final execqttyFormat = AppNumberFormatUtils.shared.percentFormatter.format(
      execqtty,
    );

    final qttyFormat = AppNumberFormatUtils.shared.percentFormatter.format(
      qtty,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          /// Tiểu khoản
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_sub_account,
            value: StockUtils.getSubAccountName(widget.model.accountId),
            // value: (widget.model.producttypename ?? '').replaceAll(".", ''),
          ),

          /// Mã cổ phiếu
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_history_stock_code,
            value: widget.model.symbol ?? '',
          ),

          /// Lệnh
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_stock_type,
            value: widget.model.orderTypeEnum.title,
          ),

          /// Loại lệnh
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_command_type,
            value: widget.model.priceType ?? "-",
          ),

          /// Giá đặt lệnh
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_order_price,
            value: widget.model.price.toString().getPriceFormatted(),
          ),

          /// Giá kích hoạt
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: "Giá kích hoạt",
            value: widget.model.activePrice.toString().getPriceFormatted(),
          ),

          /// Thời gian đặt lệnh
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_order_time,
            value:
                "${AppTimeUtils.format(DateTime.now(), AppTimeUtilsFormat.dateNormal)} ${widget.model.tradeTime ?? ''}",
          ),

          /// Thời gian hiệu lực
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: "Thời gian hiệu lực",
            value: "${widget.model.fromDate} - ${widget.model.toDate}",
          ),

          /// Thời gian hiệu lực
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            allowTitleWrap: true,
            title: "Khi có sự kiện quyền pha loãng giá cổ phiếu",
            value: widget.model.diluationActionFormat,
          ),
          Divider(color: themeData.divider, thickness: 1, height: 48),

          /// Khối lượng khớp
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            padding: EdgeInsets.zero,
            value: execqttyFormat,
            title: VPTradingLocalize.current.trading_joint_volume,
          ),

          /// Giá khớp
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            value: execprice.toDouble().getPriceFormatted(
              currency: '',
              convertToThousand: true,
            ),
            title: VPTradingLocalize.current.trading_matched_price,
          ),

          /// Giá trị lệnh đặt. Giá trị lệnh đặt = Giá đặt * KL đặt
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_order_value_title,
            value:
                ((double.tryParse(widget.model.price ?? '0') ?? 0) *
                        (widget.model.qty ?? 0))
                    .toMoney(),
          ),

          ///Trạng thái
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle?.copyWith(
              color: widget.model.orderStatusEnum.textColor,
            ),
            title: VPTradingLocalize.current.trading_status,
            value: widget.model.orderStatusEnum.title,
          ),
          const SizedBox(height: 8),

          /// build bottom actions view
          buildActionBottom(context),
        ],
      ),
    );
  }

  Widget buildActionBottom(BuildContext context) {
    if (widget.model.allowAmend == StockAppConstants.n &&
        widget.model.allowCancel == StockAppConstants.n) {
      return Padding(
        padding: const EdgeInsets.only(top: 8),
        child: VpsButton.secondaryXsSmall(
          title: VPCommonLocalize.current.close,
          onPressed: () {
            Navigator.of(context).pop();
          },
          alignment: Alignment.center,
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          if (widget.model.allowCancel == StockAppConstants.y)
            Expanded(
              child: VpsButton.secondaryDangerXsSmall(
                title: VPTradingLocalize.current.trading_cancel_order,
                onPressed: () {
                  dialogConfirmDeleteOrder(context, () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                    if (widget.onDeleteCommand != null) {
                      widget.onDeleteCommand!();
                    }
                  });
                },
                alignment: Alignment.center,
              ),
            ),
          if (widget.model.allowAmend == StockAppConstants.y) ...[
            const SizedBox(width: 8),
            Expanded(
              child: VpsButton.secondaryXsSmall(
                title: VPTradingLocalize.current.trading_edit_order,
                onPressed: () {
                  _showEditOrder(context);
                },
                alignment: Alignment.center,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showEditOrder(BuildContext context) {
    if (widget.model.conditionOrderTypeEnum == ConditionOrderTypeEnum.seo) {
      opendEditConditionPendingOrderBottomSheet(
        context: context,
        item: widget.model,
        onEditSuccess: () {
          context.pop();
          context.pop();
          context.showSuccess(content: 'Đã gửi thành công');
          widget.onEditCommandSucces?.call();
        },
      );
    } else {
      opendEditConditionProfitStopLossOrderBottomSheet(
        context: context,
        item: widget.model,
        onEditSuccess: () {
          context.pop();
          context.pop();
          context.showSuccess(content: 'Đã gửi thành công');
          widget.onEditCommandSucces?.call();
        },
      );
    }
  }
}

void dialogConfirmDeleteOrder(
  BuildContext context,
  VoidCallback onDeleteCommand,
) async {
  VPPopup.outlineAndPrimaryButton(
        title: VPTradingLocalize.current.trading_cancel_order_title,
        content: VPTradingLocalize.current.trading_cancel_order_confirm_message,
      )
      .copyWith(icon: VpTradingAssets.icons.orderCancelIcon.svg())
      .copyWith(
        button: VpsButton.secondarySmall(
          title: VPTradingLocalize.current.trading_close,
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      )
      .copyWith(
        button: VpsButton.primaryDangerSmall(
          title: VPTradingLocalize.current.trading_cancel_order_title,
          onPressed: () {
            onDeleteCommand();
          },
        ),
      )
      .showDialog(context);
}
