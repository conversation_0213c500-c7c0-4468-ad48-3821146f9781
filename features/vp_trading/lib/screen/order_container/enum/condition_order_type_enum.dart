enum ConditionOrderTypeEnum { all, gtc, seo, tpo, slo }

extension ConditionOrderTypeEnumExt on ConditionOrderTypeEnum {
  String get title {
    switch (this) {
      case ConditionOrderTypeEnum.all:
        return "Tất cả";
      case ConditionOrderTypeEnum.gtc:
        return "Lệnh GTC";
      case ConditionOrderTypeEnum.seo:
        return "Lệnh chờ";
      case ConditionOrderTypeEnum.tpo:
        return "Lệnh chốt lời";
      case ConditionOrderTypeEnum.slo:
        return "Lệnh cắt lỗ";
    }
  }

  String? get titleEdit {
    switch (this) {
      case ConditionOrderTypeEnum.seo:
        return "Sửa lệnh chờ";
      case ConditionOrderTypeEnum.tpo:
        return " Sửa lệnh chốt lời";
      case ConditionOrderTypeEnum.slo:
        return "Sửa lệnh cắt lỗ";
      default:
        return "";
    }
  }

  String get codeRequest {
    switch (this) {
      case ConditionOrderTypeEnum.all:
        return "ALL";
      case ConditionOrderTypeEnum.gtc:
        return "GTC";
      case ConditionOrderTypeEnum.seo:
        return "SEO";
      case ConditionOrderTypeEnum.tpo:
        return "TPO";
      case ConditionOrderTypeEnum.slo:
        return "SLO";
    }
  }

  static ConditionOrderTypeEnum conditionOrderTypeFromString(String? code) {
    switch (code?.toUpperCase()) {
      case "GTC":
        return ConditionOrderTypeEnum.gtc;
      case "SEO":
        return ConditionOrderTypeEnum.seo;
      case "TPO":
        return ConditionOrderTypeEnum.tpo;
      case "SLO":
        return ConditionOrderTypeEnum.slo;
      default:
        return ConditionOrderTypeEnum.gtc; // fallback
    }
  }
}
