import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/order/request/order_request_model.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/confirm_toggle_button.dart';

class DerivativeOrderConfirmDialog extends StatelessWidget {
  final String price;
  final Function(bool showConfirmOrder) onConfirm;
  final OrderRequestModel requestModel;

  const DerivativeOrderConfirmDialog({
    super.key,
    required this.requestModel,
    required this.onConfirm,
    required this.price,
  });

  @override
  Widget build(BuildContext context) {
    bool isBuyOrder = requestModel.side == OrderAction.buy.value;

    Color orderColor = isBuyOrder ? themeData.primary : themeData.red;

    String orderLabel =
        isBuyOrder
            ? OrderAction.buy.nameTypeDerivative
            : OrderAction.sell.nameTypeDerivative;

    String title = VPTradingLocalize.current.derivative_order_accept;

    String buttonLabel = VPCommonLocalize.current.confirm;

    bool showConfirmOrder = true;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: vpTextStyle.headineBold6.copyColor(themeData.displayLarge),
        ),
        const SizedBox(height: 16),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.trading_command_type,
          content: orderLabel,
          colorContent: orderColor,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: 'Mã hợp đồng',
          content: requestModel.symbol,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.trading_volume,
          content: requestModel.qty.abs().volumeString,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(title: 'Giá đặt', content: price),
        const SizedBox(height: 20),
        ConfirmToggleButton(
          onChange: (showConfirm) => showConfirmOrder = showConfirm,
          showConfirm: showConfirmOrder,
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            Expanded(
              child: VpsButton.secondarySmall(
                title: VPCommonLocalize.current.close,
                onPressed: () => Navigator.pop(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child:
                  isBuyOrder
                      ? VpsButton.primarySmall(
                        title: buttonLabel,
                        onPressed: () {
                          Navigator.pop(context);
                          onConfirm(showConfirmOrder);
                        },
                      )
                      : VpsButton.primaryDangerSmall(
                        title: buttonLabel,
                        onPressed: () {
                          Navigator.pop(context);
                          onConfirm(showConfirmOrder);
                        },
                      ),
            ),
          ],
        ),
      ],
    );
  }
}

class OrderConfirmRowTitle extends StatelessWidget {
  final String title;
  final String content;
  final Color? colorContent;

  const OrderConfirmRowTitle({
    super.key,
    required this.title,
    required this.content,
    this.colorContent,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: vpTextStyle.body14.copyColor(themeData.gray700)),
        Text(
          content,
          style: vpTextStyle.body14.copyColor(
            colorContent ?? themeData.displayLarge,
          ),
        ),
      ],
    );
  }
}
