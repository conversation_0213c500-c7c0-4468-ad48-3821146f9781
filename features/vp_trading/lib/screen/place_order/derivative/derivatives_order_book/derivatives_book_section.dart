import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/derivatives_order_book/derivatives_order_book_cubit.dart';
import 'package:vp_trading/cubit/derivative/derivatives_order_book_multi_select/derivatives_order_book_multi_select_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/screen/derivative_home_cubit.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/widget/dialog_cancel_all_order.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_list.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class DerivativesBookSection extends StatefulWidget {
  const DerivativesBookSection({super.key});

  @override
  State<DerivativesBookSection> createState() => _DerivativesBookSectionState();
}

class _DerivativesBookSectionState extends State<DerivativesBookSection> {
  late DerivativesOrderBookCubit _cubit;
  late DerivativesOrderBookMultiSelectCubit _multiSelectCubit;
  late DeleteUpdateOrderCubit _deleteUpdateOrderCubit;

  bool _isOrderListVisible = true;

  void _toggleOrderList() {
    setState(() {
      _isOrderListVisible = !_isOrderListVisible;
    });
  }

  @override
  void initState() {
    super.initState();
    _cubit = DerivativesOrderBookCubit();
    _multiSelectCubit = DerivativesOrderBookMultiSelectCubit();
    _deleteUpdateOrderCubit = DeleteUpdateOrderCubit();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => _multiSelectCubit),
        BlocProvider(create: (context) => _deleteUpdateOrderCubit),
        BlocProvider(
          create:
              (context) =>
                  _cubit..init(
                    orderStatus:
                        "${OrderStatusEnum.waiting.codeRequest},${OrderStatusEnum.matchedPartial.codeRequest}",
                  ),
        ),
      ],
      child: Column(
        children: [
          _header(),
          const SizedBox(height: 8),
          const DividerWidget(),
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            child:
                _isOrderListVisible
                    ? _buildOrderList()
                    : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Row _header() {
    return Row(
      children: [
        BlocBuilder<DerivativesOrderBookCubit, DerivativesOrderBookState>(
          builder: (context, state) {
            return GestureDetector(
              onTap: _toggleOrderList,
              child: Text(
                "${VPTradingLocalize.current.trading_pending_order} (${state.listItems.length})",
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),
            );
          },
        ),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: _toggleOrderList,
          child: AnimatedRotation(
            turns: _isOrderListVisible ? 0.5 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: DesignAssets.icons.dropdown.icArrowBottom.svg(
              colorFilter: ColorFilter.mode(
                vpColor.iconPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
        const Spacer(),
        Row(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                final homeCubit =
                    context
                        .findAncestorStateOfType<State>()
                        ?.context
                        .read<DerivativeHomeCubit>();
                if (homeCubit != null) {
                  homeCubit.onIndexChanged(2);
                }
              },
              child: Container(
                color: Colors.transparent,
                margin: const EdgeInsets.only(left: 16),
                child: Text(
                  VPTradingLocalize.current.trading_command_history,
                  style: vpTextStyle.captionMedium.copyColor(vpColor.textBrand),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderList() {
    return BlocBuilder<DerivativesOrderBookCubit, DerivativesOrderBookState>(
      builder: (context, orderState) {
        return BlocBuilder<
          DerivativesOrderBookMultiSelectCubit,
          DerivativesOrderBookMultiSelectState
        >(
          bloc: _multiSelectCubit,
          builder: (context, multiSelectState) {
            return Column(
              children: [
                if (orderState.isLoading) const CommandHistoryLoadingWidget(),
                if (!orderState.isLoading && orderState.listItems.isEmpty)
                  NoDataView(
                    content: VPTradingLocalize.current.trading_no_data_message,
                  ),

                if (orderState.listItems.isNotEmpty &&
                    !orderState.isLoading) ...[
                  // Filter button
                  const SizedBox(height: 8),
                  _buildTitle(),
                  const SizedBox(height: 8),
                  DerivativesOrderList(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    items: orderState.listItems,
                    refresh: () async {
                      await _cubit.loadData();
                    },
                    editSuccess: () async {
                      await _cubit.loadData();
                    },
                    isMultiSelectMode: multiSelectState.isMultiSelectMode,
                    isOrderSelected: _multiSelectCubit.isOrderSelected,
                    onSelectionChanged: _multiSelectCubit.toggleOrderSelection,
                  ),
                ],
              ],
            );
          },
        );
      },
    );
  }

  _buildTitle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'Mã HĐ/ Trạng thái',
              style: context.textStyle.captionRegular?.copyWith(
                color: vpColor.textSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: Text(
              'KL đặt/ Giá đặt',
              style: context.textStyle.captionRegular?.copyWith(
                color: vpColor.textSecondary,
              ),
              textAlign: TextAlign.left,
            ),
          ),

          _buttonDeleteAll(),
        ],
      ),
    );
  }

  _buttonDeleteAll() {
    return GestureDetector(
      onTap: () {
        dialogConfirmDeleteAllOrder(context, () {
          _deleteUpdateOrderCubit.deleteAllOrder(
            DeleteOrderRequest(
              accountId:
                  GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
              requestId: "app_${AppHelper().genXRequestID()}",
              via: "V",
              orderId: _cubit.state.listOrderIdCancel.join(','),
            ),
          );
          Navigator.of(context).pop();
        });
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          VpTradingAssets.icons.icRemove2.svg(),
          const SizedBox(width: 8),
          Text(
            VPTradingLocalize.current.trading_cancel_all_order,
            style: vpTextStyle.captionMedium.copyColor(vpColor.textAccentRed),
          ),
        ],
      ),
    );
  }
}
