import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivatives_condition_order/derivatives_condition_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_state.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/market_type.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_multi_select_bottom_bar.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_title_widget.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/widget/no_data_derivatives_order_book.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

// Dialog function for multiple order deletion
void dialogConfirmDeleteMultipleOrders(
  BuildContext context,
  int orderCount,
  VoidCallback onConfirmCallback,
) async {
  VPPopup.outlineAndPrimaryButton(
        title: orderCount > 0 ? 'Huỷ $orderCount lệnh' : 'Hủy tất cả',
        content:
            orderCount > 0
                ? 'Quý khách chắc chắn muốn hủy các lệnh đã chọn?'
                : 'Quý khách chắc chắn muốn hủy tất cả các lệnh?',
      )
      .copyWith(icon: VpTradingAssets.icons.icCancleDialog.svg())
      .copyWith(
        button: VpsButton.secondaryXsSmall(
          title: 'Đóng',
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      )
      .copyWith(
        button: VpsButton.primaryDangerXsSmall(
          title: VPTradingLocalize.current.trading_cancel_order_title,
          onPressed: () {
            Navigator.of(context).pop();
            onConfirmCallback();
          },
        ),
      )
      .showDialog(context);
}

class ConditionalOrderPage extends StatefulWidget {
  const ConditionalOrderPage({super.key});

  @override
  State<ConditionalOrderPage> createState() => _ConditionalOrderPageState();
}

class _ConditionalOrderPageState extends State<ConditionalOrderPage> {
  late DerivativesConditionOrderCubit _cubit;
  late DeleteUpdateOrderCubit _deleteUpdateOrderCubit;

  @override
  void initState() {
    super.initState();
    _cubit = DerivativesConditionOrderCubit();
    _deleteUpdateOrderCubit = DeleteUpdateOrderCubit();
    _cubit.init();
  }

  @override
  void dispose() {
    _cubit.dispose();
    _deleteUpdateOrderCubit.close();
    super.dispose();
  }

  void _handleEnterMultiSelectMode() {
    _cubit.toggleMultiSelectMode();
  }

  void _handleExitMultiSelectMode() {
    _cubit.toggleMultiSelectMode();
  }

  Future<void> _handleCancelAllOrders(String listOrderId) async {
    dialogConfirmDeleteMultipleOrders(context, 0, () async {
      _deleteUpdateOrderCubit.deleteAllOrder(
        DeleteOrderRequest(
          accountId:
              GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
          requestId: "app_${AppHelper().genXRequestID()}",
          via: "V",
          orderId: listOrderId,
          market: MarketType.derivatives.nameServer,
        ),
        isConditional: true,
      );
    });
  }

  Future<void> _handleCancelSelectedOrders(String listOrderId) async {
    final selectedCount = _cubit.state.selectedItems.length;
    dialogConfirmDeleteMultipleOrders(context, selectedCount, () async {
      _deleteUpdateOrderCubit.deleteAllOrder(
        DeleteOrderRequest(
          accountId:
              GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
          requestId: "app_${AppHelper().genXRequestID()}",
          via: "V",
          orderId: listOrderId,
          market: MarketType.derivatives.nameServer,
        ),
        isConditional: true,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _deleteUpdateOrderCubit,
      child: BlocListener<DeleteUpdateOrderCubit, DeleteUpdateOrderState>(
        listener: (context, deleteState) {
          if (deleteState.status ==
                  DeleteUpdateOrderStateEnum.isDeleteSuccess ||
              deleteState.status ==
                  DeleteUpdateOrderStateEnum.isDeleteAllSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  VPTradingLocalize.current.trading_cancel_order_success,
                ),
                backgroundColor: vpColor.textAccentGreen,
              ),
            );
            _cubit.loadData();
          } else if (deleteState.status == DeleteUpdateOrderStateEnum.failure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(deleteState.errorMessage ?? "Hủy lệnh thất bại"),
                backgroundColor: vpColor.textAccentRed,
              ),
            );
          }
        },
        child: BlocBuilder<
          DerivativesConditionOrderCubit,
          DerivativesConditionOrderState
        >(
          builder: (context, state) {
            return Scaffold(
              body: Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: Column(
                  children: [
                    if (state.isLoading)
                      const Expanded(child: CommandHistoryLoadingWidget()),
                    if (!state.isLoading && state.listItems.isEmpty)
                      Expanded(
                        child: PullToRefreshView(
                          onRefresh: () async {
                            await _cubit.loadData();
                          },
                          child: const NoDataDerivativesOrderBook(),
                        ),
                      ),
                    if (state.listItems.isNotEmpty && !state.isLoading) ...[
                      DerivativesOrderTitleWidget(
                        expandTitleWidget: const [10, 6, 7, 12],
                        showTitleDeleteAll: true,
                        onDeleteAll:
                            () => _handleCancelAllOrders(
                              state.listOrderIdCancel.join(','),
                            ),
                        onMultiSelectMode: _handleEnterMultiSelectMode,
                        isEnableCancelButton:
                            state.listOrderIdCancel.isNotEmpty,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: PullToRefreshView(
                          onRefresh: () async {
                            await _cubit.loadData();
                          },
                          child: _buildOrderList(state),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              bottomNavigationBar:
                  state.isMultiSelectMode
                      ? DerivativesOrderMultiSelectBottomBar(
                        selectedCount: state.selectedItems.length,
                        onBack: _handleExitMultiSelectMode,
                        onCancelOrders:
                            () => _handleCancelSelectedOrders(
                              state.selectedItems
                                  .map((item) => item.orderId ?? '')
                                  .where((id) => id.isNotEmpty)
                                  .join(','),
                            ),
                        isLoading: false,
                      )
                      : null,
            );
          },
        ),
      ),
    );
  }

  Widget _buildOrderList(DerivativesConditionOrderState state) {
    return ListView.builder(
      itemCount: state.listItems.length,
      itemBuilder: (context, index) {
        final item = state.listItems[index];
        return _buildOrderItem(item, state);
      },
    );
  }

  Widget _buildOrderItem(
    ConditionOrderBookModel item,
    DerivativesConditionOrderState state,
  ) {
    final isSelected = state.selectedItems.contains(item);
    final isMultiSelectMode = state.isMultiSelectMode;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation1,
        borderRadius: BorderRadius.circular(16),
        border:
            isSelected
                ? Border.all(color: vpColor.textAccentBlue, width: 2)
                : null,
      ),
      child: InkWell(
        onTap: isMultiSelectMode ? () => _cubit.toggleSelectItem(item) : null,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  if (isMultiSelectMode) ...[
                    Checkbox(
                      value: isSelected,
                      onChanged: (_) => _cubit.toggleSelectItem(item),
                      activeColor: vpColor.textAccentBlue,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.symbol ?? '-',
                          style: context.textStyle.body14?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: vpColor.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _getOrderTypeText(item),
                          style: context.textStyle.captionRegular?.copyWith(
                            color: _getOrderTypeColor(item),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${item.qty ?? '-'}',
                          style: context.textStyle.body14?.copyWith(
                            color: vpColor.textPrimary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          item.price ?? '-',
                          style: context.textStyle.captionRegular?.copyWith(
                            color: vpColor.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!isMultiSelectMode)
                    Expanded(
                      flex: 1,
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: GestureDetector(
                          onTap: () => _showDeleteConfirmDialog(item),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: vpColor.backgroundElevation0,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: vpColor.textDisabled,
                                width: 0.5,
                              ),
                            ),
                            child: VpTradingAssets.icons.icRemove2.svg(
                              width: 16,
                              height: 16,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              if (!isMultiSelectMode) ...[
                const SizedBox(height: 12),
                _buildOrderDetails(item),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderDetails(ConditionOrderBookModel item) {
    return Column(
      children: [
        _buildDetailRow('Giá kích hoạt', '${item.activePrice ?? '-'}'),
        const SizedBox(height: 8),
        _buildDetailRow('Khối lượng', '${item.qty ?? '-'}'),
        const SizedBox(height: 8),
        _buildDetailRow('Giá đặt', item.price ?? '-'),
        const SizedBox(height: 8),
        _buildDetailRow('Thời gian tạo', item.createTime ?? '-'),
        const SizedBox(height: 8),
        _buildDetailRow('Trạng thái', item.orderStatus ?? '-'),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: context.textStyle.body14?.copyWith(
            color: vpColor.textSecondary,
          ),
        ),
        Text(
          value,
          style: context.textStyle.body14?.copyWith(
            color: vpColor.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _getOrderTypeText(ConditionOrderBookModel item) {
    final side = item.side?.toUpperCase();
    if (side == 'B' || side == 'BUY') {
      return 'LONG';
    } else if (side == 'S' || side == 'SELL') {
      return 'SHORT';
    }
    return item.orderType ?? '-';
  }

  Color _getOrderTypeColor(ConditionOrderBookModel item) {
    final side = item.side?.toUpperCase();
    if (side == 'B' || side == 'BUY') {
      return vpColor.textAccentGreen;
    } else if (side == 'S' || side == 'SELL') {
      return vpColor.textAccentRed;
    }
    return vpColor.textPrimary;
  }

  void _showDeleteConfirmDialog(ConditionOrderBookModel item) {
    VPPopup.custom(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Hủy lệnh điều kiện',
            style: context.textStyle.headineBold6?.copyWith(
              color: vpColor.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'Bạn có chắc chắn muốn hủy lệnh điều kiện ${item.symbol ?? ''} này?',
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: VpsButton.secondarySmall(
                  title: 'Đóng',
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: VpsButton.primaryDangerSmall(
                  title: 'Hủy lệnh',
                  onPressed: () {
                    Navigator.of(context).pop();
                    _deleteOrder(item);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    ).showDialog(context);
  }

  void _deleteOrder(ConditionOrderBookModel item) {
    _deleteUpdateOrderCubit.deleteOrder(
      DeleteOrderRequest(
        accountId:
            GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
        requestId: "app_${AppHelper().genXRequestID()}",
        via: "V",
        orderId: item.orderId,
        market: MarketType.derivatives.nameServer,
      ),
    );
  }
}
