import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

class GtcEffectiveTimeButton extends StatelessWidget {
  const GtcEffectiveTimeButton({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<PlaceOrderCubit, PlaceOrderState, OrderType?>(
      selector: (state) => state.orderType,
      builder: (_, orderType) {
        if (orderType == OrderType.lo) return const SizedBox.shrink();
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                VPTradingLocalize.current.trading_effective_time,
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),
              const SizedBox(height: 4),
              BlocBuilder<PlaceOrderCubit, PlaceOrderState>(
                buildWhen:
                    (previous, current) => previous.symbol != current.symbol,
                builder: (context, state) {
                  final dateKey = GlobalKey<VPDateTimeHolderCommonState>();
                  return VPDateTimeHolderCommon(
                    key: dateKey,
                    startDate: DateTime.now(),
                    endDate: DateTime.now(),
                    minDate: DateTime.now(),
                    onDateTimeChanged: (data) {
                      context
                          .read<ValidateConditionOrderCubit>()
                          .setEffectiveTime(data.startDate, data.endDate);
                    },
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
