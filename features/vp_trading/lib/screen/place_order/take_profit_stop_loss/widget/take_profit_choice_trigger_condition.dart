import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/model/enum/take_profit_trigger_condition_enum.dart';

class TakeProfitChoiceTriggerCondition extends StatelessWidget {
  const TakeProfitChoiceTriggerCondition({super.key});

  void _handleOnTapChange(
    TakeProfitTriggerConditionEnum triggerCondition,
    BuildContext context,
  ) {
    if (triggerCondition == TakeProfitTriggerConditionEnum.slippageProfit) {
      context.read<ValidateConditionOrderCubit>().setTriggerConditionEnum(
        TakeProfitTriggerConditionEnum.rateProfit,
      );
    } else {
      context.read<ValidateConditionOrderCubit>().setTriggerConditionEnum(
        TakeProfitTriggerConditionEnum.slippageProfit,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      ValidateConditionOrderCubit,
      ValidateConditionOrderState,
      TakeProfitTriggerConditionEnum
    >(
      selector: (state) => state.triggerConditionEnum,
      builder: (context, triggerCondition) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          decoration: BoxDecoration(
            color: vpColor.backgroundElevation0,
            border: Border.all(color: vpColor.strokeNormal),
            borderRadius: const BorderRadius.all(Radius.circular(8)),
          ),
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => _handleOnTapChange(triggerCondition, context),
                  child: SvgPicture.asset(
                    VpTradingAssets.icons.icArrowLeft.path,
                    package: VpTradingAssets.package,
                    width: 20,
                    colorFilter: ColorFilter.mode(
                      vpColor.iconPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              BlocSelector<PlaceOrderCubit, PlaceOrderState, OrderType?>(
                selector: (state) => state.orderType,
                builder: (_, orderType) {
                  return Text(
                    triggerCondition.toStringShowDialog(orderType: orderType),
                    style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                  );
                },
              ),
              Expanded(
                child: InkWell(
                  onTap: () => _handleOnTapChange(triggerCondition, context),
                  child: SvgPicture.asset(
                    VpTradingAssets.icons.icArrowRight.path,
                    package: VpTradingAssets.package,
                    width: 20,
                    colorFilter: ColorFilter.mode(
                      vpColor.iconPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
