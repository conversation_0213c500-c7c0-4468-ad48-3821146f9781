import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/advance_payment/param/advance_success_param.dart';
import 'package:vp_utility/screen/recommendation_home/widget/content_expansion_widget.dart';
import 'package:vp_utility/widget/button/button_widget.dart';

class AdvanceSuccessfulPage extends StatelessWidget {
  const AdvanceSuccessfulPage({super.key, required this.param});

  final AdvanceSuccessParam param;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: ListView(
                children: [
                  const SizedBox(height: 16),
                  SvgPicture.asset(
                    VpUtilityAssets.icons.checkCircle.path,
                    package: VpUtilityAssets.package,
                    width: 120,
                    colorFilter: ColorFilter.mode(
                      themeData.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 32, bottom: 16),
                    child: AutoSizeText(
                      VpUtilityLocalize.current.ap_advance_payment_successful,
                      style: vpTextStyle.headineBold6.copyColor(
                        themeData.primary,
                      ),
                      maxLines: 1,
                      minFontSize: 4,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Text(
                    VpUtilityLocalize.current.ap_successful_transaction,
                    style: vpTextStyle.body16.copyColor(themeData.gray500),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: themeData.highlightBg,
                    ),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ContentExpansionWidget(
                          padding: const EdgeInsets.all(0),
                          title: VpUtilityLocalize.current.oc_sub_account,
                          value: VpUtilityLocalize.current.ap_ordinary_full,
                          textStyle: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                        ),
                        ContentExpansionWidget(
                          title:
                              VpUtilityLocalize
                                  .current
                                  .ap_amount_required_advance,

                          value: param.amount.toMoney(),
                        ),
                        ContentExpansionWidget(
                          title: VpUtilityLocalize.current.ap_advance_fee,
                          value: param.fee.toMoney(),
                        ),
                        ContentExpansionWidget(
                          title: VpUtilityLocalize.current.ap_total_advance,
                          value: param.total.toMoney(),
                          textStyle: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          DividerWidget(),
          Padding(
            padding: const EdgeInsets.all(16),
            child: ButtonWidget(
              action: VpUtilityLocalize.current.oc_close,
              onPressed: () {
                context.pop();
                context.pop();
              },
            ),
          ),
        ],
      ),
    );
  }
}
