import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/widget/bottom_sheet_tooltip_widget.dart';

class IncomeStatementExpandedHeaderWidget extends StatelessWidget {
  const IncomeStatementExpandedHeaderWidget({
    super.key,
    this.title,
    this.alignment = Alignment.center,
    this.flex = 1,
    this.showIconToolTip = false,
    this.title2Line = true,
    this.titlePosition,
  });

  final String? title;
  final AlignmentGeometry alignment;
  final int flex;
  final bool showIconToolTip;
  final bool title2Line;
  final TextAlign? titlePosition;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: flex,
      child: Align(
        alignment: alignment,
        child: InkWell(
          onTap:
              showIconToolTip == true
                  ? () {
                    _onShowTooltip(
                      context: context,
                      bottomSheetContent:
                          VpUtilityLocalize.current.totalProfitAndLoss,
                    );
                  }
                  : null,
          child: Row(
            children: [
              Text(
                getTitle(titleData: title ?? ""),
                style: vpTextStyle.captionRegular?.copyWith(
                  color: vpColor.textPrimary,
                ),
                textAlign: titlePosition,
              ),

              showIconToolTip
                  ? Padding(
                    padding: const EdgeInsets.only(left: 5, top: 2),
                    child: VpUtilityAssets.icons.icIToolTip.svg(
                      height: 8.75,
                      width: 8.75,
                    ),
                  )
                  : const SizedBox(),
            ],
          ),
        ),
      ),
    );
  }

  String getTitle({required String titleData}) {
    String title = "";
    if (title2Line == true) {
      return title = getTitle2Line(title: titleData);
    } else {
      title = titleData;
    }
    return title;
  }

  String getTitle2Line({required String title}) {
    // Thay thế ký tự "/" bằng "\n" nếu có
    String replacedText = title.replaceAllMapped(RegExp(r'/'), (match) {
      return "/\n";
    });
    return replacedText;
  }

  void _onShowTooltip({
    required BuildContext context,
    String? bottomSheetContent,
  }) {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AppBottomSheet(
          child: TooltipBottomSheet(bottomSheetContent: bottomSheetContent),
        );
      },
    );
  }
}
