import 'package:flutter/material.dart';
import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/widget/button/button_widget.dart';
import '../data/constants/enums.dart';
import '../data/entities/statement_of_money_entities.dart';

Future showInfoDerivativeTransactionBottomSheet(
  BuildContext context,
  DerivativeStatementEntity model,
) async {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (_) => InfoTransactionBottomSheet(model: model),
  );
}

class InfoTransactionBottomSheet extends StatelessWidget {
  const InfoTransactionBottomSheet({super.key, required this.model});

  final DerivativeStatementEntity model;

  @override
  Widget build(BuildContext context) {
    // Lấy giá trị để hiển thị
    final isCredit = model.credit! > 0;
    final displayValue = (isCredit ? model.credit : model.debit) ?? 0.0;
    final color = isCredit ? themeData.primary : themeData.red;

    return BaseBottomSheet(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        item(title: VpUtilityLocalize.current.time, value: '${model.txdate}'),
        const SizedBox(height: 8),
        item(
          title: VpUtilityLocalize.current.tradingName,
          value: getTransactionDescription(model.tltxcd ?? ''),
        ),
        const SizedBox(height: 8),
        const Divider3Widget(),
        const SizedBox(height: 8),
        item(
          title: VpUtilityLocalize.current.transaction,
          value:
              isCredit
                  ? '+${displayValue.toMoney(symbol: 'đ')}'
                  : '-${displayValue.toMoney(symbol: 'đ')}',
          style: vpTextStyle.body14?.copyWith(color: color),
        ),
        const SizedBox(height: 8),
        item(
          title: VpUtilityLocalize.current.balanceAfterTransaction,
          value: model.balance?.toMoney(symbol: 'đ') ?? '--đ',
          style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
        ),
        const SizedBox(height: 8),
        const Divider3Widget(),
        const SizedBox(height: 8),
        Text(
          VpUtilityLocalize.current.content,
          style: vpTextStyle.body14?.copyWith(color: themeData.gray700),
        ),
        Text('${model.desc}', style: vpTextStyle.body14.copyColor(vpColor.textPrimary),),
        const SizedBox(height: 24),
        ButtonWidget(
          colorBorder: themeData.borderPopUp,
          colorEnable: ColorDefine.transparent,
          textStyle: vpTextStyle.body14?.copyWith(color: themeData.gray700),
          action: VpUtilityLocalize.current.close,
          onPressed: () => context.pop(),
        ),
      ],
    );
  }

  Widget item({
    required String title,
    required String value,
    TextStyle? style,
  }) {
    return Row(
      children: [
        Text(
          title,
          style: vpTextStyle.body14?.copyWith(color: themeData.gray700),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            value,
            style: style ?? vpTextStyle.body14.copyColor(vpColor.textPrimary),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
}
