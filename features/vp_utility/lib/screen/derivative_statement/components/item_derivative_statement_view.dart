import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/screen/derivative_statement/components/info_transaction_bottom_sheet%20copy.dart';
import '../data/constants/enums.dart';
import '../data/entities/statement_of_money_entities.dart';

class ItemDerivativeStatementView extends StatelessWidget {
  const ItemDerivativeStatementView({
    Key? key,
    required this.statement,
    required this.visibleDate,
  }) : super(key: key);

  final DerivativeStatementEntity statement;
  final bool visibleDate;

  @override
  Widget build(BuildContext context) {
    // Lấy giá trị để hiển thị
    final isCredit = statement.credit! > 0;
    final displayValue = (isCredit ? statement.credit : statement.debit) ?? 0.0;

    final color = isCredit ? themeData.primary : themeData.red;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: visibleDate,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
            width: double.infinity,
            color: themeData.highlightBg,
            child: Text(
              '${statement.txdate}',
              style: vpTextStyle.captionRegular?.copyWith(
                color: themeData.gray500,
              ),
            ),
          ),
        ),
        Visibility(visible: !visibleDate, child: const Divider3Widget()),
        InkWell(
          onTap:
              () =>
                  showInfoDerivativeTransactionBottomSheet(context, statement),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    getTransactionDescription(statement.tltxcd ?? ''),
                    style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  isCredit
                      ? '+${displayValue.toMoney(symbol: 'đ')}'
                      : '-${displayValue.toMoney(symbol: 'đ')}',
                  style: vpTextStyle.body14?.copyWith(color: color),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
