import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/cubit/derivative/derivative_statement_bloc.dart';
import 'package:vp_utility/screen/derivative_statement/components/item_derivative_statement_view.dart';
import 'package:vp_utility/screen/derivative_statement/data/entities/statement_of_money_entities.dart';
import 'package:vp_utility/screen/derivative_statement/widget/item_info_widget.dart';
import 'package:vp_utility/screen/derivative_statement/widget/profit_history_filter_view.dart';
import 'package:vp_utility/screen/securities_statement/component/home_stock_loading_widget.dart';
import 'package:vp_utility/widget/none_widget.dart';

import 'data/constants/transaction_type_value_constant.dart';

class DerivativeStatementPage extends StatefulWidget {
  const DerivativeStatementPage({Key? key, this.derivativeAccount = false})
    : super(key: key);

  final bool derivativeAccount;

  @override
  State<DerivativeStatementPage> createState() =>
      _DerivativeStatementPageState();
}

class _DerivativeStatementPageState extends State<DerivativeStatementPage> {
  late DerivativeStatementBloc _bloc;

  @override
  void initState() {
    _bloc = DerivativeStatementBloc();
    // _bloc.initFilterDefault();
    _bloc.onLoadData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DerivativeStatementBloc>(
      create: (cxt) => _bloc,
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              BlocBuilder<DerivativeStatementBloc, DerivativeStatementState>(
                builder:
                    (context, state) => HeaderWidget(
                      subTitle: VpUtilityLocalize.current.service,
                      title: VpUtilityLocalize.current.marginStatement,
                      icon:
                          _bloc.haveFilter
                              ? VpUtilityAssets.icons.icHaveFilter.svg()
                              : VpUtilityAssets.icons.icFilter.svg(),
                      actionRight:
                          () => pickDate(
                            transactionType: state.selectedTransactionType,
                          ),
                    ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 12, 16),
                      child: Row(
                        children: [
                          Text(
                            VpUtilityLocalize.current.subAccount,
                            style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                          ),
                          const Spacer(),
                          Text(
                            VpUtilityLocalize.current.derivative,
                            style: vpTextStyle.body14?.copyWith(
                              color: themeData.gray700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(child: buildContentView()),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future pickDate({TransactionType? transactionType}) async {
    final value = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (_) => ProfitHistoryFilterView(
            maxDate: _bloc.maxDate,
            startDate: _bloc.startDate,
            endDate: _bloc.endDate,
            resetEndDate: _bloc.resetEndDate,
            resetStartDate: _bloc.resetStartDate,
            initialTransactionType: transactionType ?? TransactionType.all,
          ),
    );
    if (value is Tuple3) {
      _bloc.onDatePicked([
        value.item1,
        value.item2,
      ], value.item3 ?? TransactionType.all);
    }
  }

  Widget buildContentView() {
    return BlocBuilder<DerivativeStatementBloc, DerivativeStatementState>(
      builder: (context, state) {
        if (state.isLoading) {
          return HomeStockLoading(count: 10);
        }
        if (state is MoneyStatementErrorState) {
          return ErrorView(
            showErrorImage: true,
            responseError: state.error,
            onTryAgain: () {
              _bloc.onRefresh();
            },
            margin: const EdgeInsets.symmetric(horizontal: 16),
          );
        }
        return PullToRefreshView(
          onRefresh: () async {
            _bloc.onRefresh();
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ItemInfoWidget(
                title: VpUtilityLocalize.current.openingBalance,
                value: _bloc.beginCodeTransaction(),
              ),
              const SizedBox(height: 4),
              ItemInfoWidget(
                title: VpUtilityLocalize.current.closingBalance,
                value: _bloc.endCodeTransaction(),
              ),
              const SizedBox(height: 4),
              ItemInfoWidget(
                title: VpUtilityLocalize.current.aroseDuringPeriod,
                value: _bloc.getOccurredDuringPeriod(),
                addCharacter: true,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: buildTransactionView(
                    data: state.listItemStatement ?? [],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildTransactionView({required List<DerivativeStatementEntity> data}) {
    return ListViewHelper(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      hasMore: () => false,
      noDataView:
          () => NoneWidget(
            desc: VpUtilityLocalize.current.noDataNow,
            padding: const EdgeInsets.only(bottom: 20),
          ),
      itemBuilder: (BuildContext context, int index) {
        final item = data[index];
        final visibleDate =
            index == 0 ? true : (item.txdate != data[index - 1].txdate);
        return ItemDerivativeStatementView(
          statement: item,
          visibleDate: visibleDate,
        );
      },
      itemCount: () => data.length,
    );
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }
}
