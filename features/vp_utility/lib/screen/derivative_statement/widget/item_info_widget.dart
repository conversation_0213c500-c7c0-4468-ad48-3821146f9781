import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_stock_common/extension/color_exts.dart';
class ItemInfoWidget extends StatelessWidget {
  const ItemInfoWidget({
    super.key,
    required this.title,
    required this.value,
    this.addCharacter = false,
  });

  final String title;
  final num value;
  final bool addCharacter;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(child: Text(title, style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),)),
          const SizedBox(width: 16),
          Text(
            value.toMoney(addCharacter: addCharacter, symbol: 'đ'),
            style: vpTextStyle.body14?.copyWith(
              color: addCharacter ? CommonColorUtils.colorValue(value) : null,
            ),
          ),
        ],
      ),
    );
  }
}
