import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';

import '../data/entities/transaction_history_entities.dart';
import '../data/enums/enum.dart';

class TransactionHistoryItem extends StatelessWidget {
  const TransactionHistoryItem({
    super.key,
    this.onTap,
    this.onLongPress,
    required this.transactionHistory,
  });

  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final TransactionHistoryEntity transactionHistory;

  @override
  Widget build(BuildContext context) {
    OrderStatus? orderStatus = getOrderStatusFromString(
      transactionHistory.status ?? '',
    );

    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 2,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Text(
                    //     AppTimeUtils()
                    //         .formatTimeHHMM('${transactionHistory.timecreate}'),
                    //     style: vpTextStyle.body16),
                    Text(
                      "${transactionHistory.tradeTime}",
                      style: vpTextStyle.captionRegular?.copyWith(
                        color: themeData.gray500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    FittedBox(
                      child: Text(
                        "${transactionHistory.symbol}",
                        style: vpTextStyle.body16?.copyColor(
                          vpColor.textPrimary,
                        ),
                      ),
                    ),
                    Text(
                      getDisplayText(transactionHistory.execType ?? ''),
                      style: vpTextStyle.captionRegular?.copyWith(
                        color: getTextColor(transactionHistory.execType ?? ''),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Align(
                alignment: Alignment.centerRight,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      "${formatNumber(transactionHistory.execQty)}/${formatNumber(transactionHistory.qty)}",
                      style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
                    ),
                    Text(
                      getAverageOrPrice(
                        matchqtty: transactionHistory.execQty,
                        avgprice: transactionHistory.execPrice,
                        price: transactionHistory.price,
                        priceType: transactionHistory.priceType,
                      ),
                      style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Align(
                alignment: Alignment.centerRight,
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: orderStatus?.color,
                    borderRadius: const BorderRadius.all(Radius.circular(4.0)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 4,
                      right: 4,
                      bottom: 6,
                      top: 4,
                    ),
                    child: Text(
                      orderStatus?.statusText ?? '',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String getDisplayText(String execType) {
    if (execType == "NB") {
      return "LONG";
    } else if (execType == "NS") {
      return "SHORT";
    } else {
      return "--";
    }
  }

  Color getTextColor(String execType) {
    if (execType == "NB") {
      return themeData.primaryDark;
    } else if (execType == "NS") {
      return themeData.red;
    } else {
      return themeData.white;
    }
  }

  String formatNumber(num? number) {
    if (number == null) {
      return "--";
    }
    final formatter = NumberFormat('#,###');
    return formatter.format(number);
  }

  String getAverageOrPrice({
    num? matchqtty,
    num? avgprice,
    num? price,
    String? priceType,
  }) {
    if (matchqtty == null || avgprice == null || price == null) {
      return "--";
    }
    if (matchqtty > 0) {
      return avgprice.toString();
    } else if (matchqtty == 0) {
      if (priceType == "LO") {
        return price.toString();
      } else {
        return priceType ?? "";
      }
    } else {
      return '';
    }
  }
}
