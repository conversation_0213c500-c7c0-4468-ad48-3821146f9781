import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/button_widget.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/screen/derivative_transaction_history/widget/status_bottom_sheet_select_mutil_item_transaction_history.dart';
import 'btn_transparent_widget.dart';

class TransactionHistoryBottomSheetFilter extends StatefulWidget {
  const TransactionHistoryBottomSheetFilter({super.key});

  @override
  State<TransactionHistoryBottomSheetFilter> createState() =>
      _TransactionHistoryBottomSheetFilterState();
}

class _TransactionHistoryBottomSheetFilterState
    extends State<TransactionHistoryBottomSheetFilter> {
  late List<ItemSelect> listBuySell;
  late List<ItemSelect> listStatus;

  @override
  void initState() {
    super.initState();
    _onInitAndSetDataListType();
    _onInitAndSetDataListStatus();
  }

  void _onInitAndSetDataListType() {
    listBuySell = [
      ItemSelect(title: "Tất cả", id: "ALL", selected: true),
      ItemSelect(title: "Lệnh mua", id: "LM", selected: false),
      ItemSelect(title: "Lệnh bán", id: "LB", selected: false),
    ];
  }

  void _onInitAndSetDataListStatus() {
    listStatus = [
      ItemSelect(id: 1, selected: false, title: "Chờ khớp", value: ["1"]),
      ItemSelect(id: 2, selected: false, title: "Đang khớp", value: ["2"]),
      ItemSelect(id: 3, selected: false, title: "Đã khớp", value: ["3"]),
      ItemSelect(id: 4, selected: false, title: "Chờ hủy", value: ["4"]),
      ItemSelect(id: 5, selected: false, title: "Đã hủy", value: ["5"]),
      ItemSelect(id: 6, selected: false, title: "Từ chối", value: ["6"]),
      ItemSelect(id: 7, selected: false, title: "Hết phiên", value: ["7"]),
    ];
  }

  List<ItemSelect> listGroup = [];

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 24),
            Text(
              "Loại giao dịch",
              style: vpTextStyle.subtitle14?.copyColor(vpColor.textPrimary),
            ),
            ListSelectOne(listItem: listBuySell, callBackItemSelect: (item) {}),
            const SizedBox(height: 24),
            Text(
              "Trạng thái",
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),

            StatusBottomSheetSelectMultiItem(listItem: listStatus),

            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ButtonTransparentWidget(
                    onPress: () {},
                    title: "Làm mới",
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ButtonWidget(
                    action: "Áp dụng",
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
