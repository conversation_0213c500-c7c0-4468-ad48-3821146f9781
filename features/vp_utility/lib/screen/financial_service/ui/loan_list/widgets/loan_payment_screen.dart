// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:vp_common/extensions/double_extensions.dart';
// import 'package:vp_common/utils/app_text_input_formatter_utils.dart';
// import 'package:vp_core/theme/bloc/theme_cubit.dart';
// import 'package:vp_core/vp_core.dart';
// import 'package:vp_design_system/themes/vp_color_old.dart';
// import 'package:vp_utility/generated/l10n.dart';
// import 'package:vp_utility/screen/financial_service/widget/common/app_text_field.dart';
// import 'package:vp_utility/widget/button/button_widget.dart';
//
// import '../../../../../cubit/financial_service/loan_list/cubit/payment/loan_payment_cubit.dart';
// import '../../../../../widget/container_hepler.dart';
//
// class LoanPaymentScreen extends StatefulWidget {
//   final bool isExtend;
//   final double interestE;
//   final double getPrinAmt;
//   final double totalOutStanding;
//   final num autoId;
//
//   const LoanPaymentScreen({
//     super.key,
//     this.isExtend = false,
//     required this.autoId,
//     required this.interestE,
//     required this.getPrinAmt,
//     required this.totalOutStanding,
//   });
//
//   @override
//   State<LoanPaymentScreen> createState() => _LoanPaymentScreenState();
// }
//
// class _LoanPaymentScreenState extends State<LoanPaymentScreen> {
//   late LoanPaymentCubit _bloc;
//   final FocusNode _focus = FocusNode();
//
//   @override
//   void initState() {
//     super.initState();
//     _bloc = context.read<LoanPaymentCubit>();
//     _bloc.moneyController.text =
//         widget.totalOutStanding.formatNumberFundCertificates();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
//     return Container(
//       height: double.infinity,
//       decoration: ContainerHelper.decorationBottom(),
//       padding: const EdgeInsets.only(top: 16),
//       // margin: const EdgeInsets.only(top: SizeUtils.kSize122),
//       child: SafeArea(
//         top: false,
//         child: Scaffold(
//           backgroundColor: Colors.transparent,
//           resizeToAvoidBottomInset: false,
//           body: GestureDetector(
//             onTap: () {
//               FocusScope.of(context).unfocus();
//             },
//             child: Stack(
//               children: [
//                 SingleChildScrollView(
//                   reverse: true,
//                   child: Padding(
//                     padding: EdgeInsets.only(
//                       left: 24,
//                       right: 24,
//                       bottom: keyboardHeight,
//                     ),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.stretch,
//                       children: [
//                         body(),
//                         const SizedBox(height: 16),
//                         // _buildButton(keyboardHeight),
//                       ],
//                     ),
//                   ),
//                 ),
//                 _buildButton(keyboardHeight),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget body() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Align(
//           alignment: Alignment.center,
//           child: Container(
//             width: 56,
//             height: 8,
//             decoration: ShapeDecoration(
//               color: const Color(0xFFE0E0E0),
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(8),
//               ),
//             ),
//           ),
//         ),
//         const SizedBox(height: 12),
//         Text(
//           VpUtilityLocalize.current.loanListFilter,
//           style: vpTextStyle.subtitle16?.copyWith(fontWeight: FontWeight.bold),
//         ),
//         BlocBuilder<LoanPaymentCubit, LoanPaymentState>(
//           builder: (context, state) {
//             return RadioListTile(
//               title: Text(
//                 VpUtilityLocalize.current.paymentAll,
//                 style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
//               ),
//               subtitle: Text(
//                 widget.totalOutStanding.doubleToMoney(),
//                 style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
//               ),
//               value: false,
//               groupValue: state.enable,
//               activeColor: Colors.green,
//               contentPadding: EdgeInsets.zero,
//               onChanged: (value) {
//                 if (state.alvpayamt < state.totalOutStanding) return;
//                 _bloc.moneyController.text =
//                     widget.totalOutStanding.formatNumberFundCertificates();
//                 _bloc.swithEnableTextField(state.enable);
//               },
//             );
//           },
//         ),
//         BlocBuilder<LoanPaymentCubit, LoanPaymentState>(
//           builder: (context, state) {
//             return RadioListTile(
//               title: Text(
//                 VpUtilityLocalize.current.intputOtherMoney,
//                 style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
//               ),
//               value: true,
//               groupValue: state.enable,
//               contentPadding: EdgeInsets.zero,
//               activeColor: Colors.green,
//               onChanged: (value) {
//                 _bloc.onChangeAmount('0');
//                 _bloc.swithEnableTextField(state.enable);
//                 _focus.requestFocus();
//               },
//             );
//           },
//         ),
//         Stack(
//           children: [
//             BlocBuilder<LoanPaymentCubit, LoanPaymentState>(
//               builder:
//                   (context, state) => AppTextField(
//                     enable: state.enable,
//                     typeInput: TextInputType.phone,
//                     hintText: '0',
//                     maxLength: 15,
//                     isAllowMoveCursor: false,
//                     textController: _bloc.moneyController,
//                     isSuccess: state.message.isEmpty,
//                     onChanged: (value) => _bloc.onChangeAmount(value),
//                     messsage: state.message.isNotEmpty ? state.message : null,
//                     listFormatter: [CurrencyInputFormatter()],
//                     focusNode: _focus,
//                   ),
//             ),
//             Positioned(
//               right: 20,
//               top: 8,
//               child: Text(
//                 'đ',
//                 style: vpTextStyle.body14?.copyWith(color: themeData.gray500),
//               ),
//             ),
//           ],
//         ),
//         BlocBuilder<LoanPaymentCubit, LoanPaymentState>(
//           builder: (context, state) {
//             return Text(
//               "${VpUtilityLocalize.current.availableFuns} ${(state.alvpayamt).toDouble().doubleToMoney()}",
//               style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
//             );
//           },
//         ),
//         const SizedBox(height: 16.0),
//         Text(VpUtilityLocalize.current.paid, style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),),
//         const SizedBox(height: 8.0),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               VpUtilityLocalize.current.printAmtPaid,
//               style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
//             ),
//             BlocBuilder<LoanPaymentCubit, LoanPaymentState>(
//               builder: (context, state) {
//                 return Text(
//                   !state.enable
//                       ? widget.getPrinAmt.doubleToMoney()
//                       : _focus.hasFocus
//                       ? state.getPrincipalAmount
//                       : '0 đ',
//                   style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
//                 );
//               },
//             ),
//           ],
//         ),
//         const SizedBox(height: 8.0),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               VpUtilityLocalize.current.interestPaid,
//               style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
//             ),
//             BlocBuilder<LoanPaymentCubit, LoanPaymentState>(
//               builder: (context, state) {
//                 return Text(state.getInterestE, style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),);
//               },
//             ),
//           ],
//         ),
//       ],
//     );
//   }
//
//   //
//   Widget _buildButton(double keyboardHeight) {
//     return Align(
//       alignment: Alignment.bottomCenter,
//       child: Padding(
//         padding: EdgeInsets.only(
//           left: 24,
//           right: 24,
//           bottom: keyboardHeight > 0 ? keyboardHeight + 8 : 0,
//         ),
//         child: SizedBox(
//           height: 44,
//           child: Row(
//             children: [
//               Expanded(
//                 child: ButtonWidget(
//                   action: VpUtilityLocalize.current.close,
//                   colorBorder: themeData.gray900,
//                   textStyle: vpTextStyle.body14?.copyWith(
//                     color: themeData.black,
//                     fontWeight: FontWeight.w500,
//                   ),
//                   colorEnable: themeData.white,
//                   onPressed: () => context.pop(),
//                 ),
//               ),
//               const SizedBox(width: 8),
//               Expanded(
//                 child: BlocBuilder<LoanPaymentCubit, LoanPaymentState>(
//                   builder: (context, state) {
//                     return ButtonWidget(
//                       enable: state.enablePayment,
//                       action: VpUtilityLocalize.current.pay,
//                       textStyle: vpTextStyle.body14?.copyWith(
//                         fontWeight: FontWeight.w500,
//                       ),
//                       onPressed: () => _bloc.postLoanPayment(widget.autoId),
//                     );
//                   },
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
