import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';

class TooltipBottomSheet extends StatelessWidget {
  const TooltipBottomSheet({
    super.key,
    this.bottomSheetTitle,
    this.bottomSheetContent,
    this.btnClose = false,
  });

  final String? bottomSheetTitle;
  final String? bottomSheetContent;
  final bool? btnClose;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        bottomSheetTitle.isNullOrEmpty
            ? const SizedBox()
            : Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                bottomSheetTitle ?? '',
                style: vpTextStyle.subtitle14?.copyWith(fontSize: 16),
              ),
            ),
        Text(bottomSheetContent ?? '', style: vpTextStyle.body14.copyColor(vpColor.textPrimary),),
        const SizedBox(height: 24),
        btnClose == true
            ? InkWell(
              onTap: () {
                context.pop();
              },
              child: Container(
                width: double.infinity,
                height: 40,
                decoration: BoxDecoration(
                  border: Border.all(color: themeData.gray700, width: 1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      VpUtilityLocalize.current.close,
                      style: vpTextStyle.subtitle16.copyColor(
                        vpColor.textPrimary,
                      ),
                    ),
                  ),
                ),
              ),
            )
            : const SizedBox(),
      ],
    );
  }
}
