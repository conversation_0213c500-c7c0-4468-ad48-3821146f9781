import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

class ChartPriceEntity {
  int x;

  double close;

  double? open;

  double volume;

  DateTime time;

  ChartPriceEntity({
    required this.x,
    required this.close,
    required this.time,
    required this.volume,
    this.open,
  });

  String get tradingDate => _formatDDMMYYYY(time);

  String get formattedTime => _formatHHMinutes(time);

  String get priceDisplay =>
      close.getPriceFormatted(currency: '', convertToThousand: true);

  ChartPriceEntity copyWith({
    int? x,
    double? close,
    double? volume,
    DateTime? time,
  }) {
    return ChartPriceEntity(
      x: x ?? this.x,
      close: close ?? this.close,
      volume: volume ?? this.volume,
      time: time ?? this.time,
    );
  }

  String _formatHHMinutes(DateTime time) {
    return _formatTime(time, DateTimeUtils.hourMinute);
  }

  String _formatDDMMYYYY(DateTime time) {
    return _formatTime(time, DateTimeUtils.ddMMYYYY);
  }

  String _formatTime(DateTime time, String format) {
    return DateTimeUtils.dateToString(dateTime: time, format: format);
  }
}

extension ChartPriceEntityExt on ChartPriceEntity {
  ChartVolumeModel toChartVolumeModel() {
    return ChartVolumeModel(
      volume: volume,
      time: time,
      price: close,
      open: open ?? 0,
    );
  }
}

class ChartVolumeModel {
  ChartVolumeModel({
    required this.time,
    required this.volume,
    required this.price,
    required this.open,
  });

  final double volume;

  final DateTime time;

  final double price;

  final double open;
}
