import 'package:vp_common/constants/const_otp.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_wealth/data/model/chart_price_entity.dart';
import 'package:vp_wealth/presentation/model/broker_model.dart';
import 'package:vp_wealth/presentation/place_order/model/securities_portfolio_responses_model.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/stock_order_entity.dart';

import '../data/model/asset/copier_info.dart';
import '../data/model/asset/history_request_model.dart';
import '../data/model/asset/transaction_model.dart';
import '../data/model/assets/item_assets_model.dart';
import '../data/model/assets/nav_model.dart';
import '../data/model/assets/right_model.dart';
import '../data/model/category/category_model.dart';
import '../data/model/contract/wealth_contract_model.dart';
import '../data/model/money_statement_model.dart';
import '../data/model/order/accept_command_data_model.dart';
import '../data/model/order/command_history_model.dart';
import '../data/model/order/investment_command_model.dart';
import '../data/model/order/investment_order_model.dart';
import '../data/model/plan/plan_model.dart';
import '../data/model/stock_detail_entity.dart';
import '../data/model/survey/survey_question_model.dart';
import 'entity/item_expected_performance.dart';
import 'request/fetch_list_command_history_request.dart';
import 'request/fetch_list_investment_command_request.dart';
import 'request/fetch_list_plan_request.dart';
import 'request/order_request.dart';

abstract class WealthRepository {
  // Kiểm tra trạng thái tham gia khảo sát của khách hàng
  Future<int> getSurveyStatus();

  // Lấy danh sách câu hỏi khảo sát
  Future<List<SurveyQuestionModel>> getSurveyQuestions();

  // Trả lời câu hỏi khảo sát
  Future<int> updateSurveyAnswer(Map<String, dynamic> data);

  // Lấy danh mục mẫu
  Future<CategoryModel> getSampleCategory();

  // Lấy trường phải đầu tư và danh mục mẫu
  Future<List<CategoryModel>> getListCategoryByType();

  // Tạo bản nháp kế hoạch
  Future<dynamic> createDraftPLan(PlanModel data);

  // Cập nhật bản nháp kế hoạch
  Future<dynamic> updateDraftPLan(PlanModel data);

  // Cập nhật mã người giới thiệu
  Future<dynamic> updateBrokerNo({
    required int copierId,
    required String brokerNo,
  });

  // Active kế hoạch
  Future<BEBaseResponse> activePlan({required int copierId});

  // Delete kế hoạch
  Future<BEBaseResponse> deletePlan({required int copierId});

  // Lấy mã cổ phiếu sử dụng trong tích sản
  Future<List<ItemData>> getListStockOfWealth();

  // Preview hợp đồng đầu tư
  Future<String> previewContract(PlanModel data);

  // Preview đăng ký hợp đồng đầu tư
  Future<String> previewRegistrationContract(PlanModel data);

  // Preview hợp đồng chỉnh sửa kế hoạch
  Future<String> previewModifiedContract(PlanModel data);

  // Preview hợp đồng dừng đầu tư
  Future<String> previewContractStopInvestment(int copierId);

  // Preview hợp đồng tiếp tục đầu tư
  Future<String> previewContractContinueInvestment(int copierId);

  // Lấy link file hợp đồng
  Future<WealthContractModel> getContractByRequestId(String requestId);

  // Lấy danh sách kế hoạch
  Future<DataListResponse<List<PlanModel>>> getListPlan(
    FetchListPlanRequest request,
  );

  // Lấy danh sách lịch sử lệnh
  Future<DataListResponse<List<CommandHistoryModel>>> getListCommandHistory(
    FetchListCommandHistoryRequest request,
  );

  // Huỷ lệnh
  Future<BEBaseResponse> cancelOrder({
    required int copierId,
    required String transactionId,
  });

  // Lấy danh sách xác nhận lệnh (lệnh tổng)
  Future<DataListResponse<List<InvestmentCommandModel>>>
  getListInvestmentCommand(FetchListInvestmentCommandRequest request);

  // Kích hoạt xác nhận lệnh (lệnh tổng)
  Future<BEBaseResponse> acceptCommand({required int id});

  // Kiểm tra thay đổi trước khi cập nhật
  Future<BEBaseResponse> checkChangePlan(PlanModel data);

  // Cập nhật kế hoạch đang hoạt động và kế hoạch dừng đi lệnh
  Future<BEBaseResponse> updateChangePlan(PlanModel data);

  // Chi tiết kế hoạch
  Future<PlanModel> getDetailPlan(int copierId);

  // Dừng đầu tư
  Future<BEBaseResponse> stopInvestment({required int copierId});

  // Tiếp tục đầu tư
  Future<BEBaseResponse> activeInvestment({required int copierId});

  // Lấy giá trị NAV của các tiểu khoản tích sản
  Future<NavModel?> getValueNAV();

  // Lấy danh mục nắm giữ
  Future<List<ItemAssetsModel>> getListAssetsHeld();

  // Lấy danh sách cổ phiếu
  Future<List<StockDetailEntity>> getStockBySymbols(List<String> symbols);

  //Lay thong tin tieu khoan va danh muc theo custodycd
  Future<List<AccountModel>> getInfoAccount();

  Future<CashInfo> cashDetail({required String copierId});

  Future<BEBaseResponse> depositMoney({
    required int copierId,
    required num amount,
    required String normalAccount,
  });

  Future<BEBaseResponse> withdrawMoney({
    required int copierId,
    required num amount,
    required String normalAccount,
  });

  Future<DataListResponse<List<Transaction>>> cashTransferHistory({
    required HistoriesRequestModel historiesRequestModel,
  });

  // Lich su giao dich tien
  Future<List<MoneyStatementModel>> getCashStatementHist({
    required String subAccountId,
    required String accountId,
    required String fromDate,
    required String toDate,
  });

  // Chủ động mua cổ phiếu
  Future<BEBaseResponse> proactiveBuyStock(OrderForWealthRequest request);

  // Chủ động bán cổ phiếu
  Future<BEBaseResponse> proactiveSellStock(OrderForWealthRequest request);

  // Chi tiết một danh mục nắm giữ
  Future<ItemAssetsModel?> detailAssetsHeld(int copierId);

  // Chi tiết xác nhận lệnh tổng
  Future<InvestmentCommandModel> detailAcceptCommand(int id);

  // Chủ động đầu tư thêm
  Future<int> orderSummary({required int copierId, required num totalInvest});

  // Lấy danh sách quyền mua
  Future<RightModel> getRightOffList({required int copierId});

  // Tính tài sản dự kiến
  Future<ExpectedPerformanceModel?> calculateExpectedPerformance(
    PlanModel data,
  );

  // Lấy danh sách sổ lệnh
  Future<DataListResponse<List<InvestmentOrderModel>>> getListInvestmentOrder(
    int page,
    int pageSize,
    String? status,
    String? investType,
    String? transactionKind,
    String? fromDate,
    String? toDate,
  );
  // Chi tiết xác nhận lệnh đã xác nhận
  Future<DataListResponse<List<AcceptCommandDataModel>>> getDetailAcceptCommand(
    String type,
    int id,
    String status,
  );

  Future<BrokerModel> getBrokerInfo(String accountNo);

  Future<Map<String, List<ChartPriceEntity>>> getPriceCharts(
    String symbols, {
    int frequent = 5,
  });

  Future<List<ChartPriceEntity>> getChartDetail({
    required String symbol,
    required String chartType,
  });

  Future<List<SecuritiesPortfolioResponsesModel>> getSecuritiesPortfolio(
    String accountId,
  );

  Future<List<StockDetailEntity>> getStocks(String symbols);

  Future<List<StockOrderEntity>> getAllStockAtSplash();

}

class BEBaseResponse with BaseResponseError {
  dynamic status;

  dynamic data;

  dynamic httpStatus;

  BEBaseResponse.fromJson(dynamic json) {
    code = json['code'] ?? json['ec'];
    message = json['em'] as String? ?? json['message'] as String?;
    status = json['status'] ?? json['s'];
    data = json['data'];
    httpStatus = json['httpStatus'];
  }

  BEBaseResponse({dynamic code, String? message, this.status, this.data}) {
    this.code = int.tryParse(code.toString())?.abs();
    this.message = message;
  }

  bool isSuccess() {
    return code?.toString() == '0' ||
        code?.toString() == '200' ||
        status?.toString() == '1' ||
        status?.toString() == 'ok';
  }

  bool is503() {
    return (httpStatus ?? '') == '503';
  }

  bool isCancelCentralize() {
    return status == closeInputOTPErrorCode;
  }

  bool isEmpty() {
    return code?.toString() == 'IVBERR02';
  }
}

class DataListResponse<T> {
  dynamic status;
  String? message;
  dynamic code;
  int? totalPages;
  int? totalElements;
  int? currentPage;
  T data;

  DataListResponse({
    required this.status,
    required this.data,
    this.message,
    this.totalPages,
    this.totalElements,
    this.currentPage,
  });

  factory DataListResponse.fromJson(
      Map<String, dynamic> json, T Function(Object? json) fromJsonT) {
    return DataListResponse<T>(
      status: json['status'],
      message: json['message'],
      totalPages: json['totalPages'],
      totalElements: json['totalElements'],
      currentPage: json['currentPage'],
      data: fromJsonT(json['data']),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": this.status,
        "message": this.message,
        "data": this.data,
      };

  bool isSuccess() {
    return code?.toString() == '0' ||
        code?.toString() == '200' ||
        status?.toString() == '1' ||
        status?.toString() == 'ok';
  }

  bool isCancelCentralize() {
    return status == closeInputOTPErrorCode;
  }

  bool isEmpty() {
    return code?.toString() == 'IVBERR02';
  }
}
