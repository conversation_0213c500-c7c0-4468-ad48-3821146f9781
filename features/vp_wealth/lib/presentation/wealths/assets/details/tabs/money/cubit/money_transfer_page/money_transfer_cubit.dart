import 'package:flutter/material.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/enum/money_tranfer_enum.dart';
import 'package:vp_wealth/data/enum/type_select_plan.dart';
import 'package:vp_wealth/data/model/asset/copier_info.dart';
import 'package:vp_wealth/data/model/asset/money_tranfer_result.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/router/wealth_router.dart';

import 'money_transfer_state.dart';

// Cubit để quản lý trạng thái chuyển tiền
class WealthMoneyTransferCubit extends Cubit<WealthMoneyTransferState> {
  WealthMoneyTransferCubit()
    : super(
        WealthMoneyTransferState(
          transferAmount: 0,
          transferContent: '',
          isValid: false,
        ),
      );

  late TextEditingController textController;
  late TextEditingController textContentController;
  late ItemAssetsModel? itemAssetsModel;
  late MoneyTranferEnum moneyTranferEnum;
  List<AccountModel> lstAccountModel = [];

  void init({required ItemAssetsModel itemAssetsModel}) async {
    getListAccount();
    moneyTranferEnum =
        itemAssetsModel.moneyTranferEnum ??
        MoneyTranferEnum.DetailAssetHeldScreen;
    // if (itemAssetsModel.accountNo.isNullOrEmpty) {
    //   moneyTranferEnum = MoneyTranferEnum.AssetHeldScreen;
    // } else {
    //   moneyTranferEnum = MoneyTranferEnum.DetailAssetHeldScreen;
    // }
    textController = TextEditingController(
      text: state.transferAmount.toString(),
    );
    textContentController = TextEditingController(
      text: state.transferContent.toString(),
    );
    Future.delayed(
      const Duration(milliseconds: 200),
    ).then((value) => getInfoAccount());
  }

  Future<void> getInfoAccount() async {
    try {
      final data =
          await GetIt.instance.get<WealthRepository>().getInfoAccount();
      lstAccountModel.addAll(data);
      AccountModel? accountNormal = lstAccountModel.findFirstOrNull(
        (element) => element.copierInfo.isAccountNormal ?? false,
      );
      emit(state.copyWith(listAccountModel: lstAccountModel));
      updateTypeAccount(
        accountModel: accountNormal,
        typeSelectEnum: TypeSelectEnum.transfer,
      );
      emit(state.copyWith(selectedSourceAccount: accountNormal));
    } catch (e) {
      showError(e);
    }
  }

  List<AccountModel> getListAccountReceiving(
    List<AccountModel> accounts,
    bool? isTransfer,
  ) {
    var listAccountModel = accounts;
    //neu tk chuyen la tieu khoan thuong va chon select tieu khoan nhan
    if ((checkSelectAccountTransfer() ?? false) && !(isTransfer ?? false)) {
      listAccountModel =
          listAccountModel.where((account) {
            return !(account.copierInfo.isSelect ?? false) &&
                !(account.copierInfo.isAccountNormal ?? false);
          }).toList();
    }
    return listAccountModel;
  }

  Future depositMoney({
    int? copierId,
    required ItemAssetsModel itemAssetsModel,
  }) async {
    try {
      final context =
          GetIt.instance<NavigationService>().navigatorKey.currentContext!;
      showDialogLoading();
      String? accountNo =
          (state.selectedSourceAccount?.copierInfo.isAccountNormal ?? false)
              ? state.selectedSourceAccount?.accountNo
              : state.selectedDestinationAccount?.accountNo;
      final data = await GetIt.instance.get<WealthRepository>().depositMoney(
        copierId: copierId ?? 0,
        amount: state.transferAmount ?? 0,
        normalAccount: accountNo ?? '',
      );
      hideDialogLoading();
      if (data.isSuccess()) {
        context.push(
          WealthRouter.moneyTransferResult,
          extra: MoneyTranferResultModel(
            accountModelTransfer: state.selectedDestinationAccount!,
            accountModelTo: state.selectedSourceAccount!,
            amount: state.transferAmount ?? 0,
            content: state.transferContent,
            typeMoneyTranfer: moneyTranferEnum,
            itemAssetsModel: itemAssetsModel,
            moneyTranferStatus: MoneyTranferStatus.SUCCESS,
          ),
        );
      }
      return data;
    } catch (e) {
      hideDialogLoading();
      if (e is ResponseError && e.status == smartOTPUnRegistered ||
          e is ResponseError && e.status == closeInputOTPErrorCode) {
        AppKeyboardUtils.dismissKeyboard();
        return;
      }
      final context =
          GetIt.instance<NavigationService>().navigatorKey.currentContext!;
      final message = await getErrorMessage(e);
      context.push(
        WealthRouter.moneyTransferResult,
        extra: MoneyTranferResultModel(
          accountModelTransfer: state.selectedDestinationAccount!,
          accountModelTo: state.selectedSourceAccount!,
          amount: state.transferAmount ?? 0,
          content: state.transferContent,
          typeMoneyTranfer: moneyTranferEnum,
          messageError: message,
          itemAssetsModel: itemAssetsModel,
          moneyTranferStatus: MoneyTranferStatus.FAIL,
        ),
      );
      return [];
    } finally {}
  }

  void getListAccount() {
    List<SubAccountModel> lstNormalAccounts =
        GetIt.instance<SubAccountCubit>().state.subAccountsAll.where((e) {
          return e.accountType == SubAccountType.normal;
        }).toList();
    for (int i = 0; i < lstNormalAccounts.length; i++) {
      lstAccountModel.add(
        AccountModel(
          accountNo: lstNormalAccounts[i].id.toString(),
          copierInfo: CopierInfo(
            id: i,
            name: lstNormalAccounts[i].fullName,
            isAccountNormal: true,
          ),
        ),
      );
    }
  }

  //huyr select
  void updateAccountsNormal(
    List<AccountModel> accounts,
    TypeSelectEnum typeSelectEnum,
  ) {
    switch (typeSelectEnum) {
      case TypeSelectEnum.transfer:
        for (var account in accounts) {
          if (account.copierInfo.typeSelect == TypeSelectEnum.transfer &&
              account.copierInfo.isAccountNormal == true) {
            account.copierInfo.isSelect = false;
          }
        }
        break;
      case TypeSelectEnum.receiving:
        for (var account in accounts) {
          if (account.copierInfo.typeSelect == TypeSelectEnum.receiving &&
              account.copierInfo.isAccountNormal == true) {
            account.copierInfo.isSelect = false;
          } else if (account.copierInfo.typeSelect ==
              TypeSelectEnum.receiving) {
            account.copierInfo.isSelect = false;
          }
        }
        break;
    }
  }

  void updateAccountNoNormal(
    List<AccountModel> accounts,
    TypeSelectEnum typeSelectEnum,
  ) {
    switch (typeSelectEnum) {
      case TypeSelectEnum.transfer:
        for (var account in accounts) {
          if (account.copierInfo.typeSelect == TypeSelectEnum.transfer &&
              account.copierInfo.isAccountNormal != true) {
            account.copierInfo.isSelect = false;
          }
        }
        break;
      case TypeSelectEnum.receiving:
        for (var account in accounts) {
          if (account.copierInfo.typeSelect == TypeSelectEnum.receiving &&
              account.copierInfo.isAccountNormal != true) {
            account.copierInfo.isSelect = false;
          } else if (account.copierInfo.typeSelect ==
              TypeSelectEnum.receiving) {
            account.copierInfo.isSelect = false;
          }
        }
        break;
    }
  }

  void updateTypeAccount({
    AccountModel? accountModel,
    TypeSelectEnum? typeSelectEnum,
  }) {
    onSelectSuggestMoney(
      num.parse(textController.text.toString().replaceAll(',', '')),
    );
    switch (typeSelectEnum) {
      case TypeSelectEnum.transfer:
        for (var element in (state.listAccountModel ?? [])) {
          if (element == accountModel) {
            //nếu ko phải tài khoản thường thì sẽ chuyển toàn bộ trạng thái tài khoản thường về false
            if (!(accountModel?.copierInfo.isAccountNormal ?? false)) {
              updateAccountsNormal(
                state.listAccountModel ?? [],
                typeSelectEnum!,
              );
            }
            //nếu  phải tài khoản thường thì sẽ chuyển toàn bộ trạng thái tài khoản ! thường về false
            if (accountModel?.copierInfo.isAccountNormal ?? false) {
              updateAccountNoNormal(
                state.listAccountModel ?? [],
                typeSelectEnum!,
              );
            }
            element.copierInfo.isSelect = true;
            element.copierInfo.typeSelect = typeSelectEnum;
            updateSourceAccount(element);
          }
        }
        break;
      case TypeSelectEnum.receiving:
        for (var element in (state.listAccountModel ?? [])) {
          if (element == accountModel) {
            if (!(accountModel?.copierInfo.isAccountNormal ?? false)) {
              updateAccountsNormal(
                state.listAccountModel ?? [],
                typeSelectEnum!,
              );
            }
            element.copierInfo.isSelect = true;
            element.copierInfo.typeSelect = typeSelectEnum;
            updateDestinationAccount(element);
          }
        }
        break;
      case null:
        throw UnimplementedError();
    }
  }

  //kieerm tra tai khoan normal o phan truyen tien co duoc select khong
  bool? checkSelectAccountTransfer() {
    state.listAccountModel?.any((account) {
      account;
      return (account.copierInfo.isSelect ?? false) &&
          (account.copierInfo.isAccountNormal ?? false) &&
          (account.copierInfo.typeSelect == TypeSelectEnum.transfer);
    });
  }

  Future withdrawMoney({
    int? copierId,
    required ItemAssetsModel itemAssetsModel,
  }) async {
    try {
      final context =
          GetIt.instance<NavigationService>().navigatorKey.currentContext!;
      showDialogLoading();
      String? accountNo =
          (state.selectedSourceAccount?.copierInfo.isAccountNormal ?? false)
              ? state.selectedSourceAccount?.accountNo
              : state.selectedDestinationAccount?.accountNo;
      final data = await GetIt.instance.get<WealthRepository>().withdrawMoney(
        copierId: copierId ?? 0,
        amount: state.transferAmount ?? 0,
        normalAccount: accountNo ?? '',
      );
      hideDialogLoading();
      if (data.isSuccess()) {
        context.push(
          WealthRouter.moneyTransferResult,
          extra: MoneyTranferResultModel(
            accountModelTransfer: state.selectedDestinationAccount!,
            accountModelTo: state.selectedSourceAccount!,
            amount: state.transferAmount ?? 0,
            content: state.transferContent,
            itemAssetsModel: itemAssetsModel,
            typeMoneyTranfer: moneyTranferEnum,
            moneyTranferStatus: MoneyTranferStatus.SUCCESS,
          ),
        );
      }

      return data;
    } catch (e) {
      hideDialogLoading();
      if (e is ResponseError && e.status == smartOTPUnRegistered ||
          e is ResponseError && e.status == closeInputOTPErrorCode) {
        AppKeyboardUtils.dismissKeyboard();
        return;
      }
      final context =
          GetIt.instance<NavigationService>().navigatorKey.currentContext!;
      final message = await getErrorMessage(e);
      context.push(
        WealthRouter.moneyTransferResult,
        extra: MoneyTranferResultModel(
          accountModelTransfer: state.selectedDestinationAccount!,
          accountModelTo: state.selectedSourceAccount!,
          amount: state.transferAmount ?? 0,
          typeMoneyTranfer: moneyTranferEnum,
          itemAssetsModel: itemAssetsModel,
          content: state.transferContent,
          messageError: message,
          moneyTranferStatus: MoneyTranferStatus.FAIL,
        ),
      );
      return [];
    } finally {}
  }

  Future<CashInfo?> getCashDetail(String? copierId) async {
    try {
      final data = await GetIt.instance.get<WealthRepository>().cashDetail(
        copierId: copierId ?? '',
      );
      textController.text = '0';
      return data;
    } catch (e) {
      showError(e);
    } finally {}
  }

  Future<void> sendMoney(ItemAssetsModel itemAssetsModel) async {
    if (state.selectedSourceAccount == null &&
        state.selectedDestinationAccount == null) {
      emit(
        state.copyWith(
          isValidSelectedDestinationAccount: true,
          isValidSelectedSourceAccount: true,
        ),
      );
      return;
    }
    if (!validateTransfer(state.transferAmount.toString())) {
      return;
    }
    if (state.selectedSourceAccount?.copierInfo.name == 'Tiểu khoản thường') {
      depositMoney(
        copierId: state.selectedDestinationAccount?.copierInfo.id ?? 0,
        itemAssetsModel: itemAssetsModel,
      );
    } else {
      withdrawMoney(
        copierId: state.selectedSourceAccount?.copierInfo.id ?? 0,
        itemAssetsModel: itemAssetsModel,
      );
    }
  }

  // Cập nhật tài khoản nguồn
  void updateSourceAccount(AccountModel? accountModel) async {
    final bool isRegularSubAccount =
        accountModel?.copierInfo.isAccountNormal == true;
    final bool isDifferentAccount =
        accountModel != state.selectedDestinationAccount;

    CashInfo? cashInfo = await getCashDetail(
      isRegularSubAccount
          ? '${accountModel?.accountNo}.1'
          : accountModel?.copierInfo.id.toString(),
    );

    var newState = state.copyWith(
      selectedSourceAccount: accountModel,
      isValidSelectedSourceAccount: true,
      baldefovd: cashInfo?.baldefovd.toString(),
    );

    if (isRegularSubAccount) {
      newState = newState.copyWith(
        hideListAccount: false,
        hideSubAccount: true,
        selectedDestinationAccount: null,
        isValidSelectedDestinationAccount: false,
      );

      if (!isDifferentAccount) {
        newState = newState.copyWith(
          selectedDestinationAccount: null,
          isValidSelectedDestinationAccount: false,
        );
      }
    } else {
      newState = newState.copyWith(hideListAccount: true);

      if (state.selectedDestinationAccount?.copierInfo.isAccountNormal ??
          false) {
        // No changes needed, just emit newState
      } else if (isDifferentAccount) {
        newState = newState.copyWith(
          hideSubAccount: false,
          isValidSelectedDestinationAccount: false,
        );
      } else {
        newState = newState.copyWith(
          hideSubAccount: false,
          selectedDestinationAccount: null,
          isValidSelectedDestinationAccount: false,
        );
      }
    }

    emit(newState);
    enableButton();
  }

  // Cập nhật tài khoản đích
  void updateDestinationAccount(AccountModel? accountModel) {
    final bool isRegularSubAccount =
        accountModel?.copierInfo.isAccountNormal == true;
    final bool isDifferentAccount = accountModel != state.selectedSourceAccount;

    var newState = state.copyWith(
      selectedDestinationAccount: accountModel,
      isValidSelectedDestinationAccount: true,
    );

    if (!isRegularSubAccount && !isDifferentAccount) {
      newState = newState.copyWith(
        selectedSourceAccount: null,
        isValidSelectedSourceAccount: false,
      );
    }

    emit(newState);
    hideListAccount(accountModel);
    enableButton();
  }

  //https://jira.vpbanks.com.vn/browse/TS-174
  void hideListAccount(AccountModel? accountModel) {
    if (accountModel?.copierInfo.name != 'Tiểu khoản thường') {
      state.copyWith(hideListAccount: true);
    } else {
      state.copyWith(hideListAccount: false);
    }
  }

  // Cập nhật số tiền
  void updateTransferAmount(String? amount) {
    num transferAmount =
        num.tryParse(amount.toString().replaceAll(',', '')) ?? 0;
    onSelectSuggestMoney(transferAmount);
    enableButton();
  }

  bool? enableButton() {
    bool? enableButton =
        state.selectedSourceAccount != null &&
        state.isValid == false &&
        state.selectedDestinationAccount != null &&
        ((state.transferAmount ?? 0) > 0) &&
        (state.transferAmount ?? 0) <= (num.parse(state.baldefovd ?? '0'));
    emit(state.copyWith(enableButton: enableButton));
  }

  // Cập nhật nội dung chuyển khoản
  void updateTransferContent(String content) {
    emit(state.copyWith(transferContent: content));
  }

  // Xác thực điều kiện hợp lệ để chuyển tiền
  bool validateTransfer(String amount) {
    return amount.isNotEmpty && amount != '0';
  }

  void onSelectSuggestMoney(num value) {
    textController.text = value.toFormat3();
    textController.selection = TextSelection.fromPosition(
      TextPosition(offset: textController.text.length),
    );
    num amount = num.tryParse(value.toString().replaceAll(',', '')) ?? 0;
    bool? isValid = amount > num.parse(state.baldefovd ?? '0');
    emit(state.copyWith(transferAmount: amount, isValid: isValid));
  }
}
