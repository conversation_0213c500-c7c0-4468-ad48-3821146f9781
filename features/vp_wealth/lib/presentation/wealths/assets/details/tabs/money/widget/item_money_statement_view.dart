import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/money_statement_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/widget/info_transaction_bottom_sheet.dart';

class ItemMoneyStatementView extends StatelessWidget {
  const ItemMoneyStatementView({
    Key? key,
    required this.statement,
    required this.visibleDate,
  }) : super(key: key);

  final MoneyStatementModel statement;
  final bool visibleDate;

  @override
  Widget build(BuildContext context) {
    final increase = statement.creditAmt != null && statement.creditAmt! > 0;

    final color = increase ? themeData.primary : themeData.red;

    var money = (increase ? statement.creditAmt : statement.debitAmt) ?? 0.0;

    money = increase ? money : (money * -1.0);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: visibleDate,
          child: Container(
            padding: const EdgeInsets.symmetric(
              vertical: SizeUtils.kSize4,
              horizontal: SizeUtils.kSize16,
            ),
            width: double.infinity,
            color: themeData.highlightBg,
            child: Text(
              statement.busDate?.toDate() ?? '',
              style: vpTextStyle.body14?.copyWith(color: themeData.gray500),
            ),
          ),
        ),
        Visibility(visible: !visibleDate, child: const Divider3Widget()),
        InkWell(
          onTap: () => showInfoTransactionBottomSheet(context, statement),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: SizeUtils.kSize16,
              vertical: SizeUtils.kSize12,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    statement.tltxdesc ?? '',
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ),
                ),
                kSpacingWidth16,
                Text(
                  money.toMoney(addCharacter: true),
                  style: vpTextStyle.body16?.copyWith(color: color),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
