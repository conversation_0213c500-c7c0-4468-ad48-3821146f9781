import 'package:flutter/material.dart';
import 'package:vp_core/utils/go_router_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/money_tranfer_enum.dart';
import 'package:vp_wealth/data/model/asset/money_tranfer_result.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_key_lang.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_localized_values.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class MoneyTransferResult extends StatefulWidget {
  const MoneyTransferResult({super.key, required this.model});

  final MoneyTranferResultModel model;

  @override
  State<MoneyTransferResult> createState() => _MoneyTransferResultState();
}

class _MoneyTransferResultState extends State<MoneyTransferResult> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Stack(
          children: [
            SizedBox(
              width: double.infinity,
              child:
                  widget.model.moneyTranferStatus == MoneyTranferStatus.SUCCESS
                      ? Assets.images.backgroundSuccess.image(fit: BoxFit.fill)
                      : Assets.images.backgroundError.image(fit: BoxFit.fill),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                margin: EdgeInsets.only(
                  top: (108 + MediaQuery.of(context).padding.top),
                ),
                decoration: BoxDecoration(
                  color: vpColor.backgroundElevation0,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(SizeUtils.kSize16),
                    topRight: Radius.circular(SizeUtils.kSize16),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: SizeUtils.kSize16,
                  ),
                  child: Column(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            kSpacingHeight48,
                            widget.model.moneyTranferStatus ==
                                    MoneyTranferStatus.SUCCESS
                                ? successWidget()
                                : failedWidget(),
                            kSpacingHeight8,
                            buildInfoTransferMoney(context),
                          ],
                        ),
                      ),
                      VpsButton.primarySmall(
                        width: double.infinity,
                        title:
                            widget.model.moneyTranferStatus ==
                                    MoneyTranferStatus.SUCCESS
                                ? WealthStock.current.buttonClose
                                : 'Thử lại',
                        onPressed: () {
                          /*switch (widget.model.typeMoneyTranfer) {
                            case MoneyTranferEnum.AssetHeldScreen:
                              navigation.popUntil(WealthRouter.wealthMainPage);
                              break;
                            case MoneyTranferEnum.AcceptCommandPage:
                              navigation
                                  .popUntil(WealthRouter.acceptCommandPage);
                              break;
                            default:
                              navigation.popUntil(
                                  WealthRouter.detailAssetsHeldScreen);
                          }*/
                          if (widget.model.typeMoneyTranfer ==
                              MoneyTranferEnum.AwaitCommandPage) {
                            context.popUntilRoute(
                              WealthRouter.awaitCommandPage,
                            );
                          } else {
                            if (widget.model.moneyTranferStatus ==
                                MoneyTranferStatus.SUCCESS) {
                              /// 2 back, để bỏ qua màn hình nhập số tiền
                              context.pop();
                              context.pop();
                              return;
                            } else {
                              context.popUntilRoute(
                                WealthRouter.moneyTransferPage,
                              );
                              context.pushReplacement(
                                WealthRouter.moneyTransferPage,
                                extra: widget.model.itemAssetsModel,
                              );
                            }
                          }
                        },
                      ),
                      kSpacingHeight16,
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget successWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Assets.images.icSuccess.image(width: 140, height: 140),
        kSpacingHeight8,
        Text(
          getMoneyLang(MoneyKeyLang.transSuccess),
          style: vpTextStyle.headineBold6?.copyWith(color: vpColor.textPrimary),
        ),
        kSpacingHeight4,
        Text(
          'Lệnh chuyển tiền của bạn đã hoàn tất. Vui lòng kiểm tra thông tin như bên dưới.',
          style: vpTextStyle.body14?.copyWith(color: vpColor.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget failedWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Assets.icons.icCancel.svg(width: 140, height: 140),
        kSpacingHeight8,
        Text(
          'Chuyển tiền thất bại',
          style: vpTextStyle.headineBold6?.copyWith(color: vpColor.textPrimary),
        ),
        kSpacingHeight4,
        Text(
          widget.model.messageError ?? '',
          style: vpTextStyle.body14?.copyWith(color: vpColor.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /*--------- Thong tin chuyen tien --------*/
  Widget buildInfoTransferMoney(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: themeData.highlightBg,
        borderRadius: BorderRadius.circular(SizeUtils.kSize8),
      ),
      child: Column(
        children: [
          const SizedBox(height: SizeUtils.kSize8),
          buildContentText(
            context,
            getMoneyLang(MoneyKeyLang.transFrom),
            widget.model.accountModelTo.copierInfo.name,
          ),
          buildContentText(
            context,
            getMoneyLang(MoneyKeyLang.transTo),
            widget.model.accountModelTransfer.copierInfo.name,
          ),
          buildContentText(
            context,
            getMoneyLang(MoneyKeyLang.transMoney),
            widget.model.amount.toMoney(),
            isBold: true,
            color: vpColor.textBrand,
          ),
          buildContentText(
            context,
            getMoneyLang(MoneyKeyLang.transContent),
            widget.model.content ?? '-',
          ),
          const SizedBox(height: SizeUtils.kSize16),
        ],
      ),
    );
  }

  /*--------- Build Text Content --------*/
  Padding buildContentText(
    BuildContext context,
    String title,
    String content, {
    bool isBold = false,
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(
        top: SizeUtils.kSize8,
        left: SizeUtils.kSize16,
        right: SizeUtils.kSize16,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              title,
              style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
              textAlign: TextAlign.start,
            ),
          ),
          Expanded(
            child: Text(
              content.trim(),
              style: vpTextStyle.subtitle14?.copyWith(
                color: color ?? vpColor.textPrimary,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
