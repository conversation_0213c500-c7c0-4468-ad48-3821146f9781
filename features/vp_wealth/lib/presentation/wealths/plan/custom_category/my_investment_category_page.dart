import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/extensions/price_exts.dart';
import 'package:vp_common/utils/app_text_input_formatter_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/chart/chart_data_investment_category.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/app_keyboard_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/loading_utils.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import 'package:vp_wealth/presentation/wealths/plan/cubit/wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/cubit/custom_category_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/cubit/custom_category_validate_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/item_investment_stock.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/stock_changed_properties.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/widget/icon_status_price_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/chart_invesment_category.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/text_suggest_widget.dart';
import 'package:vp_wealth/presentation/wealths/search/bloc/search_stock_bloc.dart';
import 'package:vp_wealth/presentation/wealths/search/search_stocks_page.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/presentation/bloc/details_stock_bloc.dart';
import 'package:vp_wealth/presentation/wealths/widgets/bottom_sheet/bottom_sheet_investment_guide_line.dart';
import 'package:vp_wealth/presentation/wealths/widgets/utils/right_suffix_money.dart';
import 'package:vp_wealth/wealth_data/wealth_data.dart';

class MyInvestmentCategoryPage extends StatefulWidget {
  final WealthPlanCubit cubit;

  const MyInvestmentCategoryPage({super.key, required this.cubit});

  @override
  State<MyInvestmentCategoryPage> createState() =>
      _MyInvestmentCategoryPageState();
}

class _MyInvestmentCategoryPageState extends State<MyInvestmentCategoryPage> {
  final CustomCategoryCubit _cubit = CustomCategoryCubit();

  WealthPlanCubit get _wealthPlanCubit => widget.cubit;

  late final DetailsStockBloc _bloc = DetailsStockBloc();

  final FocusNode _initialInvestmentAmountFocusNode = FocusNode();
  final FocusNode _periodicInvestmentAmountFocusNode = FocusNode();
  final _cubitValidate = CustomCategoryValidateCubit();
  @override
  void initState() {
    super.initState();
    _cubit.onFetchListData(_wealthPlanCubit);
    _cubit.onFetchListStockCategorySuggest();

    _initialInvestmentAmountFocusNode.addListener(() {
      _wealthPlanCubit.onShowSuggestInitialInvestmentMoney(
        _initialInvestmentAmountFocusNode.hasFocus,
      );
    });
    _periodicInvestmentAmountFocusNode.addListener(() {
      _wealthPlanCubit.onShowSuggestPeriodicInvestmentMoney(
        _periodicInvestmentAmountFocusNode.hasFocus,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _cubitValidate,
      child: GestureDetector(
        onTap: () => AppKeyboardUtils.dismissKeyboard(),
        child: VPScaffold(
          backgroundColor: vpColor.backgroundElevation0,
          appBar: VPAppBar.flows(title: 'Tuỳ chỉnh danh mục'),
          body: Container(
            color: vpColor.backgroundElevation0,
            child: Column(
              children: [
                _infoNote,
                Divider(
                  color: vpColor.backgroundElevationMinus1,
                  height: 8,
                  thickness: 8,
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        _investmentInfoWidget,
                        _allocationWealthPlanNote,
                        _divider,
                        _listStockCategory,
                        _listStockCategorySuggest,
                        _divider,
                        kSpacingHeight8,
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: _buildBottomNavigator(),
        ),
      ),
    );
  }

  Widget get _infoNote {
    return BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.showNoteMyCategory != current.showNoteMyCategory,
      builder: (context, state) {
        if (state.showNoteMyCategory) {
          return Container(
            padding: const EdgeInsets.symmetric(
              vertical: SizeUtils.kSize8,
              horizontal: SizeUtils.kSize12,
            ),
            decoration: BoxDecoration(
              color: vpColor.backgroundAccentBlue.withOpacity(0.16),
              borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
            ),
            child: Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Assets.icons.icInfoCircleBlue.svg(
                  width: 24,
                  height: 24,
                  colorFilter: ColorFilter.mode(
                    vpColor.iconAccentBlue,
                    BlendMode.srcIn,
                  ),
                ),
                kSpacingWidth8,
                Expanded(
                  child: Text(
                    'Lợi nhuận kỳ vọng của Danh mục sẽ được tính toán giả định dựa trên lợi nhuận kỳ vọng của từng cổ phiếu và tỷ trọng được phân bổ của các cổ phiếu trong danh mục',
                    style: vpTextStyle.captionMedium?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ),
                kSpacingWidth8,
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () => _cubit.closeNoteMyCategory(),
                  child: Assets.icons.close.svg(
                    width: 24,
                    height: 24,
                    color: vpColor.iconPrimary,
                  ),
                ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget get _investmentInfoWidget {
    return Padding(
      padding: const EdgeInsets.only(
        top: SizeUtils.kSize12,
        left: SizeUtils.kSize16,
        bottom: SizeUtils.kSize12,
        right: SizeUtils.kSize16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Số tiền đầu tư ban đầu',
                style: vpTextStyle.body14?.copyWith(
                  color: vpColor.textTertiary,
                ),
              ).paddingRight16(),
              SizedBox(
                width: 185,
                child: Stack(
                  children: [
                    VPTextField.small(
                      enableInteractiveSelection: false,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        CurrencyInputFormatter(),
                        LengthLimitingTextInputFormatter(13),
                      ],
                      autofocus: false,
                      maxLength: 13,
                      focusNode: _initialInvestmentAmountFocusNode,
                      hintText: WealthStock.current.hintTextEnterAmount,
                      controller:
                          _wealthPlanCubit
                              .textInitialInvestmentAmountController,
                      onChanged: (value) {
                        if (value.isEmpty) {
                          _cubit.updateInitialInvestmentAmount(value);
                          return;
                        }
                        if (_wealthPlanCubit
                            .textInitialInvestmentAmountController
                            .text
                            .endsWith(',000')) {
                          _cubit.updateInitialInvestmentAmount(value);
                          return;
                        }
                        _wealthPlanCubit
                            .textInitialInvestmentAmountController
                            .value = TextEditingValue(text: value + ',000');
                        _wealthPlanCubit
                            .textInitialInvestmentAmountController
                            .selection = TextSelection.collapsed(
                          offset:
                              _wealthPlanCubit
                                  .textInitialInvestmentAmountController
                                  .text
                                  .length -
                              4,
                        );
                        _cubit.updateInitialInvestmentAmount(value);
                      },
                    ),
                    const RightSuffixMoney(),
                  ],
                ),
              ),
            ],
          ),
          kSpacingHeight10,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                WealthStock.current.periodicInvestmentAmount,
                style: vpTextStyle.body14?.copyWith(
                  color: vpColor.textTertiary,
                ),
              ).paddingRight16(), //.paddingRight16(),

              SizedBox(
                width: 185,
                child: BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                  bloc: _cubit,
                  buildWhen:
                      (previous, current) =>
                          previous.showErrorPeriodAmount !=
                          current.showErrorPeriodAmount,
                  builder: (context, state) {
                    return Stack(
                      children: [
                        VPTextField.small(
                          enableInteractiveSelection: false,
                          inputType:
                              !_cubit.state.showErrorPeriodAmount
                                  ? InputType.rest
                                  : InputType.error,
                          keyboardType: TextInputType.phone,
                          inputFormatters: [
                            CurrencyInputFormatter(),
                            LengthLimitingTextInputFormatter(13),
                          ],
                          autofocus: false,
                          focusNode: _periodicInvestmentAmountFocusNode,
                          maxLength: 13,
                          hintText: WealthStock.current.hintTextEnterAmount,
                          controller:
                              _wealthPlanCubit.textMonthlyIncomeController,
                          onChanged: (value) {
                            if (value.isEmpty) {
                              _cubit.updatePeriodicInvestmentAmount(value);
                              return;
                            }
                            if (_wealthPlanCubit
                                .textMonthlyIncomeController
                                .text
                                .endsWith(',000')) {
                              _cubit.updatePeriodicInvestmentAmount(value);
                              return;
                            }
                            _wealthPlanCubit
                                .textMonthlyIncomeController
                                .value = TextEditingValue(text: value + ',000');
                            _wealthPlanCubit
                                .textMonthlyIncomeController
                                .selection = TextSelection.collapsed(
                              offset:
                                  _wealthPlanCubit
                                      .textMonthlyIncomeController
                                      .text
                                      .length -
                                  4,
                            );
                            _cubit.updatePeriodicInvestmentAmount(value);
                          },
                        ),
                        const RightSuffixMoney(),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget get _allocationWealthPlanNote {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: SizeUtils.kSize12,
        left: SizeUtils.kSize16,
        right: SizeUtils.kSize16,
      ),
      child: Container(
        decoration: BoxDecoration(
          color: vpColor.backgroundAccentYellow,
          borderRadius: const BorderRadius.all(Radius.circular(8)),
        ),
        child: Row(
          children: [
            Assets.icons.icQuestion1.svg().paddingRight8(),
            kSpacingWidth4,
            Flexible(
              child: Text(
                WealthStock.current.noteAboutPeriodicInvestmentAmount,
                style: vpTextStyle.captionMedium?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => showBottomSheetRateInvestmentGuideline(context),
              child: Text(
                'Xem ngay',
                style: vpTextStyle.captionMedium?.copyWith(
                  color: vpColor.textAccentYellow,
                ),
              ),
            ).paddingLeft12(),
          ],
        ).paddingSymmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget get _listStockCategory {
    return BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.listStock != current.listStock ||
              previous.listChartData != current.listChartData,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                left: SizeUtils.kSize16,
                right: SizeUtils.kSize16,
                top: SizeUtils.kSize12,
                bottom: SizeUtils.kSize12,
              ),
              child: Text(
                WealthStock.current.investmentCategory,
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeUtils.kSize16,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                    bloc: _cubit,
                    buildWhen:
                        (previous, current) =>
                            previous.totalRate != current.totalRate,
                    builder: (context, state) {
                      return RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: WealthStock.current.rateInvestmentAmount,
                              style: vpTextStyle.captionMedium?.copyWith(
                                color: vpColor.textSecondary,
                              ),
                            ),
                            TextSpan(
                              text: ' ${100 - state.totalRate.toInt()}% ',
                              style: vpTextStyle.subtitle14?.copyWith(
                                color:
                                    state.totalRate != 0
                                        ? vpColor.textAccentRed
                                        : vpColor.textBrand,
                              ),
                            ),
                            TextSpan(
                              text: '/100%',
                              style: vpTextStyle.subtitle14?.copyWith(
                                color: vpColor.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  if (state.listStock.isNotEmpty) _buttonAddStock,
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeUtils.kSize16,
              ),
              child: BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                bloc: _cubit,
                buildWhen:
                    (previous, current) =>
                        previous.totalRate != current.totalRate,
                builder: (context, state) {
                  return (state.totalRate != 0 && state.listStock.isNotEmpty)
                      ? Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Assets.icons.errorTriangle.svg().paddingRight4(),
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: 'Tổng tỷ lệ phân bổ phải đạt 100%',
                                  style: vpTextStyle.body14?.copyWith(
                                    color: vpColor.textAccentRed,
                                  ),
                                ),
                              ],
                            ),
                          ).paddingBottom4(),
                        ],
                      )
                      : const SizedBox();
                },
              ),
            ),
            state.listStock.isEmpty
                ? _listStockCategoryEmpty
                : _listStock(state.listStock).paddingTop12(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  WealthStock.current.investmentCategory,
                  style: vpTextStyle.subtitle16?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
                kSpacingHeight8,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      WealthStock.current.expectedProfit,
                      style: vpTextStyle.body14?.copyWith(
                        color: vpColor.textTertiary,
                      ),
                    ),
                    BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                      bloc: _cubit,
                      buildWhen:
                          (previous, current) =>
                              previous.expectedProfit != current.expectedProfit,
                      builder: (context, state) {
                        return RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text:
                                    state.expectedProfit == 0
                                        ? ' -%/năm'
                                        : ' ${state.expectedProfit.doubleFormatForDigits()}%/năm',
                                style: vpTextStyle.subtitle14?.copyWith(
                                  color: vpColor.textPrimary,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ).paddingOnly(left: 16, right: 16, top: 12),
            if (_cubit.isShowChartStock)
              _chartStock(state.listChartData).paddingVertical(8),
          ],
        );
      },
    );
  }

  Widget _chartStock(List<ChartDataInvestmentCategory> chartData) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      child: ChartInvestmentCategory(chartData: chartData).paddingLeft(12),
    );
  }

  Widget get _listStockCategoryEmpty {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize20),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          children: [
            kSpacingHeight16,
            // SvgPicture.asset(WealthKeyAssets.emptyBox),
            Assets.icons.emptyStock.svg(),
            kSpacingHeight16,
            Text(
              WealthStock.current.emptyStockCategory,
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
            kSpacingHeight8,
            SizedBox(width: 140, child: _buttonAddStock),
            kSpacingHeight16,
          ],
        ),
      ),
    );
  }

  Widget _listStock(List<ItemData> listStock) {
    return BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.initialInvestmentAmount !=
                  current.initialInvestmentAmount ||
              previous.periodicInvestmentAmount !=
                  current.periodicInvestmentAmount,
      builder: (context, state) {
        return Column(
          children:
              listStock
                  .asMap()
                  .map((index, e) {
                    return MapEntry(
                      index,
                      ItemInvestmentStock(
                        data: e,
                        initialAmount:
                            _wealthPlanCubit
                                .textInitialInvestmentAmountController
                                .text
                                .volume,
                        periodicAmount:
                            _wealthPlanCubit
                                .textMonthlyIncomeController
                                .text
                                .volume,
                        onRemove: () => _cubit.onRemoveItemListData(index),
                        onUpdateTotalRate:
                            (data) => _cubit.updateTotalRateAndExpectedProfit(
                              data: data,
                            ),
                      ).paddingOnly(
                        bottom: index == listStock.length - 1 ? 0 : 4,
                      ),
                    );
                  })
                  .values
                  .toList(),
        );
      },
    );
  }

  Widget get _listStockCategorySuggest {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            WealthStock.current.suggestedByVPBankS,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ),
          kSpacingHeight4,
          Text(
            WealthStock.current.suggestedByVPBankSContent,
            style: vpTextStyle.caption2Medium?.copyWith(
              color: vpColor.textTertiary,
            ),
          ),
          kSpacingHeight12,
          SizedBox(
            height: 66,
            child: BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
              bloc: _cubit,
              buildWhen:
                  (previous, current) =>
                      previous.listStockCategorySuggest !=
                      current.listStockCategorySuggest,
              builder: (context, state) {
                return ListView.builder(
                  itemCount: state.listStockCategorySuggest.length,
                  scrollDirection: Axis.horizontal,
                  shrinkWrap: true,
                  itemBuilder: (cxt, index) {
                    ItemData item = state.listStockCategorySuggest[index];
                    return _itemStockSuggest(item: item);
                  },
                );
              },
            ),
          ),
          kSpacingHeight24,
        ],
      ),
    );
  }

  Widget _itemStockSuggest({required ItemData item}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => _showBottomSheetInfoStock(item: item),
      child: Container(
        width: SizeUtils.kSize120,
        height: SizeUtils.kSize72,
        margin: const EdgeInsets.only(right: SizeUtils.kSize10),
        decoration: BoxDecoration(
          border: Border(left: BorderSide(color: themeData.primary, width: 2)),
          image: DecorationImage(
            alignment: Alignment.centerRight,
            image: AssetImage(Assets.images.bgItemStock.path),
          ),
          borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
        ),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: themeData.divider),
            borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: SizeUtils.kSize12,
            vertical: SizeUtils.kSize4,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.symbol,
                style: vpTextStyle.body16?.copyWith(color: themeData.black),
              ),
              Text(
                item.business ?? '',
                style: vpTextStyle.caption2Medium?.copyWith(
                  color: vpColor.textTertiary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget get _buttonAddStock {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SizeUtils.kSize8,
        vertical: SizeUtils.kSize4,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
        border: Border.all(color: vpColor.strokeGray),
      ),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _showBottomSheetSearchStock,
        child: Row(
          children: [
            Assets.icons.icPlus.svg(color: vpColor.iconPrimary).paddingRight8(),
            Text(
              WealthStock.current.addStock,
              style: vpTextStyle.subtitle14?.copyWith(
                color: vpColor.textPrimary,
              ),
            ).paddingBottom4(),
          ],
        ),
      ),
    );
  }

  Future<void> _showBottomSheetSearchStock() async {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context1) {
        return AppBottomSheet(
          isFullSize: true,
          initialChildSize: 0.85 * MediaQuery.of(context).size.height,
          child: BlocProvider<SearchStockBloc>(
            create: (context) => SearchStockBloc(),
            child: SearchStocksPage(
              dataList: _cubit.state.listStock,
              onAddList: (item) => _cubit.onAddItemListData(item, context),
            ),
          ),
        );
      },
    ).then((value) {
      ScaffoldMessenger.of(context).clearSnackBars();
    });
  }

  Future<void> _showBottomSheetInfoStock({required ItemData item}) async {
    _bloc.init(item.symbol);
    await showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StreamBuilder<StockDetailEntity>(
          stream: _bloc.streamStockItem,
          builder: (context, snapshot) {
            if (snapshot.data == null || snapshot.hasError) {
              return const SizedBox();
            }
            StockDetailEntity data = snapshot.data!;

            return AppBottomSheet(
              isFullSize: true,
              padding: const EdgeInsets.only(top: SizeUtils.kSize16),
              initialChildSize: 0.35 * MediaQuery.of(context).size.height,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: SizeUtils.kSize24,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: item.symbol,
                                style: vpTextStyle.headineBold6?.copyWith(
                                  color: vpColor.textPrimary,
                                ),
                              ),
                              TextSpan(
                                text: '  ${data.exchange}',
                                style: vpTextStyle.headine6?.copyWith(
                                  color: vpColor.textTertiary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          data.companyName,
                          style: vpTextStyle.body14?.copyWith(
                            color: vpColor.textSecondary,
                          ),
                        ),
                        _buildPriceSection().paddingVertical(16),
                      ],
                    ),
                  ),
                  // _divider,
                  kSpacingHeight12,
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: SizeUtils.kSize24,
                    ),
                    child: Row(
                      children: [
                        // Expanded(
                        //   child: ButtonWidget(
                        //     action: 'Xem chi tiết',
                        //     colorEnable: themeData.transparent,
                        //     colorBorder: themeData.borderBg,
                        //     textStyle: TextStyleUtils.text14Weight600
                        //         .copyWith(color: themeData.black),
                        //     onPressed: () =>
                        //         _showBottomSheetDetailStock(name: name),
                        //   ),
                        // ),
                        // kSpacingWidth12,
                        Expanded(
                          child: VpsButton.primaryXsSmall(
                            onPressed: () {
                              Navigator.pop(context);
                              _cubit.onAddItemListData(item, context);
                            },
                            disabled: false,
                            alignment: Alignment.center,
                            title: 'Thêm vào danh mục',
                          ),
                          // child: ButtonWidget(
                          //   action: 'Thêm vào danh mục',
                          //   onPressed: () {
                          //     Navigator.pop(context);
                          //     _cubit.onAddItemListData(item, context);
                          //   },
                          // ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildPriceSection({bool isOnlyPrice = false}) {
    return StreamBuilder<StockDetailEntity>(
      stream: _bloc.streamStockItem,
      builder: (context, snapshot) {
        if (snapshot.data == null || snapshot.hasError) {
          return const SizedBox();
        }

        StockDetailEntity data = snapshot.data!;

        final totalVolume = snapshot.data?.totalVolume ?? 0;

        final totalTrading = snapshot.data?.totalTradingValue ?? 0;

        final style = vpTextStyle.captionRegular?.copyWith(
          color: vpColor.textTertiary,
        );

        return Row(
          children: [
            IntrinsicWidth(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  StreamBuilder<dynamic>(
                    stream:
                        _bloc
                            .stockChangedProperties[StockChangedProperties
                                .lastPrice]!
                            .stream,
                    initialData: false,
                    builder: (context, snapshot) {
                      final price = data.currentPrice;
                      final content = price.getPriceFormatted(
                        currency: '',
                        convertToThousand: true,
                      );

                      final styleText = vpTextStyle.headline4?.copyWith(
                        color: data.colorByPrice,
                      );

                      return Container(
                        color:
                            snapshot.data!
                                ? data.colorByPrice.withOpacity(0.16)
                                : null,
                        child: Text(content, style: styleText),
                      );
                    },
                  ).paddingRight8(),
                  Padding(
                    padding: EdgeInsets.only(
                      left: SizeUtils.kSize4,
                      top: isOnlyPrice ? 5 : SizeUtils.kSize16,
                    ),
                    child: StreamBuilder<bool>(
                      stream:
                          _bloc
                              .stockChangedProperties[StockChangedProperties
                                  .changeValue]!
                              .stream,
                      initialData: false,
                      builder: (context, snapshot) {
                        final changeValue = data.changeValue ?? 0;

                        final changePercent = data.changePercent ?? 0;

                        final content = (changeValue.abs()).getPriceFormatted(
                          currency: '',
                          convertToThousand: true,
                        );
                        final content1 = changePercent.getValuePercentAbs();
                        final textStyle = vpTextStyle.captionSemiBold
                            ?.copyWith(color: data.colorByPrice)
                            .copyWith(color: data.colorByPrice);
                        return Container(
                          color:
                              snapshot.data!
                                  ? data.colorByPrice.withOpacity(0.16)
                                  : null,
                          child:
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  IntrinsicWidth(
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        IconStatusUpDownWidget(
                                          value: changeValue,
                                          iconSize: 12,
                                          color: data.colorByPrice,
                                        ),
                                        const SizedBox(width: SizeUtils.kSize4),
                                        Expanded(
                                          child: Text(
                                            content,
                                            style: textStyle,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  IntrinsicWidth(
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        const SizedBox(width: SizeUtils.kSize4),
                                        Expanded(
                                          child: Text(
                                            content1,
                                            style: textStyle,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ).paddingBottom12(),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            kSpacingWidth8,
            isOnlyPrice
                ? const SizedBox.shrink()
                : Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(WealthStock.current.tt, style: style),
                          Text(
                            getTotalTrading(totalVolume),
                            style: vpTextStyle.subtitle16?.copyWith(
                              color: vpColor.textPrimary,
                            ),
                          ),
                        ],
                      ),
                      kSpacingWidth24,
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(WealthStock.current.tv, style: style),
                          Text(
                            getTotalTrading(totalTrading),
                            style: vpTextStyle.subtitle16?.copyWith(
                              color: vpColor.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
          ],
        );
      },
    );
  }

  String getTotalTrading(double totalTrading) {
    if (totalTrading == 0.0) {
      return 0.toString();
    }
    if (totalTrading >= 1000000000) {
      return '${(totalTrading / 1000000000).toFormat2()} ${WealthStock.current.bil}';
    } else if (totalTrading >= 1000000) {
      return '${(totalTrading / 1000000).toFormat2()} ${WealthStock.current.mil}';
    } else if (totalTrading >= 1000) {
      return '${(totalTrading / 1000).toFormat2()} ${WealthStock.current.k}';
    } else {
      return totalTrading.toString();
    }
  }

  Widget get _divider {
    return SizedBox(
      height: 2,
      width: double.infinity,
      child: ColoredBox(color: themeData.buttonDisableBg),
    );
  }

  Widget _rightSuffix() {
    return Align(
      alignment: Alignment.topRight,
      child: Text(
        'đ',
        style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
      ).paddingOnly(top: 4, right: 8),
    );
  }

  //
  Widget _buildSuggestPeriodicInvestmentMoney() {
    return BlocBuilder<WealthPlanCubit, WealthPlanState>(
      bloc: _wealthPlanCubit,
      buildWhen:
          (previous, current) =>
              previous.showSuggestPeriodicInvestmentMoney !=
              current.showSuggestPeriodicInvestmentMoney,
      builder: (context, state) {
        if (!state.showSuggestPeriodicInvestmentMoney) {
          return const SizedBox();
        }
        return WidgetTextSuggest(
          isNeedUnFocus: false,
          textController: _wealthPlanCubit.textMonthlyIncomeController,
          onTap: () {
            _cubit.updatePeriodicInvestmentAmount(
              _wealthPlanCubit.textMonthlyIncomeController.text,
            );
            AppKeyboardUtils.dismissKeyboard();
          },
        );
      },
    );
  }

  Widget _buildsuggestInitialInvestmentMoney() {
    return BlocBuilder<WealthPlanCubit, WealthPlanState>(
      bloc: _wealthPlanCubit,
      buildWhen:
          (previous, current) =>
              previous.showSuggestInitialInvestmentMoney !=
              current.showSuggestInitialInvestmentMoney,
      builder: (context, state) {
        if (!state.showSuggestInitialInvestmentMoney) {
          return const SizedBox();
        }
        return WidgetTextSuggest(
          isNeedUnFocus: false,
          textController:
              _wealthPlanCubit.textInitialInvestmentAmountController,
          onTap: () {
            _cubit.updateInitialInvestmentAmount(
              _wealthPlanCubit.textInitialInvestmentAmountController.text,
            );
            AppKeyboardUtils.dismissKeyboard();
          },
        );
      },
    );
  }

  Widget _buildBottomNavigator() {
    return SafeArea(
      bottom: MediaQuery.of(context).viewInsets.bottom <= 0,
      child: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 60, // Chiều cao vùng đổ bóng
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 30,
                    spreadRadius: 0,
                    offset: const Offset(0, -12),
                  ),
                ],
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(color: Colors.transparent),
            child: Wrap(
              children: [
                Visibility(
                  visible: !(MediaQuery.of(context).viewInsets.bottom > 0),
                  child: BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                    bloc: _cubit,
                    buildWhen:
                        (previous, current) =>
                            previous.listStock != current.listStock,
                    builder: (context, state) {
                      return VpsButton.primarySmall(
                        width: double.infinity,
                        disabled: !state.listStock.isNotEmpty,
                        title: WealthStock.current.buttonSaveChange,
                        onPressed: () async {
                          if (_cubit.state.listStock.isEmpty) return;
                          if (_cubit.validateSaveChange()) {
                            _cubitValidate.validate();
                            return;
                          }

                          LoadingUtil.showLoading();
                          WealthData().allStocksWealth.clear();

                          await WealthData().getAllStockWealth(throwE: true).then((
                            value,
                          ) {
                            LoadingUtil.hideLoading();
                            context.pop(_cubit.state.listStock);
                            showSnackBar(
                              context,
                              'Đã cập nhật bản kế hoạch theo danh mục của tôi.',
                              isSuccess: true,
                            );
                          });
                        },
                      );
                    },
                  ).paddingOnly(left: 16, right: 16, top: 16),
                ),
                _buildsuggestInitialInvestmentMoney(),
                _buildSuggestPeriodicInvestmentMoney(),
              ],
            ),
          ).paddingOnly(bottom: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }
}
