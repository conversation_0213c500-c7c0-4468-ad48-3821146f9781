import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/investment_frequency.dart';
import 'package:vp_wealth/data/model/chart/chart_data_investment_category.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/check_info_plan_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/cubit/edit_wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/chart_invesment_category.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/plan_utils_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/remove_wealth_dialog.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/text_suggest_widget.dart';
import 'package:vp_wealth/presentation/wealths/widgets/bottom_sheet/app_custom_calendar.dart';
import 'package:vp_wealth/presentation/wealths/widgets/utils/right_suffix_money.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class EditWealthPlanPage extends StatefulWidget {
  final EditWealthPlanCubit cubit;

  const EditWealthPlanPage({super.key, required this.cubit});

  @override
  State<EditWealthPlanPage> createState() => _EditWealthPlanPageState();
}

class _EditWealthPlanPageState extends State<EditWealthPlanPage>
    with RouteAware {
  EditWealthPlanCubit get _cubit => widget.cubit;

  PlanModel get _model => _cubit.state.plan!;
  FocusNode initialAmountFocusnode = FocusNode();
  FocusNode periodicAmountFocusnode = FocusNode();
  FocusNode timeFocusnode = FocusNode();
  FocusNode scheduleTimeFocusnode = FocusNode();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void didPopNext() {
    unFocus();
  }

  @override
  void initState() {
    super.initState();
    initialState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void initialState() {
    _cubit.prepareDateForEdit(_model);
    initialAmountFocusnode.addListener(() {
      _cubit.onShowSuggestInitialInvestmentMoney(
        initialAmountFocusnode.hasFocus,
      );
    });
    periodicAmountFocusnode.addListener(() {
      _cubit.onShowSuggestPeriodicInvestmentMoney(
        periodicAmountFocusnode.hasFocus,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return VPScaffold(
      backgroundColor: vpColor.backgroundElevation0,
      appBar: VPAppBar.flows(title: 'Sửa kế hoạch', leading: _close),
      body: SafeArea(
        child: Column(
          children: [
            Divider(
              color: vpColor.backgroundElevationMinus1,
              height: 8,
              thickness: 8,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(height: 12),
                    _allocationMoneyPlan,
                    _investmentGuideToolTip(),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 2,
                      width: double.infinity,
                      child: ColoredBox(color: themeData.buttonDisableBg),
                    ),
                    _chart,
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigator(),
    );
  }

  Widget _buildBottomNavigator() {
    return SafeArea(
      bottom: MediaQuery.of(context).viewInsets.bottom <= 0,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: <BoxShadow>[
            BoxShadow(color: vpColor.backgroundElevation0, blurRadius: 10),
          ],
        ),
        child: Wrap(
          children: [
            Visibility(
              visible: !(MediaQuery.of(context).viewInsets.bottom > 0),
              child: _actionSaveChanged,
            ),
            _buildsuggestInitialInvestmentMoney(),
            _buildSuggestPeriodicInvestmentMoney(),
          ],
        ),
      ).paddingOnly(bottom: MediaQuery.of(context).viewInsets.bottom),
    );
  }

  Widget get _allocationMoneyPlan {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
            bloc: _cubit,
            buildWhen:
                (previous, current) => previous.isExpand != current.isExpand,
            builder: (context, state) {
              return Column(
                children: [
                  // _sotiendautubandau(),
                  _sotiendautudinhky(),
                  _thoigiandautu().paddingVertical(12),
                  _tansuatdautu().paddingBottom12(),
                  _ngaydautudinhky(),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget get _chart {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.listDataStock != current.listDataStock ||
              previous.expectedProfit != current.expectedProfit,
      builder: (context, state) {
        List<ChartDataInvestmentCategory> data =
            state.listDataStock
                .asMap()
                .map(
                  (index, e) => MapEntry(
                    index,
                    e.mapResponseToChartDataInvestmentCategory(index),
                  ),
                )
                .values
                .cast<ChartDataInvestmentCategory>()
                .toList();
        return Padding(
          padding: const EdgeInsets.all(SizeUtils.kSize16).copyWith(top: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// loi nhuan ki vong
              Row(
                children: [
                  Text(
                    WealthStock.current.expectedProfit,
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${_cubit.expectedProfit(state.listDataStock) == 0 ? 'x' : _cubit.expectedProfit(state.listDataStock).doubleFormatForDigits()}%/năm',
                    style: vpTextStyle.subtitle14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ],
              ),

              /// chart
              ChartInvestmentCategory(chartData: data).paddingLeft8(),

              /// danh muc dau tu
              GestureDetector(
                onTap: () async {
                  var result = await context.push(
                    WealthRouter.customInvestmentCategoryPage,
                    extra: _cubit,
                  );
                  if (result == null) return;
                  final mapResult = result as Map<String, dynamic>;
                  num amount = mapResult['amount'] as num;
                  _cubit.textPeriodicInvestmentAmountController.text =
                      amount.valueTextMoney;
                  _cubit.editPeriodicInvestmentAmount(amount);
                  _cubit.updateCustomCategory(mapResult['listStock']);
                },
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Tuỳ chỉnh danh mục đầu tư',
                      style: vpTextStyle.subtitle16?.copyWith(
                        color: vpColor.textBrand,
                      ),
                    ).paddingRight8(),
                    Assets.icons.icArrowRight.svg(width: 20, height: 20),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget get _actionSaveChanged {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.isActive != current.isActive,
      builder: (context, state) {
        return VpsButton.primarySmall(
          disabled: !state.isActive,
          title: WealthStock.current.buttonSaveChange,
          width: double.infinity,
          alignment: Alignment.center,
          onPressed: () {
            _cubit.checkChangePlan((plan, isSuccess) {
              unFocus();
              if (isSuccess) {
                context.push(
                  WealthRouter.checkInfoPlanPage,
                  extra: CheckInfoPlanArguments(model: plan, cubit: _cubit),
                );
              }
            });
          },
        ).paddingAll(16);
      },
    );
  }

  Widget _investmentGuideToolTip() {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: SizeUtils.kSize8,
        horizontal: SizeUtils.kSize12,
      ),
      decoration: BoxDecoration(
        color: vpColor.piechartBlue4.withOpacity(0.16),
        borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Assets.icons.icSuccessNew.svg(),
          kSpacingWidth8,
          Expanded(
            child: Text(
              'Trường hợp ngày đầu tư định kỳ theo kế hoạch rơi vào Ngày nghỉ/Ngày lễ, ngày đầu tư định kỳ thực tế của kỳ đó sẽ được chuyển sang ngày giao dịch kế tiếp.',
              style: vpTextStyle.captionMedium?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
        ],
      ),
    ).paddingSymmetric(horizontal: 16, vertical: 12);
  }

  Widget _sotiendautubandau() {
    if (!_cubit.originalPlanModel.showEditInitialInvestStatus) {
      return const SizedBox.shrink();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          WealthStock.current.amountInitialStart,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ).paddingRight8(),
        SizedBox(
          width: 170,
          child: BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
            bloc: _cubit,
            buildWhen:
                (previous, current) =>
                    previous.showErrorEditInitialInvestmentAmount !=
                    current.showErrorEditInitialInvestmentAmount,
            builder: (context, state) {
              return Stack(
                children: [
                  VPTextField.small(
                    enableInteractiveSelection: false,
                    inputType:
                        state.showErrorEditInitialInvestmentAmount
                            ? InputType.error
                            : InputType.rest,
                    focusNode: initialAmountFocusnode,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [
                      CurrencyInputFormatter(),
                      LengthLimitingTextInputFormatter(13),
                    ],
                    autofocus: false,
                    maxLength: 13,
                    hintText: WealthStock.current.hintTextEnterAmount,
                    controller: _cubit.textInitialInvestmentAmountController,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        _cubit.editInitialInvestmentAmount(
                          _cubit
                              .textInitialInvestmentAmountController
                              .text
                              .volume,
                        );
                        return;
                      }
                      if (_cubit.textInitialInvestmentAmountController.text
                          .endsWith(',000')) {
                        _cubit.editInitialInvestmentAmount(
                          _cubit
                              .textInitialInvestmentAmountController
                              .text
                              .volume,
                        );
                        return;
                      }
                      _cubit
                          .textInitialInvestmentAmountController
                          .value = TextEditingValue(text: value + ',000');
                      _cubit
                          .textInitialInvestmentAmountController
                          .selection = TextSelection.collapsed(
                        offset:
                            _cubit
                                .textInitialInvestmentAmountController
                                .text
                                .length -
                            4,
                      );
                      if (_cubit.onValidateEditInitialInvestmentAmount(value)) {
                        _cubit.editInitialInvestmentAmount(
                          _cubit
                              .textInitialInvestmentAmountController
                              .text
                              .volume,
                        );
                      }
                    },
                  ),
                  const RightSuffixMoney(),
                ],
              );
            },
          ),
        ),
      ],
    ).paddingBottom8();
  }

  Widget _sotiendautudinhky() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          WealthStock.current.periodicInvestmentAmount,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ).paddingRight8(),
        SizedBox(
          width: 170,
          child: BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
            bloc: _cubit,
            buildWhen:
                (previous, current) =>
                    previous.showErrorEditPeriodicInvestmentAmount !=
                    current.showErrorEditPeriodicInvestmentAmount,
            builder: (context, state) {
              return Stack(
                children: [
                  VPTextField.small(
                    enableInteractiveSelection: false,
                    inputType:
                        state.showErrorEditPeriodicInvestmentAmount
                            ? InputType.error
                            : InputType.rest,
                    focusNode: periodicAmountFocusnode,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [
                      CurrencyInputFormatter(),
                      LengthLimitingTextInputFormatter(13),
                    ],
                    autofocus: false,
                    maxLength: 13,
                    hintText: WealthStock.current.hintTextEnterAmount,
                    onChanged: (value) {
                      final shouldEdit = _cubit
                          .onValidateEditPeriodicInvestmentAmount(value);
                      if (value.isEmpty) {
                        if (shouldEdit) {
                          _cubit.editPeriodicInvestmentAmount(
                            _cubit
                                .textPeriodicInvestmentAmountController
                                .text
                                .volume,
                          );
                        }
                        return;
                      }

                      if (_cubit.textPeriodicInvestmentAmountController.text
                          .endsWith(',000')) {
                        if (shouldEdit) {
                          _cubit.editPeriodicInvestmentAmount(
                            _cubit
                                .textPeriodicInvestmentAmountController
                                .text
                                .volume,
                          );
                        }
                        return;
                      }

                      final newText = value + ',000';
                      _cubit
                          .textPeriodicInvestmentAmountController
                          .value = TextEditingValue(
                        text: newText,
                        selection: TextSelection.collapsed(
                          offset: newText.length - 4,
                        ),
                      );

                      if (shouldEdit) {
                        _cubit.editPeriodicInvestmentAmount(newText.volume);
                      }
                    },
                    controller: _cubit.textPeriodicInvestmentAmountController,
                  ),
                  const RightSuffixMoney(),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _thoigiandautu() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              WealthStock.current.investmentTime,
              style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
            ),
          ],
        ).paddingRight8(),
        SizedBox(
          width: 170,
          height: 36,
          child: BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
            bloc: _cubit,
            buildWhen:
                (previous, current) =>
                    previous.valueInvestmentTime !=
                        current.valueInvestmentTime ||
                    previous.showErrorEditInvestmentTime !=
                        current.showErrorEditInvestmentTime,
            builder: (context, state) {
              return VPTextField.small(
                inputType:
                    state.showErrorEditInvestmentTime
                        ? InputType.error
                        : InputType.rest,
                focusNode: timeFocusnode,
                suffixIcon: (_) {
                  return Text(
                    WealthStock.current.year,
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ).paddingTop4();
                },
                controller: _cubit.textInvestmentTimeController,
                keyboardType: TextInputType.phone,
                hintText: WealthStock.current.hintTextEnterYearNumber,
                maxLength: 3,
                autofocus: false,
                onChanged: (value) {
                  if (_cubit.onValidateEditInvestmentTime(value)) {
                    _cubit.editInvestmentTime(
                      num.parse(_cubit.textInvestmentTimeController.text),
                    );
                  }
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _tansuatdautu() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          WealthStock.current.investmentFrequency,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ).paddingRight12(),
        BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
          bloc: _cubit,
          buildWhen:
              (previous, current) =>
                  previous.investmentFrequency != current.investmentFrequency,
          builder: (context, state) {
            return GestureDetector(
              onTap: () {
                PlanUtilsWidget.showBottomSheetEditInvestmentFrequency(
                  context,
                  _cubit,
                );
                AppKeyboardUtils.unFocusTextField();
              },
              child: Container(
                width: 170,
                height: 36,
                decoration: BoxDecoration(
                  border: Border.all(color: vpColor.strokeNormal),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(state.investmentFrequency.title),
                    const Icon(Icons.keyboard_arrow_down_rounded),
                  ],
                ).paddingSymmetric(horizontal: 8, vertical: 4),
              ),
            );
          },
        ),
      ],
    );
  }

  void _onShowCalender(BuildContext context) {
    final now = DateTime.now();
    final defaultMinDay = DateTime(now.year, now.month, now.day + 1);
    final defaultMaxDay = DateTime(now.year + 1, now.month, now.day);
    final initialSelectedDate =
        _cubit.state.scheduleInvestment ?? defaultMinDay;
    showAppCalendar(
      context,
      maxDay: defaultMaxDay,
      minDay: defaultMinDay,
      isRangeMode: false,
      initialSelectedDate: initialSelectedDate,
      callback: (param) {
        DateTime? returnParams = param;
        if (_cubit.onValidateEditPeriodicInvestmentDay(
          returnParams?.day ?? -1,
        )) {
          _cubit.editScheduleInvestment(returnParams);
        }
        return;
      },
    );
  }

  Widget _ngaydautudinhky() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            AppKeyboardUtils.unFocusTextField();
            _onShowCalender(context);
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    WealthStock.current.scheduleInvestment,
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ),
                ],
              ).paddingRight8(),
              Container(
                width: 170,
                height: 36,
                decoration: BoxDecoration(
                  color: vpColor.backgroundElevation0,
                  border: Border.all(color: vpColor.strokeNormal),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
                  bloc: _cubit,
                  builder: (context, state) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${state.scheduleInvestment?.day ?? ''}',
                          style: vpTextStyle.body14?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                        ),
                        const Spacer(),
                        const Icon(Icons.keyboard_arrow_down_rounded),
                      ],
                    ).paddingSymmetric(horizontal: 8);
                  },
                ),
              ),
            ],
          ),
        ),
        BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
          bloc: _cubit,
          buildWhen:
              (previous, current) =>
                  previous.investmentFrequency != current.investmentFrequency ||
                  previous.scheduleInvestment != current.scheduleInvestment,
          builder: (context, state) {
            return state.investmentFrequency == InvestmentFrequency.monthly
                ? Row(
                  children: [
                    Text(
                      '${WealthStock.current.monthlyInvestmentDay} ',
                      style: vpTextStyle.body14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                    Text(
                      '${state.scheduleInvestment!.day}.',
                      style: vpTextStyle.subtitle14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                  ],
                ).paddingTop16()
                : RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        children: [
                          TextSpan(
                            text: ' của các tháng ',
                            style: vpTextStyle.body14?.copyWith(
                              color: vpColor.textPrimary,
                            ),
                            children: [
                              TextSpan(
                                style: vpTextStyle.subtitle14?.copyWith(
                                  color: vpColor.textPrimary,
                                ),
                                text: '${_cubit.buildInvestmentSchedule()}.',
                              ),
                            ],
                          ),
                        ],
                        text: '${state.scheduleInvestment!.day}',
                        style: vpTextStyle.subtitle14?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ],
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                    text: 'Ngày đầu tư định kỳ hàng quý là ngày ',
                  ),
                ).paddingTop16();
          },
        ),
      ],
    );
  }

  Widget _buildsuggestInitialInvestmentMoney() {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.showSuggestInitialInvestmentMoney !=
              current.showSuggestInitialInvestmentMoney,
      builder: (context, state) {
        if (!state.showSuggestInitialInvestmentMoney) {
          return const SizedBox();
        }
        return WidgetTextSuggest(
          isNeedUnFocus: false,
          textController: _cubit.textInitialInvestmentAmountController,
          onTap: () {
            _cubit.editInitialInvestmentAmount(
              _cubit.textInitialInvestmentAmountController.text.volume,
            );
            AppKeyboardUtils.dismissKeyboard();
          },
        );
      },
    );
  }

  Widget _buildSuggestPeriodicInvestmentMoney() {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.showSuggestPeriodicInvestmentMoney !=
              current.showSuggestPeriodicInvestmentMoney,
      builder: (context, state) {
        if (!state.showSuggestPeriodicInvestmentMoney) {
          return const SizedBox();
        }
        return WidgetTextSuggest(
          isNeedUnFocus: false,
          textController: _cubit.textPeriodicInvestmentAmountController,
          onTap: () {
            _cubit.editPeriodicInvestmentAmount(
              _cubit.textPeriodicInvestmentAmountController.text.volume,
            );
            AppKeyboardUtils.dismissKeyboard();
          },
        );
      },
    );
  }

  Widget get _close {
    return InkWell(
      onTap: () => dialogClose(context),
      child: Icon(Icons.close, color: vpColor.textPrimary).paddingTop4(),
    );
  }

  unFocus() {
    initialAmountFocusnode.unfocus();
    periodicAmountFocusnode.unfocus();
    timeFocusnode.unfocus();
    scheduleTimeFocusnode.unfocus();
  }
}
