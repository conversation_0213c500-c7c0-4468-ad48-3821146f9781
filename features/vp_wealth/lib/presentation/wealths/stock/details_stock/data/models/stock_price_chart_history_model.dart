
import 'package:vp_common/utils/date_time_utils.dart';

import '../../domain/entity/stock_price_chart_entity.dart';

class StockPriceChartHistoryModel {
  List<int>? tradingTimes;
  List<num>? openPrices;
  List<num>? closePrices;
  List<num>? volumes;

  StockPriceChartHistoryModel(
      {this.tradingTimes, this.openPrices, this.closePrices, this.volumes});

  StockPriceChartHistoryModel.fromJson(Map<String, dynamic> json) {
    tradingTimes = json['t'].cast<int>();
    openPrices = json['o'].cast<num>();
    closePrices = json['c'].cast<num>();
    volumes = json['v'].cast<num>();
  }
}

extension StockPriceChartHistoryMapper on StockPriceChartHistoryModel {
  List<StockPriceChartEntity> get historyPriceEntities {
    List<StockPriceChartEntity> entities = [];

    if (closePrices != null) {
      int index = 0;
      for (int i = 0; i < closePrices!.length; i++) {
        //only add item has volume > 0 to chart data
        //still add item has volume == 0 for Nhi
        // if (volumes![i] > 0) {

        final tradingTime = tradingTimes == null || i >= tradingTimes!.length
            ? 0
            : tradingTimes![i];

        final volume =
            volumes == null || i >= volumes!.length ? 0 : volumes![i];

        entities.add(StockPriceChartEntity(
          x: index,
          volume: volume,
          closePrice: closePrices![i].toDouble(),
          dateTime: DateTime.fromMillisecondsSinceEpoch(tradingTime * 1000),
        ));
        index++;
        // }
      }
    }

    return entities;
  }

  String _formatHHMinutes(int timeStamp) {
    return _formatTime(timeStamp, DateTimeUtils.hourMinuteSecond);
  }

  String _formatDDMMYYYY(int timeStamp) {
    return _formatTime(timeStamp, DateTimeUtils.ddMMYYYY);
  }

  String _formatTime(int timestamp, String format) {
    DateTime time = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return DateTimeUtils.dateToString(dateTime: time, format: format);
  }
}

List<StockPriceChartEntity> transformStockPriceChartHistoryEntities(
        dynamic json) =>
    StockPriceChartHistoryModel.fromJson(json as Map<String, dynamic>)
        .historyPriceEntities;
