import 'package:collection/collection.dart';
import 'package:vp_common/utils/date_time_utils.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';

class CompanyInfoEntity {
  String? ticker;
  String? organCode;
  String? busTypeCode;
  String? shortName;
  String? fullName;
  String? taxCode;
  String? address;
  String? telephone;
  String? fax;
  String? email;
  String? website;
  String? foundingDate;
  String? enterpriseCode;
  List<OwnerEntity>? ownerEntities;
  int? numberOfEmployee;
  String? companyProfile;
  String? history;
  String? businessLine;
  String? position;
  String? businessStategies;
  String? primaryProduct;
  String? keyLocations;
  num? dividendYield;
  double? beta;
  int? eps;
  double? pe;
  double? bookValue;
  double? pb;
  double? roe;
  double? roa;
  double? evEBITDA;
  num? marketCap;
  ShareHolderEntity? shareHolderEntity;

  CompanyInfoEntity({
    this.ticker,
    this.organCode,
    this.busTypeCode,
    this.shortName,
    this.fullName,
    this.taxCode,
    this.address,
    this.telephone,
    this.fax,
    this.email,
    this.website,
    this.foundingDate,
    this.enterpriseCode,
    this.ownerEntities,
    this.numberOfEmployee,
    this.companyProfile,
    this.history,
    this.businessLine,
    this.position,
    this.businessStategies,
    this.primaryProduct,
    this.keyLocations,
    this.dividendYield,
    this.beta,
    this.eps,
    this.pe,
    this.bookValue,
    this.pb,
    this.roe,
    this.roa,
    this.evEBITDA,
    this.marketCap,
    this.shareHolderEntity,
  });

  String get roeDisplay => '${((roe ?? 0) * 100).toStringAsFixed(2)}%';

  String get dividendYieldDisplay =>
      '${((dividendYield ?? 0) * 100).toStringAsFixed(2)}%';

  String get chairPerson {
    String? chairPerson;
    if (ownerEntities != null && ownerEntities!.isNotEmpty) {
      chairPerson = ownerEntities!
          .firstWhereOrNull((e) => e.position == null
              ? false
              : e.position!.toLowerCase().contains('Chủ tịch'.toLowerCase()))
          ?.fullName;
    }
    return chairPerson ?? '';
  }

  String get generalManager {
    String? chairPerson;
    if (ownerEntities != null && ownerEntities!.isNotEmpty) {
      chairPerson = ownerEntities!
          .firstWhereOrNull((e) => e.position == null
              ? false
              : e.position!
                  .toLowerCase()
                  .contains('Tổng giám đốc'.toLowerCase()))
          ?.fullName;
    }
    return chairPerson ?? '';
  }

  String get establishYear => DateTimeUtils.formatTime(
      inputDate: foundingDate ?? '',
      inputFormat: DateTimeUtils.serverTimeFormat,
      outputFormat: DateTimeUtils.yyyy);

  String get marketCapDisplay =>
      ((marketCap ?? 0) ~/ 1000000000).toMoney(showSymbol: false);
}

class OwnerEntity {
  String? fullName;
  String? position;

  OwnerEntity({
    this.fullName,
    this.position,
  });
}

class ShareHolderEntity {
  double? individual;
  double? corpGovernment;
  double? corpOther;
  double? corpForeign;

  ShareHolderEntity({
    this.individual,
    this.corpGovernment,
    this.corpOther,
    this.corpForeign,
  });

  double get other =>
      100 -
      ((individual ?? 0) +
          (corpGovernment ?? 0) +
          (corpOther ?? 0) +
          (corpForeign ?? 0));
}
