
import 'package:vp_common/vp_common.dart';

class StockNewsEntity {
  String? stockCode;
  String? channelID;
  String? head;
  int? articleID;
  String? title;
  String? publishTime;
  String? content;
  String? source;
  String? author;
  String? uRL;
  String? newsSmallImageURL;
  String? newsUrl;
  double? percentChange;

  StockNewsEntity({
    this.stockCode,
    this.channelID,
    this.head,
    this.articleID,
    this.title,
    this.publishTime,
    this.content,
    this.source,
    this.author,
    this.uRL,
    this.newsUrl,
    this.newsSmallImageURL,
    this.percentChange,
  });

  String get displayTime => publishTime == null
      ? ''
      : DateTimeUtils.formatTime(
          outputFormat: DateTimeUtils.ddMMYYYYHourMinute,
          inputFormat: DateTimeUtils.serverTimeFormat,
          inputDate: publishTime!);

  String get percentChangeDisplay => percentChange == null
      ? ''
      : percentChange!.toPercent(fractionDigits: 2, addPrefixCharacter: true);
}
