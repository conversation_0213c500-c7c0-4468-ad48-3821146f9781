name: vp_wealth
description: "A new Flutter package project."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  go_router: ^15.1.2
  vp_design_system:
    path: ../../library/vp_design_system
  vp_common:
    path: ../../library/vp_common
  vp_core:
    path: ../../library/vp_core
  vp_stock_common:
    path: ../../library/vp_stock_common

  shimmer:
  freezed_annotation: ^3.0.0
  flutter_bloc: ^8.1.6
  json_annotation: ^4.9.0
  tiengviet: ^1.0.0
  tuple: ^2.0.2
  path_provider: ^2.1.5
  fluttertoast: ^8.2.12
  screenshot: ^3.0.0
  share_plus: ^11.0.0
  cached_network_image: ^3.4.1
  image_gallery_saver_plus: ^4.0.1
  flutter_inappwebview: ^6.1.5
  url_launcher: ^6.3.1
  syncfusion_pdfviewer_platform_interface: 31.1.19
  syncfusion_flutter_pdfviewer: 31.1.19
  flutter_switch: 0.3.2
  flutter_slidable: 4.0.0
  syncfusion_flutter_gauges: 31.1.19
  sticky_headers: 0.3.0+2
  just_the_tooltip: ^0.0.12 
  dotted_border: 2.1.0
  easy_localization: 3.0.7+1
  auto_size_text_field: 2.1.1
  auto_size_text: 3.0.0
  connectivity_plus: 6.1.3
  collection: 1.19.1
  flutter_keyboard_visibility: 6.0.0
  easy_debounce: 2.0.3
  pin_code_fields: 8.0.1
  image_gallery_saver: ^2.0.3
  flutter_widget_from_html: 0.16.0
  flutter_html:
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner:
  custom_lint:
  json_serializable:
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:
  freezed:

flutter_gen:
  output: lib/generated/
  line_length: 80
  assets:
    outputs:
      package_parameter_enabled: true

    # Optional
  integrations:
    image: true
    flutter_svg: true
    
 
flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
    - assets/icons/

flutter_intl:
  enabled: true
  class_name: WealthStock
  main_locale: vi