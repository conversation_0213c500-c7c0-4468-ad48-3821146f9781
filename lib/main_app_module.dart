import 'package:flutter/material.dart';
import 'package:multiple_localization/multiple_localization.dart';
import 'package:new_neo_invest/core/repository/home_repository.dart';
import 'package:new_neo_invest/core/repository/notification_repository.dart';
import 'package:new_neo_invest/core/repository/personal_photo_repository.dart';
import 'package:new_neo_invest/core/service/home_service.dart';
import 'package:new_neo_invest/core/service/notification_service.dart';
import 'package:new_neo_invest/core/service/personal_photo_service.dart';
import 'package:new_neo_invest/core/utils/firebase/firebase_messaging.dart';
import 'package:new_neo_invest/core/utils/firebase/location_notification.dart';
import 'package:new_neo_invest/generated/intl/messages_all.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:new_neo_invest/main_navigator.dart';
import 'package:new_neo_invest/screen/ai_code_pilot/ai_code_pilot_get_token_screen.dart';
import 'package:new_neo_invest/screen/ai_code_pilot/ai_code_pilot_screen.dart';
import 'package:new_neo_invest/screen/check_current_status_wealth/check_current_status_wealth_page.dart';
import 'package:new_neo_invest/screen/check_smart_otp/check_smart_otp_page.dart';
import 'package:new_neo_invest/screen/feature/all_feature_screen.dart';
import 'package:new_neo_invest/screen/home_page/widgets/home_personal_photo_page/home_personal_photo_page.dart';
import 'package:new_neo_invest/screen/main_tabbar/main_tabbar_screen.dart';
import 'package:new_neo_invest/screen/popup_update/contract_screen.dart';
import 'package:new_neo_invest/screen/splash/splash_screen.dart';
import 'package:new_neo_invest/screen/stock_home/stock_home_screen.dart';
import 'package:new_neo_invest/screen/welcome/welcome_screen.dart';
import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_common/vp_common.dart'
    show SocketFactory, SocketManager, SocketType;
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

enum MainRouter {
  priceBoard('/noauth-priceBoard'),
  splash('/noauth-splash'),
  welcome('/noauth-welcome'),
  main('/mainTabbar'),
  mainStockHome('/mainStockHome'),
  allFeature('/all_feature'),
  codePilot('/code_pilot'),
  codePilotGetToken('/code_pilot_get_token'),
  supperCombo('/supper_combo'),
  derivativeMarketPage('/derivativeMarketPage'),
  derivativeHomePage('/derivativeHomePage'),
  checkSmartOTPPage('/checkSmartOTPPage'),
  contract('/contract'),
  checkCurrentStatusWealth('/checkCurrentStatusWealth'),
  homePersonalPhoto('/homePersonalPhoto');

  const MainRouter(this.routeName);

  final String routeName;
}

class MainAppModule implements Module {
  @override
  void injectServices(GetIt service) {
    service
      ..registerFactory<LocalNotificationManager>(
        LocalNotificationImplement.new,
      )
      ..registerLazySingleton(
        () => FirebaseMessagingManager(service())..init(),
      )
      ..registerLazySingleton(() => NotificationService(service()))
      ..registerLazySingleton(() => HomeService(service()))
      ..registerLazySingleton(() => PersonalPhotoService(service()))
      ..registerLazySingleton<NotificationRepository>(
        () => NotificationRepositoryImpl(notificationService: service()),
      )
      ..registerLazySingleton<HomeRepository>(
        () => HomeRepositoryImpl(homeService: service()),
      )
      ..registerLazySingleton<PersonalPhotoRepository>(
        () => PersonalPhotoRepositoryImpl(personalPhotoService: service()),
      )
      ..registerLazySingleton<MainNavigator>(
        MainNavigatorImpl.new,
      );

    (SocketFactory.get(SocketType.stock)) as IsolateInvestSocket
      ..connect()
      ..setThrottleRules([
        ThrottleRule(
          channel: VPSocketChannel.fuTopNPrice.name,
          intervalMs: 0,
        ),
      ]);
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: MainRouter.splash.routeName,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: MainRouter.welcome.routeName,
        builder: (context, state) => const WelcomeScreen(),
      ),
      GoRoute(
        path: MainRouter.main.routeName,
        builder: (context, state) => const MainTabbarScreen(),
      ),
      GoRoute(
        name: MainRouter.codePilotGetToken.routeName,
        path: MainRouter.codePilotGetToken.routeName,
        pageBuilder: (context, state) {
          return CustomTransitionPage(
            opaque: false,
            barrierDismissible: true,
            barrierColor: Colors.black.withOpacity(0.4),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return FadeTransition(opacity: animation, child: child);
            },
            child: const AiCodePilotGetTokenScreen(),
          );
        },
      ),
      GoRoute(
        path: MainRouter.codePilot.routeName,
        builder: (context, state) => CodePilotAiScreen(
          tokenKey: state.extra as String,
        ),
      ),
      GoRoute(
        name: MainRouter.mainStockHome.routeName,
        path: MainRouter.mainStockHome.routeName,
        builder: (context, state) {
          final args = StockHomeArgsExtensions.fromQueryParams(
            state.uri.queryParameters,
          );
          return StockHomeScreen(args: args);
        },
      ),
      GoRoute(
        path: MainRouter.allFeature.routeName,
        builder: (context, state) => const AllFeatureScreen(),
      ),
      GoRoute(
        path: MainRouter.checkSmartOTPPage.routeName,
        builder: (context, state) => const CheckSmartOTPPage(),
      ),
      GoRoute(
        path: MainRouter.contract.routeName,
        builder: (context, state) =>
            ContractScreen(fundContract: state.extra as FundContract),
      ),
      GoRoute(
        path: MainRouter.checkCurrentStatusWealth.routeName,
        builder: (context, state) => const CheckCurrentStatusWealthPage(),
      ),
      GoRoute(
        path: MainRouter.homePersonalPhoto.routeName,
        builder: (context, state) => const HomePersonalPhotoPage(),
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [_MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return vpAuth;
  }
}

class _MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const _MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = _MultiLocalizationsDelegate();

  @override
  Future<VPNeoLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VPNeoLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
