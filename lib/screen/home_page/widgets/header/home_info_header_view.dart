import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:new_neo_invest/cubit/home/<USER>';
import 'package:new_neo_invest/cubit/home/<USER>/notification_cubit.dart';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/main_app_module.dart';
import 'package:new_neo_invest/main_navigator.dart';
import 'package:vp_common/vp_common.dart' hide Assets;
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_notification/vp_notification.dart';

class HeaderHome extends StatefulWidget {
  const HeaderHome({
    Key? key,
    this.onToggleDrawer,
    this.scrollController,
  }) : super(key: key);

  final ScrollController? scrollController;

  final VoidCallback? onToggleDrawer;

  @override
  State<HeaderHome> createState() => _HeaderHomeState();
}

class _HeaderHomeState extends State<HeaderHome> {
  ScrollController? get scrollController => widget.scrollController;

  bool isScrolling = false;

  void scrollListener() {
    final scrolling = scrollController!.position.pixels > 0;

    if (isScrolling != scrolling && mounted) {
      setState(() => isScrolling = scrolling);
      context.read<HomeCubit>().actionBackgroundHome(isScrolling);
    }
  }

  @override
  void initState() {
    super.initState();
    scrollController?.addListener(scrollListener);
  }

  @override
  void dispose() {
    scrollController?.removeListener(scrollListener);
    super.dispose();
  }

  Color get color {
    return isScrolling ? themeData.bgMain : Colors.transparent;
  }

  Widget avtName() {
    return Text(
        GetIt.instance<AuthCubit>()
                .userInfo
                ?.userinfo
                ?.fullname
                ?.getInitials() ??
            '',
        overflow: TextOverflow.ellipsis,
        style: vpTextStyle.subtitle16.copyColor(vpColor.textWhite));
  }

  Widget buildAppBarView() {
    return Container(
      decoration: BoxDecoration(
        color: color,
        boxShadow: isScrolling
            ? [
                const BoxShadow(
                  color: Color(0x1E2A3346),
                  blurRadius: 32,
                  offset: Offset(0, 32),
                  spreadRadius: 0,
                ),
              ]
            : [],
      ),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            onTap: () => widget.onToggleDrawer?.call(),
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                border: Border.all(
                    color: isScrolling ? themeData.black : vpColor.textWhite),
                color: vpColor.textBrand,
                shape: BoxShape.circle,
              ),
              alignment: Alignment.center,
              child: StreamBuilder<String>(
                  stream: context.read<HomeCubit>().getAvtController,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return CachedNetworkImage(
                        placeholder: (context, url) => avtName(),
                        imageBuilder: (context, imageProvider) => Container(
                          width: 58,
                          height: 58,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                                image: imageProvider, fit: BoxFit.cover),
                          ),
                        ),
                        imageUrl: snapshot.data!,
                        errorWidget: (_, __, ___) => avtName(),
                      );
                    } else {
                      return avtName();
                    }
                  }),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    GetIt.instance<AuthCubit>().userInfo?.userinfo?.fullname ??
                        '',
                    overflow: TextOverflow.ellipsis,
                    style: vpTextStyle.subtitle16.copyColor(
                        isScrolling ? themeData.black : vpColor.textWhite)),
                // BlocBuilder<NicknameCubit, NicknameState>(
                //   bloc: sl.get<NicknameCubit>(),
                //   builder: (context, state) {
                //     final nickname = Session().infoVerification?.nickname;
                //     return nickname.isNullOrEmpty
                //         ? const SizedBox.shrink()
                //         : Text('@$nickname',
                //             style: TextStyleUtils.text12Weight400.copyWith(
                //                 color: isScrolling
                //                     ? ColorUtils.black
                //                     : ColorHomeUtils.textWhite));
                //   },
                // ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () {
              context.push(MainRouter.codePilotGetToken.routeName);
            },
            child: SvgPicture.asset(
              Assets.icons.icSupportAi,
            ),
          ),
          const SizedBox(width: 16),
          InkWell(
            onTap: () => mainNavigator.openSearchPage(context),
            child: SvgPicture.asset(
              Assets.icons.icSearch,
              colorFilter: ColorFilter.mode(
                isScrolling ? vpColor.textBrand : vpColor.iconWhite,
                BlendMode.srcIn,
              ),
            ),
          ),
          const SizedBox(width: 16),
          BlocBuilder<NotificationCubit, NotificationState>(
              builder: (_, state) {
            final count = state.totalUnRead ?? 0;

            return InkWell(
              onTap: () => openNotificationPage(),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  SvgPicture.asset(
                    Assets.icons.icNotification,
                    colorFilter: ColorFilter.mode(
                      isScrolling ? vpColor.textBrand : vpColor.textWhite,
                      BlendMode.srcIn,
                    ),
                  ),

                  /// badge
                  if (count > 0)
                    Positioned(
                      right: -2,
                      top: -4,
                      child: DotView(radius: 7, color: themeData.red),
                    )
                ],
              ),
            );
          })
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: color,
          height: MediaQuery.of(context).padding.top,
        ),
        buildAppBarView(),
      ],
    );
  }

  Future<void> openNotificationPage() async {
    await context
        .push(VpNotificationRouter.notification.routeName)
        .then((value) {
      context.read<NotificationCubit>().getTotalUnReadNotification();
    });
  }

  /*-------- POP UP - SUPPORT -------- */
  Future<void> showPopupSupport() async {
    // showModalBottomSheet(
    //   barrierColor: ColorUtils.overlayBottomSheet,
    //   context: context,
    //   elevation: 0,
    //   backgroundColor: Colors.transparent,
    //   builder: (_) => DialogSelectSupport(
    //     supportContact: supportContact,
    //     consultant: true,
    //   ),
    // );
  }
}
