import 'package:intl/intl.dart';

class DateTimeUtils {
  static String hourMinuteSecond = 'HH:mm:ss';
  static String hourMinute = 'HH:mm';

  static String serverTimeFormat = 'yyyy-MM-ddTHH:mm:ssZ';
  static String ddMMYYYYHourMinute = 'dd/MM/yyyy HH:mm';

  static String ddMMYYYY = 'dd/MM/yyyy';
  static String yyyyMMDD = 'yyyy-MM-dd';
  static String yyyy = 'yyyy';

  static String formatTime({
    required String inputDate,
    required String inputFormat,
    required String outputFormat,
  }) {
    try {
      DateTime date = DateFormat(inputFormat).parse(inputDate);
      final formatter = DateFormat(outputFormat);
      return formatter.format(date);
    } catch (e) {
      return '';
    }
  }

  static DateTime? stringToDate({
    required String inputDate,
    required String inputFormat,
  }) {
    try {
      return DateFormat(inputFormat).parse(inputDate);
    } catch (e) {
      return null;
    }
  }

  static String dateToString(
      {required DateTime dateTime, required String format}) {
    final formatter = DateFormat(format);
    return formatter.format(dateTime);
  }
}
