import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

part 'auth_state.dart';

bool get isLoggedIn => GetIt.instance.get<AuthCubit>().isLogined;

enum DialogType { expire, maintenance }

class AuthCubit extends Cubit<AuthState> {
  AuthCubit() : super(const AuthState());
  bool isShowingExpireDialog = false;
  final expireController = StreamController<DialogType>();

  Stream<DialogType> get expireStream => expireController.stream;

  VerificationInfoModel? verificationInfoModel;

  CustomerInfoNewModel? _customerInfoIam;

  String? pinCode;

  String? deviceName;

  void setNeedSignature(bool value) {
    verificationInfoModel?.needSignature = value;
  }

  void loginSuccess(UserInfoModel userInfo) {
    emit(state.copyWith(status: AuthStatus.logined, userCurrent: userInfo));
  }

  bool hasAccessToken() {
    return SharedPref.getString(KeyShared.accessToken).isNotEmpty;
  }

  bool get isLogined => state.isLoggedIn;

  UserInfoModel? get userInfo {
    return state.userCurrent;
  }

  void logout() {
    verificationInfoModel = null;
    pinCode = null;
    _customerInfoIam = null;
    SharedPref().clearKey(KeyShared.accessToken);
    emit(state.copyWith(status: AuthStatus.nologin));
  }

  CustomerInfoNewModel? get customerInfoIam => _customerInfoIam;

  Future<CustomerInfoNewModel?> getCustomerInfoIam() async {
    if (_customerInfoIam != null) {
      return _customerInfoIam;
    }
    return _fetchCustomerInfo();
  }

  Future<void> setDeviceName() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceName = androidInfo.model;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceName = iosInfo.utsname.machine;
      }
    } catch (e) {
      dlog(e);
    }
  }

  Future<CustomerInfoNewModel?> _fetchCustomerInfo() async {
    try {
      final value = await getContext.push('/overlayUserIamInfo');
      if (value != null && value is CustomerInfoNewModel) {
        _customerInfoIam = value;
        return _customerInfoIam;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  void exprireSession(DialogType type, {String? message}) {
    if (isShowingExpireDialog) return;
    expireController.add(type);
    isShowingExpireDialog = true;
  }

  void removePinCode() {
    pinCode = null;
  }
}
