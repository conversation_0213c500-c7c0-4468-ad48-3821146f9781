import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/model/sign_in_model/account_list_model.dart';
import 'package:vp_core/vp_core.dart';

part 'sub_account_state.dart';

class SubAccountCubit extends Cubit<SubAccountState> {
  SubAccountCubit()
      : super(SubAccountState(subAccountSelected: subAccountTypeAll));

  void setSubAccounts(AccountListModel? accountList) {
    emit(state.copyWith(accountList: accountList));
  }

  SubAccountModel? getSubAccount(SubAccountType type) {
    return state.subAccountsAll
        .firstWhereOrNull((e) => e.toSubAccountType == type);
  }

  SubAccountModel get defaultSubAccount {
    final rawString = SharedPref.getString(
      KeyShared.defaultSubAccount,
      SubAccountType.normal.defaultSetting,
    );

    final subAccountType = rawString == SubAccountType.normal.defaultSetting
        ? SubAccountType.normal
        : SubAccountType.margin;

    return state.subAccountsAll.filterType(subAccountType);
  }

  Future<void> setDefaultSubAccount(SubAccountType subAccountType) {
    return SharedPref.setString(
      KeyShared.defaultSubAccount,
      subAccountType.defaultSetting,
    );
  }

  SubAccountType get subAccountType =>
      state.subAccountSelected.toSubAccountType;

  SubAccountType subAccountTypeFromId(String? subAccountId) =>
      (subAccountsStock.firstWhereOrNull(
                  (subAccounts) => subAccounts.id == subAccountId) ??
              defaultSubAccount)
          .toSubAccountType;

  SubAccountModel? get marginAccount => state.subAccountsAll
      .firstWhereOrNull((e) => e.toSubAccountType == SubAccountType.margin);

  SubAccountModel? get normalAccount => state.subAccountsAll
      .firstWhereOrNull((e) => e.toSubAccountType == SubAccountType.normal);

  SubAccountModel? get bondAccount => state.subAccountsAll
      .firstWhereOrNull((e) => e.toSubAccountType == SubAccountType.bond);

  List<SubAccountModel> get subAccountsStock => state.subAccountsAll
      .where(
        (e) =>
            e.toSubAccountType == SubAccountType.normal ||
            e.toSubAccountType == SubAccountType.margin,
      )
      .toList();

  List<SubAccountModel> get subAccountsAllNoCommission => state.subAccountsAll
      .where((e) => e.toSubAccountType != SubAccountType.commission)
      .toList();

  SubAccountModel? get commissionAccount => state.subAccountsAll
      .firstWhereOrNull((e) => e.toSubAccountType == SubAccountType.commission);

  SubAccountModel? get wealthAccount => state.subAccountsAll
      .firstWhereOrNull((e) => e.toSubAccountType == SubAccountType.wealth);

  List<SubAccountModel> get subAccountsAllNoCommissionHaveDerivativeAccount =>
      _getSubAccountsAllNoCommissionHaveDerivative();

  List<SubAccountModel> get getSubAccountsAllTransfer =>
      _getSubAccountsAllTransfer();

  List<SubAccountModel> _getSubAccountsAllTransfer() {
    final validTypes = {
      SubAccountProductTypeCdEnum.derivative,
      SubAccountProductTypeCdEnum.standard,
      SubAccountProductTypeCdEnum.margin,
      SubAccountProductTypeCdEnum.bond,
      SubAccountProductTypeCdEnum.wealth,
    };

    final accounts = state.derivativeSubAccount.isNotEmpty
        ? [...state.subAccountsAll, ...state.derivativeSubAccount]
        : subAccountsAllNoCommission;

    return accounts
        .where((e) => validTypes.contains(e.subAccountProductTypeCdEnum))
        .toList();
  }

  /// Phái sinh
  SubAccountModel? get derivativeAccount => state.derivativeSubAccount
      .firstWhereOrNull((e) => e.isDerivative == true);

  List<SubAccountModel>? get derivativeAccounts =>
      state.derivativeSubAccount.where((e) => e.isDerivative == true).toList();

  SubAccountModel? get derivativeActiveAccount =>
      state.derivativeSubAccount.findFirstOrNull(
        (e) => e.isDerivative == true && e.status != 'C' && e.dmaStatus != 'C',
      );

  bool get anyDerivativeAccount => derivativeActiveAccount != null;

  bool get isDerivativePending =>
      derivativeAccounts?.any((e) => e.status == 'P') ?? false;

  bool get isValidAccount =>
      const ['C', 'R'].contains(derivativeAccount?.dmaStatus);

  List<SubAccountModel> _getSubAccountsAllNoCommissionHaveDerivative() {
    if (state.derivativeSubAccount.isNotEmpty) {
      return [...subAccountsAllNoCommission, ...state.derivativeSubAccount];
    } else {
      return subAccountsAllNoCommission;
    }
  }

  List<SubAccountModel> get subAccountsAllHaveDerivative =>
      _getSubAccountsAllHaveDerivative();

  List<SubAccountModel> _getSubAccountsAllHaveDerivative() {
    if (state.derivativeSubAccount.isNotEmpty) {
      return [...state.subAccountsAll, ...state.derivativeSubAccount];
    } else {
      return state.subAccountsAll;
    }
  }
}
