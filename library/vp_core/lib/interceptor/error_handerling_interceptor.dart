import 'dart:io';

import 'package:vp_common/error/reponse_error_model.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

class ErrorHanderlingInterceptor extends InterceptorsWrapper {
  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    return super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == HttpStatus.unauthorized) {
      GetIt.instance<AuthCubit>().exprireSession(DialogType.expire);
      return handler.reject(err);
    }

    final data = err.response?.data;

    if (data is Map && data['code'] == AppConstants.systemMaintenance) {
      GetIt.instance<AuthCubit>().exprireSession(
        DialogType.maintenance,
        message: data['message'].toString(),
      );

      return handler.reject(err);
    }

    if (data is Map && data.containsKey('messageVi')) {
      try {
        final responseEr = ResponseErrorModel.fromJson(
          err.response!.data! as Map<String, dynamic>,
        );
        final tmpErr = err.copyWith(error: responseEr);
        super.onError(tmpErr, handler);
      } catch (_) {
        return super.onError(err, handler);
      }
    } else {
      return super.onError(err, handler);
    }
  }
}
