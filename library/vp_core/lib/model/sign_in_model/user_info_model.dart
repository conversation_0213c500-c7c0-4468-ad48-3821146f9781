import 'package:json_annotation/json_annotation.dart';

part 'user_info_model.g.dart';

@JsonSerializable()
class UserInfoModel {
  UserInfoModel({this.userinfo});

  factory UserInfoModel.fromJson(Map<String, dynamic> json) =>
      _$UserInfoModelFromJson(json);
  final UserModel? userinfo;

  Map<String, dynamic> toJson() => _$UserInfoModelToJson(this);
}

@JsonSerializable()
class UserModel {
  UserModel(
      {this.username,
      this.custodycd,
      this.tlid,
      this.fullname,
      this.role,
      this.language,
      this.needCaptCha,
      this.timeLockAccount,
      this.permissionInfo,
      this.listAuthType,
      this.brokerGroups,
      this.createdAt,
      this.updatedAt,
      this.custid,
      this.userid,
      this.isVerified,
      this.isreset,
      this.isresetpin,
      this.isresetpass,
      this.cimode,
      this.iscimanage,
      this.authtype,
      this.avtUrl});

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  String? username;
  String? custodycd;
  String? tlid;
  String? fullname;
  String? role;
  String? language;
  String? needCaptCha;
  String? timeLockAccount;
  PermissionInfo? permissionInfo;
  List<String>? listAuthType;
  List<String>? brokerGroups;
  int? createdAt;
  int? updatedAt;
  String? custid;
  String? userid;
  bool? isVerified;
  String? isreset;
  String? isresetpin;
  String? isresetpass;
  String? cimode;
  String? iscimanage;
  String? authtype;
  String? avtUrl;

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  void setAvtUrl(String path) {
    avtUrl = path;
  }
}

extension UserModelF on UserModel {
  void setAvtUrl(String path) {
    avtUrl = path;
  }
}

@JsonSerializable()
class PermissionInfo {
  PermissionInfo({this.accounts});

  factory PermissionInfo.fromJson(Map<String, dynamic> json) =>
      _$PermissionInfoFromJson(json);

  dynamic accounts;

  Map<String, dynamic> toJson() => _$PermissionInfoToJson(this);
}
