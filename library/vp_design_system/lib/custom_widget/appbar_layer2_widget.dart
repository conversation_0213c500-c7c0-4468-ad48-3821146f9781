import 'package:flutter/material.dart';
import 'package:vp_design_system/themes/utils.dart';
import 'package:vp_design_system/vp_design_system.dart';

class AppBarLayer2Widget extends StatelessWidget
    implements PreferredSizeWidget {
  const AppBarLayer2Widget(
      {super.key,
      this.elevation,
      this.backgroundColor,
      this.actions,
      this.leading,
      this.title,
      this.leadingWidth,
      this.subtitle2Color,
      this.subTitle1,
      this.subTitle2});
  final String? subTitle1;
  final String? subTitle2;
  final double? elevation;
  final Color? backgroundColor;
  final Color? subtitle2Color;
  final List<Widget>? actions;
  final Widget? leading;
  final double? leadingWidth;
  final Widget? title;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      titleSpacing: 0,
      shadowColor: Colors.transparent,
      backgroundColor: Colors.transparent,
      scrolledUnderElevation: 0,
      iconTheme: Theme.of(context).iconTheme,
      leading: leading,
      leadingWidth: leadingWidth,
      title: title ??
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                subTitle1 ?? '',
                style: context.textStyle.captionRegular?.copyWith(
                  color: Theme.of(context).gray500,
                ),
              ),
              Text(
                subTitle2 ?? '',
                style: context.textStyle.headine6?.copyWith(
                    color: subtitle2Color ?? Theme.of(context).primary,
                    fontWeight: FontWeight.w700,),
              ),
            ],
          ),
      actions: [
        ...?actions,
        const SizedBox(
          width: 16,
        ),
      ],
      elevation: elevation,
      centerTitle: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
