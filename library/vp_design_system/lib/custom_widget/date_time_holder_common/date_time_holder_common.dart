import 'package:flutter/material.dart';
import 'package:vp_common/extensions/date_extensions.dart';
import 'package:vp_design_system/themes/utils.dart';
import 'package:vp_design_system/vp_design_system.dart';

class VPDateTimeHolderCommon extends StatefulWidget {
  const VPDateTimeHolderCommon({
    required this.startDate,
    required this.endDate,
    this.minDate,
    this.maxDate,
    this.rangeDay,
    this.onDateTimeChanged,
    super.key,
  });

  final DateTime? minDate;

  final DateTime? maxDate;

  final DateTime startDate;

  final DateTime endDate;

  final int? rangeDay;

  final Function(({DateTime endDate, DateTime startDate}))? onDateTimeChanged;

  @override
  State<VPDateTimeHolderCommon> createState() => VPDateTimeHolderCommonState();
}

class VPDateTimeHolderCommonState extends State<VPDateTimeHolderCommon> {
  Future<void> openDatePicker() async {
    final result = await VPPopup.bottomSheet(
      VerticalDatePickerView(
        minDate: widget.minDate,
        maxDate: widget.maxDate,
        startDate: startDate,
        endDate: endDate,
        rangeDay: widget.rangeDay,
      ),
    ).showSheet(context);

    if (result is ({DateTime endDate, DateTime startDate})) {
      startDate = result.startDate;
      endDate = result.endDate;
      setState(() {});

      widget.onDateTimeChanged?.call((startDate: startDate, endDate: endDate));
    }
  }

  late DateTime startDate = widget.startDate;

  late DateTime endDate = widget.endDate;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: openDatePicker,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: context.colors.backgroundElevation0,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: context.colors.strokeNormal),
        ),
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: Row(
          children: [
            Icon(
              Icons.calendar_month,
              size: 24,
              color: context.colors.iconPrimary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '${startDate.formatToDdMmYyyy()} - ${endDate.formatToDdMmYyyy()}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: context.textStyle.body14
                    ?.copyWith(color: context.colors.textPrimary),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
