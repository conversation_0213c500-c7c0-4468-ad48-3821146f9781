import 'package:flutter/material.dart';
import 'package:vp_design_system/themes/utils.dart';

class VPBottomAppBar extends StatelessWidget {
  const VPBottomAppBar({this.child, this.color, super.key});

  final Widget? child;

  final Color? color;

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: color ?? context.colors.backgroundElevation1,
      padding: EdgeInsets.zero,
      child: child,
    );
  }
}
