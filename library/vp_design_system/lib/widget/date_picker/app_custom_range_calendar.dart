import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class AppCustomRangeCalendar extends StatefulWidget {
  final DateTime? startDay;
  final DateTime? endDay;
  final DateTime? minDay;
  final DateTime? maxDay;

  /// used when user press reset button
  final DateTime? resetStartDay;

  /// used when user press reset button
  final DateTime? resetEndDay;

  /// User can select time in this range
  /// Ex: days = 15 and user choose startDate is 01/02/2022,
  /// => User can select time in 15/01/2022 - 15/02/2022
  final int? dateRange;

  const AppCustomRangeCalendar({
    Key? key,
    this.startDay,
    this.endDay,
    this.minDay,
    this.maxDay,
    this.resetStartDay,
    this.resetEndDay,
    this.dateRange,
  }) : super(key: key);

  @override
  State<AppCustomRangeCalendar> createState() => _AppCustomRangeCalendarState();
}

class _AppCustomRangeCalendarState extends State<AppCustomRangeCalendar> {
  DateTime? _startDay;
  DateTime? _endDay;
  late DateTime _minDate;
  late DateTime _maxDate;
  late DateRangePickerController controller = DateRangePickerController();
  final ValueNotifier<bool> isDateChange = ValueNotifier(false);

  @override
  void initState() {
    _startDay = widget.startDay;
    _endDay = widget.endDay;
    controller.selectedRange = PickerDateRange(_startDay, _endDay);
    controller.displayDate = _endDay;
    _maxDate = widget.maxDay ?? DateTime.now();
    _minDate = widget.minDay ?? _maxDate.subtract(const Duration(days: 365));
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            Container(
              margin: const EdgeInsets.symmetric(
                  horizontal: 16, vertical: 16),
              child: Row(
                children: List.generate(7, (index) {
                  return Expanded(
                    flex: 1,
                    child: Text(
                      index != 6 ? 'T${index + 2}' : 'CN',
                      textAlign: TextAlign.center,
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.copyWith(color: themeData.gray500),
                    ),
                  );
                }),
              ),
            ),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16),
                child: SfDateRangePicker(
                  todayHighlightColor: themeData.primary,
                  onViewChanged: (arg) {},
                  controller: controller,
                  showNavigationArrow: false,
                  onSelectionChanged: _onSelectionChanged,
                  selectionShape: DateRangePickerSelectionShape.circle,
                  view: DateRangePickerView.month,
                  selectionColor: themeData.primary,
                  navigationDirection:
                      DateRangePickerNavigationDirection.vertical,
                  navigationMode: DateRangePickerNavigationMode.scroll,
                  enableMultiView: true,
                  enablePastDates: true,
                  allowViewNavigation: false,
                  selectionTextStyle: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(color: themeData.textEnable),
                  startRangeSelectionColor: themeData.primary,
                  endRangeSelectionColor: themeData.primary,
                  rangeTextStyle: Theme.of(context).textTheme.bodyLarge,
                  rangeSelectionColor: themeData.bgMain,
                  minDate: _minDate,
                  maxDate: _maxDate,
                  monthFormat: 'MMMM',
                  selectionMode: DateRangePickerSelectionMode.range,
                  // headerHeight: headerHeight,
                  headerStyle: DateRangePickerHeaderStyle(
                      backgroundColor: themeData.bgMain,
                      textAlign: TextAlign.start,
                      textStyle: Theme.of(context).textTheme.titleSmall),
                  monthCellStyle: DateRangePickerMonthCellStyle(
                      textStyle: Theme.of(context).textTheme.bodyLarge,
                      todayCellDecoration: BoxDecoration(
                          border:
                              Border.all(color: themeData.primary, width: 1),
                          shape: BoxShape.circle),
                      todayTextStyle: Theme.of(context)
                          .textTheme
                          .bodyLarge!
                          .copyWith(color: themeData.primary)),
                  monthViewSettings: DateRangePickerMonthViewSettings(
                      firstDayOfWeek: 1,
                      dayFormat: 'EE',
                      viewHeaderHeight: 0,
                      viewHeaderStyle: DateRangePickerViewHeaderStyle(
                        textStyle: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.copyWith(color: themeData.gray500),
                      )),
                  yearCellStyle: DateRangePickerYearCellStyle(
                    textStyle: Theme.of(context).textTheme.bodyLarge,
                    todayTextStyle: Theme.of(context)
                        .textTheme
                        .bodyLarge!
                        .copyWith(color: themeData.white),
                    todayCellDecoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: themeData.primary,
                    ),
                  ),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  border: Border(
                      top: BorderSide(width: 1, color: themeData.divider))),
              padding: const EdgeInsets.symmetric(
                  horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                        alignment: Alignment.centerLeft,
                        child: ValueListenableBuilder<bool>(
                          valueListenable: isDateChange,
                          builder: (cxt, value, _) {
                            return Text(
                              _getRangeTextDay(),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge!
                                  .copyWith(color: themeData.gray500),
                            );
                          },
                        )),
                  ),
                  TextButton(
                    child: Text(
                      'Đặt lại',
                      style: Theme.of(context)
                          .textTheme
                          .titleSmall
                          ?.copyWith(color: themeData.gray700),
                    ),
                    onPressed: () {
                      setState(() {
                        _resetSelectRange();
                      });
                    },
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    child: Text(
                      'Áp dụng',
                      style: Theme.of(context)
                          .textTheme
                          .titleSmall
                          ?.copyWith(color: themeData.primary),
                    ),
                    onPressed: () {
                      context.pop([
                        _getStartDate(),
                        _getEndDate(),
                      ]);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getRangeTextDay() {
    if (_getStartDate() != null) {
      const String dateFormat = 'dd/MM/yyyy';
      return '${AppTimeUtils.formatTime(_getStartDate().toString(), format: dateFormat)} - ${AppTimeUtils.formatTime(_getEndDate().toString(), format: dateFormat)}';
    }
    return '';
  }

  void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    if (args.value is PickerDateRange) {
      final range = args.value as PickerDateRange;

      if (range.startDate == null) return;

      setRangeTime(range.startDate, range.endDate);

      controller.selectedRange =
          PickerDateRange(range.startDate, range.endDate ?? range.startDate);
      isDateChange.notifyListeners();
    }
  }

  void setRangeTime(DateTime? startDate, DateTime? endDate) {
    if (startDate == null || widget.dateRange == null) return;

    if (startDate == endDate || endDate == null) {
      _minDate = startDate.add(Duration(days: -widget.dateRange!));
      _maxDate = startDate.add(Duration(days: widget.dateRange!));

      if (widget.minDay != null && _minDate.isBefore(widget.minDay!)) {
        _minDate = widget.minDay!;
      }

      if (widget.maxDay != null && _maxDate.isAfter(widget.maxDay!)) {
        _maxDate = widget.maxDay!;
      }

      if (_minDate.isAfter(_maxDate)) {
        final temp = _minDate;
        _minDate = _maxDate;
        _maxDate = temp;
      }

      setState(() {});
    }
  }

  void _resetSelectRange() {
    if (widget.resetStartDay != null) {
      final resetEndDay = widget.resetEndDay ?? DateTime.now();

      controller.displayDate = resetEndDay;

      controller.selectedRange =
          PickerDateRange(widget.resetStartDay, resetEndDay);
    } else {
      controller.selectedRange = null;
      controller.displayDate = DateTime.now();
    }
  }

  DateTime? _getStartDate() {
    return controller.selectedRange?.startDate;
  }

  DateTime? _getEndDate() {
    return controller.selectedRange?.endDate;
  }
}
