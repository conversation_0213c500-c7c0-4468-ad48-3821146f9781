import 'package:json_annotation/json_annotation.dart';
import 'package:vp_socket/data/socket_data.dart';

part 'socket_buy_up_sell_down_market_data.g.dart';

@JsonSerializable()
class VPBuyUpSellDownMarketData extends VPSocketInvestData {
  final String? indexCode;
  final num? timeStampS;
  final num? buyUpValueS;
  final num? sellDownValueS;
  final num? timeStampM;
  final num? buyUpValueM;
  final num? sellDownValueM;
  final num? timeStampL;
  final num? buyUpValueL;
  final num? sellDownValueL;

  const VPBuyUpSellDownMarketData({
    this.indexCode,
    this.timeStampS,
    this.buyUpValueS,
    this.sellDownValueS,
    this.timeStampM,
    this.buyUpValueM,
    this.sellDownValueM,
    this.timeStampL,
    this.buyUpValueL,
    this.sellDownValueL,
    required super.channel,
  }) : super(symbol: indexCode);

  factory VPBuyUpSellDownMarketData.fromJson(Map<String, dynamic> json) =>
      _$VPBuyUpSellDownMarketDataFromJson(json);
}
