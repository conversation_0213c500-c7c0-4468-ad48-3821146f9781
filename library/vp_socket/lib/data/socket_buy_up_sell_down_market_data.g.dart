// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'socket_buy_up_sell_down_market_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VPBuyUpSellDownMarketData _$VPBuyUpSellDownMarketDataFromJson(
        Map<String, dynamic> json) =>
    VPBuyUpSellDownMarketData(
      indexCode: json['indexCode'] as String?,
      timeStampS: json['timeStampS'] as num?,
      buyUpValueS: json['buyUpValueS'] as num?,
      sellDownValueS: json['sellDownValueS'] as num?,
      timeStampM: json['timeStampM'] as num?,
      buyUpValueM: json['buyUpValueM'] as num?,
      sellDownValueM: json['sellDownValueM'] as num?,
      timeStampL: json['timeStampL'] as num?,
      buyUpValueL: json['buyUpValueL'] as num?,
      sellDownValueL: json['sellDownValueL'] as num?,
      channel: json['channel'] as String,
    );

Map<String, dynamic> _$VPBuyUpSellDownMarketDataToJson(
        VPBuyUpSellDownMarketData instance) =>
    <String, dynamic>{
      'channel': instance.channel,
      'indexCode': instance.indexCode,
      'timeStampS': instance.timeStampS,
      'buyUpValueS': instance.buyUpValueS,
      'sellDownValueS': instance.sellDownValueS,
      'timeStampM': instance.timeStampM,
      'buyUpValueM': instance.buyUpValueM,
      'sellDownValueM': instance.sellDownValueM,
      'timeStampL': instance.timeStampL,
      'buyUpValueL': instance.buyUpValueL,
      'sellDownValueL': instance.sellDownValueL,
    };
