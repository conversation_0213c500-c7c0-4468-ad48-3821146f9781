import 'package:json_annotation/json_annotation.dart';
import 'package:vp_socket/data/socket_data.dart';

part 'socket_intraday_supply_demand_chart_data.g.dart';

@JsonSerializable()
class VPIntradaySupplyDemandChartData extends VPSocketInvestData {
  final String? indexCode;
  final num? timeStamp;
  final num? sumBuyUpValue;
  final num? sumSellDownValue;
  final num? sumMarketValue;
  final num? rateSumBuyUpValue;
  final num? rateSumSellDownValue;
  final num? buyUpOnSellDown;

  VPIntradaySupplyDemandChartData({
    this.indexCode,
    this.timeStamp,
    this.sumBuyUpValue,
    this.sumSellDownValue,
    this.sumMarketValue,
    this.rateSumBuyUpValue,
    this.rateSumSellDownValue,
    this.buyUpOnSellDown,
    required super.channel,
  }) : super(symbol: indexCode);

  factory VPIntradaySupplyDemandChartData.fromJson(Map<String, dynamic> json) =>
      _$VPIntradaySupplyDemandChartDataFromJson(json);
}
