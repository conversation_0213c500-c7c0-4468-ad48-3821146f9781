// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'socket_intraday_supply_demand_chart_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VPIntradaySupplyDemandChartData _$VPIntradaySupplyDemandChartDataFromJson(
        Map<String, dynamic> json) =>
    VPIntradaySupplyDemandChartData(
      indexCode: json['indexCode'] as String?,
      timeStamp: json['timeStamp'] as num?,
      sumBuyUpValue: json['sumBuyUpValue'] as num?,
      sumSellDownValue: json['sumSellDownValue'] as num?,
      sumMarketValue: json['sumMarketValue'] as num?,
      rateSumBuyUpValue: json['rateSumBuyUpValue'] as num?,
      rateSumSellDownValue: json['rateSumSellDownValue'] as num?,
      buyUpOnSellDown: json['buyUpOnSellDown'] as num?,
      channel: json['channel'] as String,
    );

Map<String, dynamic> _$VPIntradaySupplyDemandChartDataToJson(
        VPIntradaySupplyDemandChartData instance) =>
    <String, dynamic>{
      'channel': instance.channel,
      'indexCode': instance.indexCode,
      'timeStamp': instance.timeStamp,
      'sumBuyUpValue': instance.sumBuyUpValue,
      'sumSellDownValue': instance.sumSellDownValue,
      'sumMarketValue': instance.sumMarketValue,
      'rateSumBuyUpValue': instance.rateSumBuyUpValue,
      'rateSumSellDownValue': instance.rateSumSellDownValue,
      'buyUpOnSellDown': instance.buyUpOnSellDown,
    };
