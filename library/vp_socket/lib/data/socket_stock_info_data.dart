import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/extensions/num_helper.dart';

part 'socket_stock_info_data.g.dart';

@JsonSerializable()
class VPStockInfoData extends VPSocketInvestData with EquatableMixin {
  // string | int
  final dynamic stockType;

  // null | double
  @DynamicToNumConverter()
  @JsonKey(name: 'ceilingPrice')
  final num? ceiling;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'floorPrice')
  final num? floor;

  // null | string | double
  @DynamicToNumConverter()
  @Json<PERSON>ey(name: 'refPrice')
  final num? reference;

  // null | float
  @DynamicToNumConverter()
  final num? bidPrice3;

  // null | float
  @DynamicToNumConverter()
  final num? bidVol3;

  // null | float
  @DynamicToNumConverter()
  final num? bidPrice2;

  // null | float
  @DynamicToNumConverter()
  final num? bidVol2;

  // null | string | float
  final dynamic bidPrice1;

  // null | float
  @DynamicToNumConverter()
  final num? bidVol1;

  // null | float
  @DynamicToNumConverter()
  final num? closePrice;

  // null | float
  @DynamicToNumConverter()
  final num? closeVol;

  // null | float
  @DynamicToNumConverter()
  final num? priceChange;

  // float
  @DynamicToNumConverter()
  @JsonKey(name: 'percentPriceChange')
  final num? percentChange;

  // null | string | float
  @DynamicToNumConverter()
  @JsonKey(name: 'askPrice1')
  final dynamic offerPrice1;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'askVol1')
  final num? offerVol1;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'askPrice2')
  final num? offerPrice2;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'askVol2')
  final num? offerVol2;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'askPrice3')
  final num? offerPrice3;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'askVol3')
  final num? offerVol3;

  // null | long
  @DynamicToNumConverter()
  final num? totalTradingVolume;

  // null | double
  @DynamicToNumConverter()
  final num? totalTradingValue;

  // null | double
  @DynamicToNumConverter()
  final num? averagePrice;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'openPrice')
  final num? open;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'highPrice')
  final num? high;

  // null | float
  @DynamicToNumConverter()
  @JsonKey(name: 'lowPrice')
  final num? low;

  // null | double
  @DynamicToNumConverter()
  final num? foreignBuyVolume;

  // null | double
  @DynamicToNumConverter()
  final num? foreignSellVolume;

  // null | long
  @DynamicToNumConverter()
  final num? foreignRemain;

  // null | long
  @DynamicToNumConverter()
  final num? foreignRoom;

  // null | double
  @DynamicToNumConverter()
  @JsonKey(name: 'totalAskQTTY')
  final num? totalOfferQTTY;

  // null | double
  @DynamicToNumConverter()
  final num? totalBidQTTY;

  // null | double
  @DynamicToNumConverter()
  final num? foreignBuyValue;

  // null | double
  @DynamicToNumConverter()
  final num? foreignSellValue;

  // null | double
  @DynamicToNumConverter()
  final num? foreignNetValue;

  // null | double
  @DynamicToNumConverter()
  final num? breakEvenPoint;

  VPStockInfoData({
    required super.symbol,
    required super.channel,
    required this.stockType,
    this.ceiling,
    this.floor,
    this.reference,
    this.bidPrice3,
    this.bidVol3,
    this.bidPrice2,
    this.bidVol2,
    this.bidPrice1,
    this.bidVol1,
    this.closePrice,
    this.closeVol,
    this.priceChange,
    this.percentChange,
    this.offerPrice1,
    this.offerVol1,
    this.offerPrice2,
    this.offerVol2,
    this.offerPrice3,
    this.offerVol3,
    this.totalTradingVolume,
    this.totalTradingValue,
    this.averagePrice,
    this.open,
    this.high,
    this.low,
    this.foreignBuyVolume,
    this.foreignSellVolume,
    this.foreignRemain,
    this.foreignRoom,
    this.totalOfferQTTY,
    this.totalBidQTTY,
    this.foreignBuyValue,
    this.foreignSellValue,
    this.foreignNetValue,
    this.breakEvenPoint,
  });

  factory VPStockInfoData.fromJson(Map<String, dynamic> json) =>
      _$VPStockInfoDataFromJson(json);

  Map<String, dynamic> toJson() => _$VPStockInfoDataToJson(this);

  @override
  List<Object?> get props => [
        symbol,
        stockType,
        ceiling,
        floor,
        reference,
        bidPrice3,
        bidVol3,
        bidPrice2,
        bidVol2,
        bidPrice1,
        bidVol1,
        closePrice,
        closeVol,
        priceChange,
        percentChange,
        offerPrice1,
        offerVol1,
        offerPrice2,
        offerVol2,
        offerPrice3,
        offerVol3,
        totalTradingVolume,
        totalTradingValue,
        averagePrice,
        open,
        high,
        low,
        foreignBuyVolume,
        foreignSellVolume,
        foreignRemain,
        foreignRoom,
        totalOfferQTTY,
        totalBidQTTY,
        foreignBuyValue,
        foreignSellValue,
        foreignNetValue,
        breakEvenPoint,
      ];
}
