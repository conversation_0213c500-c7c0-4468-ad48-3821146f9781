// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'socket_stock_info_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VPStockInfoData _$VPStockInfoDataFromJson(Map<String, dynamic> json) =>
    VPStockInfoData(
      symbol: json['symbol'] as String?,
      channel: json['channel'] as String,
      stockType: json['stockType'],
      ceiling: const DynamicToNumConverter().from<PERSON>son(json['ceilingPrice']),
      floor: const DynamicToNumConverter().fromJson(json['floorPrice']),
      reference: const DynamicToNumConverter().from<PERSON>son(json['refPrice']),
      bidPrice3: const DynamicToNumConverter().fromJson(json['bidPrice3']),
      bidVol3: const DynamicToNumConverter().fromJson(json['bidVol3']),
      bidPrice2: const DynamicToNumConverter().fromJson(json['bidPrice2']),
      bidVol2: const DynamicToNumConverter().fromJson(json['bidVol2']),
      bidPrice1: json['bidPrice1'],
      bidVol1: const DynamicToNumConverter().fromJson(json['bidVol1']),
      closePrice: const DynamicToNumConverter().fromJson(json['closePrice']),
      closeVol: const DynamicToNumConverter().fromJson(json['closeVol']),
      priceChange: const DynamicToNumConverter().fromJson(json['priceChange']),
      percentChange:
          const DynamicToNumConverter().fromJson(json['percentPriceChange']),
      offerPrice1: json['askPrice1'],
      offerVol1: const DynamicToNumConverter().fromJson(json['askVol1']),
      offerPrice2: const DynamicToNumConverter().fromJson(json['askPrice2']),
      offerVol2: const DynamicToNumConverter().fromJson(json['askVol2']),
      offerPrice3: const DynamicToNumConverter().fromJson(json['askPrice3']),
      offerVol3: const DynamicToNumConverter().fromJson(json['askVol3']),
      totalTradingVolume:
          const DynamicToNumConverter().fromJson(json['totalTradingVolume']),
      totalTradingValue:
          const DynamicToNumConverter().fromJson(json['totalTradingValue']),
      averagePrice:
          const DynamicToNumConverter().fromJson(json['averagePrice']),
      open: const DynamicToNumConverter().fromJson(json['openPrice']),
      high: const DynamicToNumConverter().fromJson(json['highPrice']),
      low: const DynamicToNumConverter().fromJson(json['lowPrice']),
      foreignBuyVolume:
          const DynamicToNumConverter().fromJson(json['foreignBuyVolume']),
      foreignSellVolume:
          const DynamicToNumConverter().fromJson(json['foreignSellVolume']),
      foreignRemain:
          const DynamicToNumConverter().fromJson(json['foreignRemain']),
      foreignRoom: const DynamicToNumConverter().fromJson(json['foreignRoom']),
      totalOfferQTTY:
          const DynamicToNumConverter().fromJson(json['totalAskQTTY']),
      totalBidQTTY:
          const DynamicToNumConverter().fromJson(json['totalBidQTTY']),
      foreignBuyValue:
          const DynamicToNumConverter().fromJson(json['foreignBuyValue']),
      foreignSellValue:
          const DynamicToNumConverter().fromJson(json['foreignSellValue']),
      foreignNetValue:
          const DynamicToNumConverter().fromJson(json['foreignNetValue']),
      breakEvenPoint:
          const DynamicToNumConverter().fromJson(json['breakEvenPoint']),
    );

Map<String, dynamic> _$VPStockInfoDataToJson(VPStockInfoData instance) =>
    <String, dynamic>{
      'channel': instance.channel,
      'symbol': instance.symbol,
      'stockType': instance.stockType,
      'ceilingPrice': const DynamicToNumConverter().toJson(instance.ceiling),
      'floorPrice': const DynamicToNumConverter().toJson(instance.floor),
      'refPrice': const DynamicToNumConverter().toJson(instance.reference),
      'bidPrice3': const DynamicToNumConverter().toJson(instance.bidPrice3),
      'bidVol3': const DynamicToNumConverter().toJson(instance.bidVol3),
      'bidPrice2': const DynamicToNumConverter().toJson(instance.bidPrice2),
      'bidVol2': const DynamicToNumConverter().toJson(instance.bidVol2),
      'bidPrice1': instance.bidPrice1,
      'bidVol1': const DynamicToNumConverter().toJson(instance.bidVol1),
      'closePrice': const DynamicToNumConverter().toJson(instance.closePrice),
      'closeVol': const DynamicToNumConverter().toJson(instance.closeVol),
      'priceChange': const DynamicToNumConverter().toJson(instance.priceChange),
      'percentPriceChange':
          const DynamicToNumConverter().toJson(instance.percentChange),
      'askPrice1': instance.offerPrice1,
      'askVol1': const DynamicToNumConverter().toJson(instance.offerVol1),
      'askPrice2': const DynamicToNumConverter().toJson(instance.offerPrice2),
      'askVol2': const DynamicToNumConverter().toJson(instance.offerVol2),
      'askPrice3': const DynamicToNumConverter().toJson(instance.offerPrice3),
      'askVol3': const DynamicToNumConverter().toJson(instance.offerVol3),
      'totalTradingVolume':
          const DynamicToNumConverter().toJson(instance.totalTradingVolume),
      'totalTradingValue':
          const DynamicToNumConverter().toJson(instance.totalTradingValue),
      'averagePrice':
          const DynamicToNumConverter().toJson(instance.averagePrice),
      'openPrice': const DynamicToNumConverter().toJson(instance.open),
      'highPrice': const DynamicToNumConverter().toJson(instance.high),
      'lowPrice': const DynamicToNumConverter().toJson(instance.low),
      'foreignBuyVolume':
          const DynamicToNumConverter().toJson(instance.foreignBuyVolume),
      'foreignSellVolume':
          const DynamicToNumConverter().toJson(instance.foreignSellVolume),
      'foreignRemain':
          const DynamicToNumConverter().toJson(instance.foreignRemain),
      'foreignRoom': const DynamicToNumConverter().toJson(instance.foreignRoom),
      'totalAskQTTY':
          const DynamicToNumConverter().toJson(instance.totalOfferQTTY),
      'totalBidQTTY':
          const DynamicToNumConverter().toJson(instance.totalBidQTTY),
      'foreignBuyValue':
          const DynamicToNumConverter().toJson(instance.foreignBuyValue),
      'foreignSellValue':
          const DynamicToNumConverter().toJson(instance.foreignSellValue),
      'foreignNetValue':
          const DynamicToNumConverter().toJson(instance.foreignNetValue),
      'breakEvenPoint':
          const DynamicToNumConverter().toJson(instance.breakEvenPoint),
    };
