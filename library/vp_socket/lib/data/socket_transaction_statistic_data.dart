import 'package:json_annotation/json_annotation.dart';
import 'package:vp_socket/data/socket_data.dart';

part 'socket_transaction_statistic_data.g.dart';

@JsonSerializable()
class VPTransactionStatisticData extends VPSocketData {
  String? indexCode;

  String? timeFrame;

  Sum? sum;
  SData? s;
  MData? m;
  LData? l;

  VPTransactionStatisticData({
    this.indexCode,
    this.timeFrame,
    this.sum,
    this.s,
    this.m,
    this.l,
    required super.channel,
  }) : super(symbol: indexCode);

  factory VPTransactionStatisticData.fromJson(Map<String, dynamic> json) =>
      _$VPTransactionStatisticDataFromJson(json);
}

@JsonSerializable()
class Sum {
  final num? sumBuyUpValue;
  final num? sumSellDownValue;
  final num? sumMarketValue;
  final num? netSumValue;

  Sum(
      {this.sumBuyUpValue,
      this.sumSellDownValue,
      this.sumMarketValue,
      this.netSumValue});

  factory Sum.fromJson(Map<String, dynamic> json) => _$SumFromJson(json);
}

@JsonSerializable()
class SData {
  num? totalBuyUpValueS;
  num? rateBuyUpValueS;
  num? totalSellDownValueS;
  num? rateSellDownValueS;
  num? netValueS;

  SData(
      {this.totalBuyUpValueS,
      this.rateBuyUpValueS,
      this.totalSellDownValueS,
      this.rateSellDownValueS,
      this.netValueS});

  factory SData.fromJson(Map<String, dynamic> json) => _$SDataFromJson(json);
}

@JsonSerializable()
class MData {
  num? totalBuyUpValueM;
  num? rateBuyUpValueM;
  num? totalSellDownValueM;
  num? rateSellDownValueM;
  num? netValueM;

  MData(
      {this.totalBuyUpValueM,
      this.rateBuyUpValueM,
      this.totalSellDownValueM,
      this.rateSellDownValueM,
      this.netValueM});

  factory MData.fromJson(Map<String, dynamic> json) => _$MDataFromJson(json);
}

@JsonSerializable()
class LData {
  num? totalBuyUpValueL;
  num? rateBuyUpValueL;
  num? totalSellDownValueL;
  num? rateSellDownValueL;
  num? netValueL;

  LData(
      {this.totalBuyUpValueL,
      this.rateBuyUpValueL,
      this.totalSellDownValueL,
      this.rateSellDownValueL,
      this.netValueL});

  factory LData.fromJson(Map<String, dynamic> json) => _$LDataFromJson(json);
}
