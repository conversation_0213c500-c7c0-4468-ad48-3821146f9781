// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'socket_transaction_statistic_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VPTransactionStatisticData _$VPTransactionStatisticDataFromJson(
        Map<String, dynamic> json) =>
    VPTransactionStatisticData(
      indexCode: json['indexCode'] as String?,
      timeFrame: json['timeFrame'] as String?,
      sum: json['sum'] == null
          ? null
          : Sum.fromJson(json['sum'] as Map<String, dynamic>),
      s: json['s'] == null
          ? null
          : SData.fromJson(json['s'] as Map<String, dynamic>),
      m: json['m'] == null
          ? null
          : MData.fromJson(json['m'] as Map<String, dynamic>),
      l: json['l'] == null
          ? null
          : LData.fromJson(json['l'] as Map<String, dynamic>),
      channel: json['channel'] as String,
    );

Map<String, dynamic> _$VPTransactionStatisticDataToJson(
        VPTransactionStatisticData instance) =>
    <String, dynamic>{
      'channel': instance.channel,
      'indexCode': instance.indexCode,
      'timeFrame': instance.timeFrame,
      'sum': instance.sum,
      's': instance.s,
      'm': instance.m,
      'l': instance.l,
    };

Sum _$SumFromJson(Map<String, dynamic> json) => Sum(
      sumBuyUpValue: json['sumBuyUpValue'] as num?,
      sumSellDownValue: json['sumSellDownValue'] as num?,
      sumMarketValue: json['sumMarketValue'] as num?,
      netSumValue: json['netSumValue'] as num?,
    );

Map<String, dynamic> _$SumToJson(Sum instance) => <String, dynamic>{
      'sumBuyUpValue': instance.sumBuyUpValue,
      'sumSellDownValue': instance.sumSellDownValue,
      'sumMarketValue': instance.sumMarketValue,
      'netSumValue': instance.netSumValue,
    };

SData _$SDataFromJson(Map<String, dynamic> json) => SData(
      totalBuyUpValueS: json['totalBuyUpValueS'] as num?,
      rateBuyUpValueS: json['rateBuyUpValueS'] as num?,
      totalSellDownValueS: json['totalSellDownValueS'] as num?,
      rateSellDownValueS: json['rateSellDownValueS'] as num?,
      netValueS: json['netValueS'] as num?,
    );

Map<String, dynamic> _$SDataToJson(SData instance) => <String, dynamic>{
      'totalBuyUpValueS': instance.totalBuyUpValueS,
      'rateBuyUpValueS': instance.rateBuyUpValueS,
      'totalSellDownValueS': instance.totalSellDownValueS,
      'rateSellDownValueS': instance.rateSellDownValueS,
      'netValueS': instance.netValueS,
    };

MData _$MDataFromJson(Map<String, dynamic> json) => MData(
      totalBuyUpValueM: json['totalBuyUpValueM'] as num?,
      rateBuyUpValueM: json['rateBuyUpValueM'] as num?,
      totalSellDownValueM: json['totalSellDownValueM'] as num?,
      rateSellDownValueM: json['rateSellDownValueM'] as num?,
      netValueM: json['netValueM'] as num?,
    );

Map<String, dynamic> _$MDataToJson(MData instance) => <String, dynamic>{
      'totalBuyUpValueM': instance.totalBuyUpValueM,
      'rateBuyUpValueM': instance.rateBuyUpValueM,
      'totalSellDownValueM': instance.totalSellDownValueM,
      'rateSellDownValueM': instance.rateSellDownValueM,
      'netValueM': instance.netValueM,
    };

LData _$LDataFromJson(Map<String, dynamic> json) => LData(
      totalBuyUpValueL: json['totalBuyUpValueL'] as num?,
      rateBuyUpValueL: json['rateBuyUpValueL'] as num?,
      totalSellDownValueL: json['totalSellDownValueL'] as num?,
      rateSellDownValueL: json['rateSellDownValueL'] as num?,
      netValueL: json['netValueL'] as num?,
    );

Map<String, dynamic> _$LDataToJson(LData instance) => <String, dynamic>{
      'totalBuyUpValueL': instance.totalBuyUpValueL,
      'rateBuyUpValueL': instance.rateBuyUpValueL,
      'totalSellDownValueL': instance.totalSellDownValueL,
      'rateSellDownValueL': instance.rateSellDownValueL,
      'netValueL': instance.netValueL,
    };
