import 'package:collection/collection.dart';
import 'package:vp_socket/data/enum/socket_channel.dart';
import 'package:vp_socket/data/socket_buy_up_sell_down_market_data.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/data/socket_fu_top_n_data.dart';
import 'package:vp_socket/data/socket_intraday_supply_demand_chart_data.dart';
import 'package:vp_socket/data/socket_market_data.dart';
import 'package:vp_socket/data/socket_market_event_data.dart';
import 'package:vp_socket/data/socket_market_info_data.dart';
import 'package:vp_socket/data/socket_market_session_data.dart';
import 'package:vp_socket/data/socket_market_volume_data.dart';
import 'package:vp_socket/data/socket_stock_info_data.dart';
import 'package:vp_socket/data/socket_transaction_statistic_data.dart';

VPSocketData? defaultInvestmentSocketTransformData(dynamic data) =>
    VPInvestmentSocketFactory.get(data);

class VPInvestmentSocketFactory {
  static VPSocketData? get(dynamic data) {
    if (data is! Map<String, dynamic>) return null;

    final channelValue = data['channel'];

    final channel =
        VPSocketChannel.values.firstWhereOrNull((e) => e.name == channelValue);

    switch (channel) {
      case VPSocketChannel.stockInfo:
        return VPStockInfoData.fromJson(data);
      case VPSocketChannel.oddlotStockInfo:
        return VPStockInfoData.fromJson(data);
      case VPSocketChannel.marketData:
        return VPMarketData.fromJson(data);
      case VPSocketChannel.marketInfo:
        return VPMarketInfoData.fromJson(data);
      case VPSocketChannel.marketVolume:
        return VPMarketVolumeData.fromJson(data);
      case VPSocketChannel.fuTopNPrice:
        return VPFuTopNData.fromJson(data);
      case VPSocketChannel.marketSession:
        return VPMarketSessionData.fromJson(data);
      case VPSocketChannel.marketEvent:
        return VPMarketEventData.fromJson(data);
      case VPSocketChannel.intradaySupplyDemandChart:
        return VPIntradaySupplyDemandChartData.fromJson(data);
      case VPSocketChannel.transactionStatistic:
        return VPTransactionStatisticData.fromJson(data);
      case VPSocketChannel.buyUpSellDownMarket:
        return VPBuyUpSellDownMarketData.fromJson(data);
      default:
        return null;
    }
  }
}
