import 'dart:async';
import 'dart:isolate';

import 'package:alice/alice.dart';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:vp_socket/adapter/base_registry.dart';
import 'package:vp_socket/base/lifecycle_connectivity_bridge.dart';
import 'package:vp_socket/base/socket_controller.dart';
import 'package:vp_socket/data/enum/connection_state.dart';
import 'package:vp_socket/data/enum/socket_channel.dart';
import 'package:vp_socket/data/method_invocation.dart';
import 'package:vp_socket/data/throttle_policy.dart';
import 'package:vp_socket/data/throttle_rule.dart';
import 'package:vp_socket/extensions/socket_controller_extensions.dart';
import 'package:vp_socket/factory/socket_investment_factory.dart';
import 'package:vp_socket/interceptor/logging_interceptor.dart';
import 'package:vp_socket/socket_impl/invest/invest_codec.dart';
import 'package:vp_socket/socket_impl/invest/invest_sub.dart';
import 'package:vp_socket/socket_impl/invest/invest_topic_key.dart';
import 'package:vp_socket/base/base.dart';
import 'package:vp_socket/socket_impl/invest/socket_investment_impl.dart';

typedef PostDecodePolicyResolver = ThrottlePolicy? Function(dynamic channel);

final registry = ChannelRegistry(
  {
    VPSocketChannel.stockInfo.name: const StockInfoAdapter(),
    VPSocketChannel.oddlotStockInfo.name: const StockInfoAdapter(),
    VPSocketChannel.marketData.name: const StockInfoAdapter(),
    VPSocketChannel.marketInfo.name: const StockInfoAdapter(),
    VPSocketChannel.marketVolume.name: const StockInfoAdapter(),
    VPSocketChannel.fuTopNPrice.name: const StockInfoAdapter(),
    VPSocketChannel.intradaySupplyDemandChart.name: const StockInfoAdapter(),
    VPSocketChannel.transactionStatistic.name: const StockInfoAdapter(),
    VPSocketChannel.buyUpSellDownMarket.name: const StockInfoAdapter(),
    VPSocketChannel.marketEvent.name: MarketEventAdapter(),
  },
);

class IsolateInvestSocket extends BaseSocket
    with SocketLifecycleConnectivityMixin {
  bool _disposed = false;

  Isolate? _isolate;
  SendPort? _sendPort;
  ReceivePort? _receivePort;
  StreamSubscription? _subscription;
  final _stateController = StreamController<ConnectionStateX>.broadcast();

  Completer<void>? _ready;

  SocketLifecycleConnectivityObserver? _bridge;

  late final _controller = VPSocketController<VPInvestSub, VPInvestTopicKey>(
    VPInvestCodec(registry),
    onUnsubscribe: (sub) => unsubscribe(sub),
  );

  static void isolateEntryPoint(Map<String, dynamic> data) {
    final token = data['token'] as RootIsolateToken;
    final mainSendPort = data['sendPort'] as SendPort;

    BackgroundIsolateBinaryMessenger.ensureInitialized(token);

    final isolateReceivePort = ReceivePort();
    mainSendPort.send(isolateReceivePort.sendPort);

    var appInBackground = false;
    var networkAvailable = true;
    final rules = <ThrottleRule>[];

    StreamSubscription? subscription;

    /// channel: int or string
    ThrottlePolicy? postPolicyResolver(dynamic channel) {
      if (channel == null) {
        return null;
      }

      final keySym = rules.firstWhereOrNull((r) => r.channel == channel);

      if (keySym == null) return null;

      if (keySym.intervalMs == 0) {
        return ThrottlePolicy.none;
      }

      return ThrottlePolicy.of(Duration(milliseconds: keySym.intervalMs));
    }

    final socket = VPSocketInvestConnect(
      registry: registry,
      onData: (d) => mainSendPort.send(d),
    )
      ..setThrottlePolicyResolver(postPolicyResolver)
      ..addInterceptor(LoggingInterceptor())
      ..canRetryPredicate = () => !appInBackground && networkAvailable;

    subscription = socket.stateStream.listen((data) {
      mainSendPort.send('State|${data.name}');
    });

    Future<void> ensurePolicyConnect() async {
      if (!appInBackground && networkAvailable) {
        socket.connect();
      }
    }

    Future<void> closeNoRetry() async {
      socket.close();
    }

    isolateReceivePort
        .map((message) => MethodInvocation.fromJson(message))
        .listen((inv) {
      switch (inv.method) {
        case 'connect':
          ensurePolicyConnect();
          break;
        case 'reconnect':
          socket.reconnect();
          break;
        case 'subscribe':
          socket.subscribe(VPInvestSub.fromJson(inv.arguments));
          break;
        case 'unsubscribe':
          socket.unsubscribe(VPInvestSub.fromJson(inv.arguments));
          break;
        case 'close':
          socket.close();
          break;
        case 'dispose':
          subscription?.cancel();
          socket.dispose();
          break;
        case 'appBackground':
          appInBackground = true;
          closeNoRetry();
          break;
        case 'appForeground':
          appInBackground = false;
          ensurePolicyConnect();
          break;
        case 'networkDown':
          networkAvailable = false;
          closeNoRetry();
          break;
        case 'networkUp':
          networkAvailable = true;
          ensurePolicyConnect();
          break;
        case 'setThrottle':
          final List list = (inv.arguments['rules'] as List? ?? const []);

          rules
            ..clear()
            ..addAll(list.map(
                (e) => ThrottleRule.fromJson(Map<String, dynamic>.from(e))));

          break;
        default:
          break;
      }
    });
  }

  Future<void> init() async {
    if (_isolate != null || _disposed) return;

    if (_ready != null && !_ready!.isCompleted) return;

    try {
      _ready ??= Completer<void>();
      _receivePort = ReceivePort();

      _isolate = await Isolate.spawn(isolateEntryPoint, {
        'sendPort': _receivePort!.sendPort,
        'token': RootIsolateToken.instance!,
      });

      _subscription = _receivePort!.listen(
        (message) {
          if (_disposed) return;

          if (message is SendPort) {
            _sendPort = message;

            if (_ready != null && !_ready!.isCompleted) _ready!.complete();

            return;
          }

          if (message is String) {
            if (message.startsWith('State|')) {
              final state = message.split('|').lastOrNull;

              if (state != null) {
                final connectionState = ConnectionStateX.values.byName(state);

                GetIt.instance.get<Alice>().addLog(
                      AliceLog(
                        message: 'Socket status $connectionState',
                        level: DiagnosticLevel.debug,
                      ),
                    );
              }
            }
          }

          if (message is Map) {
            final type = message['type'];

            if (type == 'dataBatch') {
              final list = (message['data']?['list'] as List?) ?? const [];

              _controller.addBatch(list);
            } else {
              _controller.add(defaultInvestmentSocketTransformData(message));
            }
          }
        },
        onError: (e, st) {
          _onTeardown(e, st);
        },
        onDone: () {
          GetIt.instance.get<Alice>().addLog(
                AliceLog(
                  message: 'Isolate done',
                  level: DiagnosticLevel.info,
                ),
              );

          if (_disposed) return;

          _onTeardown('Isolate port closed', StateError('Isolate port closed'));
        },
        cancelOnError: true,
      );
    } catch (e, st) {
      debugPrintStack(stackTrace: st);

      _onTeardown(e, st);
    }
  }

  void _onTeardown(e, st) {
    GetIt.instance.get<Alice>().addLog(
          AliceLog(
            message: 'Isolate error',
            stackTrace: st,
            error: e,
            level: DiagnosticLevel.error,
          ),
        );
    _subscription?.cancel();
    _subscription = null;
    _receivePort?.close();
    _receivePort = null;
    _sendPort = null;
    _isolate?.kill(priority: Isolate.immediate);
    _isolate = null;
    _ready?.completeError(e, st);
    _ready = null;
    _bridge?.stop();
  }

  Future<void> _sendNow(String method, [Object? args]) async {
    if (_disposed) return;

    try {
      await init();

      await _ready?.future;

      _sendPort
          ?.send(MethodInvocation(method: method, arguments: args).toJson());
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  @override
  Future<void> connect() async {
    await _sendNow('connect');
    _bridge ??= SocketLifecycleConnectivityObserver(this)..start();
  }

  Future<void> subscribe(VPInvestSub sub) =>
      _sendNow('subscribe', sub.toJson());

  Future<void> unsubscribe(VPInvestSub sub) =>
      _sendNow('unsubscribe', sub.toJson());

  Future<void> setThrottleRules(List<ThrottleRule> rules) async {
    await _sendNow('setThrottle', {
      'rules': rules.map((r) => r.toJson()).toList(),
    });
  }

  @override
  Future<void> appForeground() async {
    GetIt.instance.get<Alice>().addLog(
          AliceLog(
            message: 'appForeground',
            level: DiagnosticLevel.summary,
          ),
        );

    return _sendNow('appForeground');
  }

  @override
  Future<void> appBackground() async {
    GetIt.instance.get<Alice>().addLog(
          AliceLog(
            message: 'appBackground',
            level: DiagnosticLevel.summary,
          ),
        );

    _sendNow('appBackground');
  }

  @override
  Future<void> networkUp() async {
    GetIt.instance.get<Alice>().addLog(
          AliceLog(
            message: 'networkUp',
            level: DiagnosticLevel.summary,
          ),
        );

    _sendNow('networkUp');
  }

  @override
  Future<void> networkDown() async {
    GetIt.instance.get<Alice>().addLog(
          AliceLog(
            message: 'networkDown',
            level: DiagnosticLevel.summary,
          ),
        );

    _sendNow('networkDown');
  }

  ListenerHandle addListener(
    VPInvestSub sub, {
    required SocketListener listener,
    required SocketSelector selector,
    bool emitImmediately = false,
  }) {
    return _controller.addListener(
      sub,
      listener: listener,
      listenWhen: selector,
      emitImmediately: emitImmediately,
    );
  }

  void removeListener(VPInvestSub sub, {required SocketListener listener}) {
    _controller.removeListener(sub, listener: listener);
  }

  @override
  Future<void> close() => _sendNow('close');

  @override
  Future<void> dispose() async {
    if (_disposed) return;

    _sendNow('dispose');

    _disposed = true;
    _bridge?.stop();

    _subscription?.cancel();
    _controller.dispose();
    _receivePort?.close();
    _isolate?.kill(priority: Isolate.immediate);

    _subscription = null;
    _receivePort = null;
    _isolate = null;
    _sendPort = null;

    if (_ready != null && !_ready!.isCompleted) {
      _ready!.completeError(StateError('Disposed'));
    }
    _ready = null;
  }

  @override
  Future<void> reconnect() => _sendNow('reconnect');

  @override
  ConnectionStateX get state => throw UnimplementedError();

  @override
  Stream<ConnectionStateX> get stateStream => _stateController.stream;
}
