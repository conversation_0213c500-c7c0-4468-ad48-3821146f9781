import 'package:vp_socket/vp_socket.dart';

mixin BuyUpSellDownSocketMixin {
  VPInvestSub? _topic;

  ListenerHandle? _listenerHandle;

  final _socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeBuyUpSellDownMarket(Set<String>? symbols) {
    if (symbols == null || symbols.isEmpty) return;

    _listenerHandle?.dispose();

    _topic = VPStockInfoSub(
      symbols: symbols,
      channel: VPSocketChannel.buyUpSellDownMarket.name,
    );

    _socket.subscribe(_topic!);

    _listenerHandle = _socket.addListener(
      _topic!,
      listener: _onSocketBuyUpSellDownListener,
      selector:
          (_, data) =>
              data is VPBuyUpSellDownMarketData &&
              symbols.contains(data.symbol),
    );
  }

  void unsubscribeBuyUpSellDownMarket() {
    _listenerHandle?.dispose();
    _listenerHandle = null;
  }

  void _onSocketBuyUpSellDownListener(VPSocketData? data) {
    if (data is VPBuyUpSellDownMarketData) {
      onSocketBuyUpSellDownListener(data);
    }
  }

  void onSocketBuyUpSellDownListener(VPBuyUpSellDownMarketData data) {}
}
