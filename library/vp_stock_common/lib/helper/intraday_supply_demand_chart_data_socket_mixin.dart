import 'package:vp_socket/vp_socket.dart';

mixin IntradaySupplyDemandChartSocketMixin {
  VPInvestSub? _topic;

  ListenerHandle? _listenerHandle;

  final _socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeIntradaySupplyDemandChartData(Set<String>? symbols) {
    if (symbols == null || symbols.isEmpty) return;

    _listenerHandle?.dispose();

    _topic = VPStockInfoSub(
      symbols: symbols,
      channel: VPSocketChannel.intradaySupplyDemandChart.name,
    );

    _socket.subscribe(_topic!);

    _listenerHandle = _socket.addListener(
      _topic!,
      listener: _onSocketIntradaySupplyDemandChartListener,
      selector:
          (_, data) =>
              data is VPIntradaySupplyDemandChartData &&
              symbols.contains(data.symbol),
    );
  }

  void unsubscribeIntradaySupplyDemandChartData() {
    _listenerHandle?.dispose();
    _listenerHandle = null;
  }

  void _onSocketIntradaySupplyDemandChartListener(VPSocketData? data) {
    if (data is VPIntradaySupplyDemandChartData) {
      onSocketIntradaySupplyDemandChartListener(data);
    }
  }

  void onSocketIntradaySupplyDemandChartListener(
    VPIntradaySupplyDemandChartData data,
  ) {}
}
