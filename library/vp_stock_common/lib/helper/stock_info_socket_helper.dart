import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class StockInfoSocketHelper {
  static bool buildWhen({
    required StockInfoFieldType type,
    required StockInfoModel item,
    required VPStockInfoData? preData,
    required VPStockInfoData? data,
  }) {
    final oldPrice = getValueFrom(
      type: StockInfoFieldType.closePrice,
      item: item,
      data: preData,
    );

    final newPrice = getValueFrom(
      type: StockInfoFieldType.closePrice,
      item: item,
      data: data,
    );

    if (newPrice != null && oldPrice != newPrice) return true;

    final oldValue = getValueFrom(type: type, item: item, data: preData);
    final newValue = getValueFrom(type: type, item: item, data: data);

    return newValue != null && oldValue != newValue;
  }

  static num? getClosePrice({
    required StockInfoModel item,
    required VPStockInfoData? data,
  }) {
    return getValueFrom(
      type: StockInfoFieldType.closePrice,
      item: item,
      data: data,
    );
  }

  static num? getChange({
    required StockInfoModel item,
    required VPStockInfoData? data,
  }) {
    return getValueFrom(
      type: StockInfoFieldType.change,
      item: item,
      data: data,
    );
  }

  static num? getPercentChange({
    required StockInfoModel item,
    required VPStockInfoData? data,
  }) {
    return getValueFrom(
      type: StockInfoFieldType.percentChange,
      item: item,
      data: data,
    );
  }

  static dynamic getValueFrom({
    required StockInfoFieldType type,
    required StockInfoModel item,
    required VPStockInfoData? data,
  }) {
    return switch (type) {
      StockInfoFieldType.symbol => item.symbol,
      StockInfoFieldType.closePrice => data?.closePrice ?? item.closePrice,
      StockInfoFieldType.vol => data?.totalTradingVolume ?? item.totalTradingVolume,
      StockInfoFieldType.change => data?.priceChange ?? item.priceChange,
      StockInfoFieldType.percentChange =>
      data?.percentChange ?? item.percentPriceChange,
      StockInfoFieldType.diff => item.diff,
    };
  }

  static bool hasChange({
    required StockInfoFieldType type,
    required StockInfoModel item,
    required VPStockInfoData? preData,
    required VPStockInfoData? data,
  }) {
    final oldValue = getValueFrom(type: type, item: item, data: preData);
    final newValue = getValueFrom(type: type, item: item, data: data);

    return oldValue != newValue;
  }
}
