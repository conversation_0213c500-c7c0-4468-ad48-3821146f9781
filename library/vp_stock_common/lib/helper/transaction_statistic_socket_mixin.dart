import 'package:vp_socket/vp_socket.dart';

mixin TransactionStatisticSocketMixin {
  VPInvestSub? _topic;

  ListenerHandle? _listenerHandle;

  final _socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeTransactionStatistic(Set<String>? symbols) {
    if (symbols == null || symbols.isEmpty) return;

    _listenerHandle?.dispose();

    _topic = VPStockInfoSub(
      symbols: symbols,
      channel: VPSocketChannel.transactionStatistic.name,
    );

    _socket.subscribe(_topic!);

    _listenerHandle = _socket.addListener(
      _topic!,
      listener: _onSocketTransactionStatisticListener,
      selector:
          (_, data) =>
              data is VPTransactionStatisticData &&
              symbols.contains(data.symbol),
    );
  }

  void unsubscribeTransactionStatistic() {
    _listenerHandle?.dispose();
    _listenerHandle = null;
  }

  void _onSocketTransactionStatisticListener(VPSocketData? data) {
    if (data is VPTransactionStatisticData) {
      onSocketTransactionStatisticListener(data);
    }
  }

  void onSocketTransactionStatisticListener(VPTransactionStatisticData data) {}
}
