enum IndexCode {
  VNINDEX,
  VN30,
  HNXINDEX,
  HNX30,
  UPCOMINDEX,
  VN100;

  String get name {
    return switch (this) {
      HNXINDEX => 'HNX',
      VNINDEX => 'VNINDEX',
      VN30 => 'VN30',
      UPCOMINDEX => 'UPCOM',
      HNX30 => 'HNX30',
      VN100 => 'VN100',
    };
  }

  String get value {
    return switch (this) {
      HNXINDEX => 'HNXINDEX',
      VNINDEX => 'VNINDEX',
      VN30 => 'VN30',
      UPCOMINDEX => 'UPCOMINDEX',
      HNX30 => 'HNX30',
      VN100 => 'VN100',
    };
  }

  String get marketCode {
    return switch (this) {
      HNXINDEX => 'HNX',
      VNINDEX => 'HOSE',
      VN30 => '30',
      UPCOMINDEX => 'UPCOM',
      HNX30 => 'HNX30',
      VN100 => 'VN100',
    };
  }

  String get socketChannel {
    return switch (this) {
      HNXINDEX => 'HNXINDEX',
      VNINDEX => 'VNINDEX',
      VN30 => 'VN30',
      UPCOMINDEX => 'UPCOMINDEX',
      HNX30 => 'HNX30',
      VN100 => 'VN100',
    };
  }
}
