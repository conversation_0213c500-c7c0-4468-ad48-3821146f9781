import 'package:flutter/cupertino.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'market_price_chart_model.dart';

part 'market_info_model.g.dart';

@JsonSerializable()
class MarketInfoModel {
  final IndexCode indexCode;

  @<PERSON>son<PERSON>ey(name: 'indexValue')
  final double marketIndex;

  final num? totalVolume;

  final num? totalValue;

  @JsonKey(defaultValue: 0)
  final double indexChange;

  @JsonKey(defaultValue: 0)
  final double indexPercentChange;

  final int? noChange;

  final String indexColor;

  final String? marketStatus;

  final num? totalTrade;

  final String? indexTime;

  @JsonKey(defaultValue: [])
  final List<MarketPriceChartIndex> index;

  @Json<PERSON>ey(defaultValue: '')
  final String marketCode;

  final num? oddLotTotalVolume;

  final num? oddLotTotalValue;

  final num? advances;

  final num? declines;

  final num? numberOfCe;

  final num? numberOfFl;

  const MarketInfoModel({
    required this.indexCode,
    required this.marketIndex,
    required this.indexColor,
    required this.indexChange,
    required this.indexPercentChange,
    required this.marketStatus,
    required this.index,
    required this.marketCode,
    this.totalVolume,
    this.totalValue,
    this.noChange,
    this.totalTrade,
    this.indexTime,
    this.oddLotTotalVolume,
    this.oddLotTotalValue,
    this.advances,
    this.declines,
    this.numberOfCe,
    this.numberOfFl,
  });

  factory MarketInfoModel.fromJson(Map<String, dynamic> json) =>
      _$MarketInfoModelFromJson(json);

  double get reference => marketIndex - indexChange;

  List<Color> get chartColor {
    final referenceValue = reference.toPrecision(2);
    final currentValue = marketIndex.toDouble().toPrecision(2);

    if (referenceValue < currentValue) {
      return [vpColor.chartGreen, vpColor.chartGreen];
    }

    if (referenceValue > currentValue) {
      return [vpColor.chartRed, vpColor.chartRed];
    }

    return [vpColor.chartYellow, vpColor.chartYellow];
  }

  List<Color> get chartShadowColor {
    final referenceValue = reference.toPrecision(2);
    final currentValue = marketIndex.toDouble().toPrecision(2);

    if (referenceValue < currentValue) {
      return [
        vpColor.chartGreen.withValues(alpha: 0.4),
        vpColor.chartGreen.withValues(alpha: 0.2),
        vpColor.chartGreen.withValues(alpha: 0.1),
      ];
    }

    if (referenceValue > currentValue) {
      return [
        vpColor.chartRed.withValues(alpha: 0.4),
        vpColor.chartRed.withValues(alpha: 0.2),
        vpColor.chartRed.withValues(alpha: 0.1),
      ];
    }

    return [
      vpColor.chartYellow.withValues(alpha: 0.3),
      vpColor.chartYellow.withValues(alpha: 0.2),
      vpColor.chartYellow.withValues(alpha: 0.1),
    ];
  }
}
