// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'market_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MarketInfoModel _$MarketInfoModelFromJson(Map<String, dynamic> json) =>
    MarketInfoModel(
      indexCode: $enumDecode(_$IndexCodeEnumMap, json['indexCode']),
      marketIndex: (json['indexValue'] as num).toDouble(),
      indexColor: json['indexColor'] as String,
      indexChange: (json['indexChange'] as num?)?.toDouble() ?? 0,
      indexPercentChange: (json['indexPercentChange'] as num?)?.toDouble() ?? 0,
      marketStatus: json['marketStatus'] as String?,
      index:
          (json['index'] as List<dynamic>?)
              ?.map(
                (e) =>
                    MarketPriceChartIndex.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
      marketCode: json['marketCode'] as String? ?? '',
      totalVolume: json['totalVolume'] as num?,
      totalValue: json['totalValue'] as num?,
      noChange: (json['noChange'] as num?)?.toInt(),
      totalTrade: json['totalTrade'] as num?,
      indexTime: json['indexTime'] as String?,
      oddLotTotalVolume: json['oddLotTotalVolume'] as num?,
      oddLotTotalValue: json['oddLotTotalValue'] as num?,
      advances: json['advances'] as num?,
      declines: json['declines'] as num?,
      numberOfCe: json['numberOfCe'] as num?,
      numberOfFl: json['numberOfFl'] as num?,
    );

Map<String, dynamic> _$MarketInfoModelToJson(MarketInfoModel instance) =>
    <String, dynamic>{
      'indexCode': _$IndexCodeEnumMap[instance.indexCode]!,
      'indexValue': instance.marketIndex,
      'totalVolume': instance.totalVolume,
      'totalValue': instance.totalValue,
      'indexChange': instance.indexChange,
      'indexPercentChange': instance.indexPercentChange,
      'noChange': instance.noChange,
      'indexColor': instance.indexColor,
      'marketStatus': instance.marketStatus,
      'totalTrade': instance.totalTrade,
      'indexTime': instance.indexTime,
      'index': instance.index,
      'marketCode': instance.marketCode,
      'oddLotTotalVolume': instance.oddLotTotalVolume,
      'oddLotTotalValue': instance.oddLotTotalValue,
      'advances': instance.advances,
      'declines': instance.declines,
      'numberOfCe': instance.numberOfCe,
      'numberOfFl': instance.numberOfFl,
    };

const _$IndexCodeEnumMap = {
  IndexCode.VNINDEX: 'VNINDEX',
  IndexCode.VN30: 'VN30',
  IndexCode.HNXINDEX: 'HNXINDEX',
  IndexCode.HNX30: 'HNX30',
  IndexCode.UPCOMINDEX: 'UPCOMINDEX',
  IndexCode.VN100: 'VN100',
};
