import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/fu_stock_detail/fu_stock_detail_args.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/confirm_dialog.dart';

StockCommonNavigator get stockCommonNavigator =>
    GetIt.instance.get<StockCommonNavigator>();

abstract class StockCommonNavigator {
  Future showCreateNewWatchlistDialog(BuildContext context);

  Future openBondSearchPage(BuildContext context, {String? hint});

  Future openSearchPage(BuildContext context, {SearchArgs? args});

  Future openPriceBoardPage(BuildContext context);

  Future openStockDetailPage(
    BuildContext context, {
    required StockDetailArgs args,
    bool pushReplacementNamed = false,
  });

  Future openFuStockDetailPage(
    BuildContext context, {
    required FuStockDetailArgs args,
    bool pushReplacementNamed = false,
  });

  Future showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    ButtonType acceptButtonType = ButtonType.primary,
    ButtonDangerType acceptButtonDangerType = ButtonDangerType.defaultType,
  });

  void openSignInRequiredDialog(
    BuildContext context, {
    VoidCallback? onSignInSuccess,
  });

  void openMarketDetailPage(
    BuildContext context, {
    required MarketDetailArgs args,
  });

  Future openAddSymbolToWatchlistSelectorBottomSheet(
    BuildContext context, {
    required List<String> symbols,
  });
}

class StockCommonNavigatorImpl extends StockCommonNavigator {
  @override
  Future showCreateNewWatchlistDialog(BuildContext context) {
    return VPPopup.custom(
      child: const CreateNewWatchlistDialog(),
      padding: const EdgeInsets.all(20),
    ).showDialog(context);
  }

  @override
  Future openBondSearchPage(BuildContext context, {String? hint}) {
    return context.pushNamed(StockCommonRouterName.bondSearch.routeName);
  }

  @override
  Future openSearchPage(BuildContext context, {SearchArgs? args}) {
    return context.pushNamed(
      StockCommonRouterName.search.routeName,
      queryParameters: args?.toQueryParams() ?? const {},
    );
  }

  @override
  Future showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    Function? onClose,
    Function? onConfirm,
    ButtonType acceptButtonType = ButtonType.primary,
    ButtonDangerType acceptButtonDangerType = ButtonDangerType.defaultType,
  }) {
    return VPPopup.custom(
      child: ConfirmDialog(
        title: title,
        content: content,
        onClose: onClose,
        onConfirm: onConfirm,
        acceptButtonType: acceptButtonType,
        acceptButtonDangerType: acceptButtonDangerType,
      ),
      padding: const EdgeInsets.all(20),
    ).showDialog(context);
  }

  @override
  void openSignInRequiredDialog(
    BuildContext context, {
    VoidCallback? onSignInSuccess,
  }) {
    VPPopup.custom(
      child: SignInRequiredDialog(
        onSignIn:
            () => context.pushNamed(
              StockCommonRouterName.signIn.routeName,
              extra: SignInArgs(onSignInSuccess: onSignInSuccess),
            ),
      ),
    ).showDialog(context);
  }

  @override
  Future openPriceBoardPage(BuildContext context) {
    return context.pushNamed(StockCommonRouterName.priceBoard.routeName);
  }

  @override
  Future openStockDetailPage(
    BuildContext context, {
    required StockDetailArgs args,
    bool pushReplacementNamed = false,
  }) async {
    if (pushReplacementNamed) {
      context.pushReplacementNamed(
        StockCommonRouterName.stockDetail.routeName,
        queryParameters: args.toQueryParams(),
      );

      return;
    }

    return context.pushNamed(
      StockCommonRouterName.stockDetail.routeName,
      queryParameters: args.toQueryParams(),
    );
  }

  @override
  void openMarketDetailPage(
    BuildContext context, {
    required MarketDetailArgs args,
  }) {
    context.pushNamed(
      StockCommonRouterName.marketDetail.routeName,
      queryParameters: args.toQueryParams(),
    );
  }

  @override
  Future openFuStockDetailPage(
    BuildContext context, {
    required FuStockDetailArgs args,
    bool pushReplacementNamed = false,
  }) async {
    if (pushReplacementNamed) {
      context.pushReplacementNamed(
        StockCommonRouterName.fuStockDetail.routeName,
        queryParameters: args.toQueryParams(),
      );

      return;
    }

    return context.pushNamed(
      StockCommonRouterName.fuStockDetail.routeName,
      queryParameters: args.toQueryParams(),
    );
  }

  @override
  Future openAddSymbolToWatchlistSelectorBottomSheet(
    BuildContext context, {
    required List<String> symbols,
  }) {
    return VPPopup.bottomSheet(
      AddSymbolToWatchListBottomSheet(symbols: symbols),
    ).showSheet(context);
  }
}
