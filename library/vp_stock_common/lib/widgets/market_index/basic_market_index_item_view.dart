import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

class BasicMarketIndexItemView extends StatefulWidget {
  const BasicMarketIndexItemView({required this.marketInfo, super.key});

  final MarketInfoModel marketInfo;

  @override
  State<BasicMarketIndexItemView> createState() =>
      _BasicMarketIndexItemViewState();
}

class _BasicMarketIndexItemViewState extends State<BasicMarketIndexItemView>
    with AutomaticKeepAliveClientMixin {
  MarketInfoModel get marketInfo => widget.marketInfo;

  double get marketIndex => marketInfo.marketIndex;

  double get indexChange => marketInfo.indexChange;

  double get indexPercentChange => marketInfo.indexPercentChange;

  void onOpenMarketDetailPage() {
    if (!isLoggedIn) {
      stockCommonNavigator.openSignInRequiredDialog(
        context,
        onSignInSuccess: () => open(),
      );

      return;
    }

    open();
  }

  void open() {
    stockCommonNavigator.openMarketDetailPage(
      getContext,
      args: MarketDetailArgs(indexCode: marketInfo.indexCode),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      onTap: onOpenMarketDetailPage,
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: vpColor.backgroundElevation1,
        ),
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            Text(
              marketInfo.indexCode.name,
              maxLines: 1,
              style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
            ),

            const SizedBox(width: 8),

            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: marketInfo.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (preData, data) {
                final oldValue = preData?.marketIndex ?? marketInfo.marketIndex;

                return oldValue != data?.marketIndex;
              },
              builder: (_, __, data, Widget? child) {
                final value =
                    data?.marketIndex?.toDouble() ?? marketInfo.marketIndex;

                return Text(
                  value == 0 || value == -1 ? '-' : value.getIndexFormatted(),
                  style: vpTextStyle.subtitle14.copyColor(getColor(data)),
                );
              },
            ),

            const SizedBox(width: 8),

            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: marketInfo.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (preData, data) {
                return preData?.marketIndex != data?.marketIndex ||
                    preData?.indexChange != data?.indexChange;
              },
              builder: (_, __, data, child) {
                return priceIconView(data);
              },
            ),

            const SizedBox(width: 4),

            /// change and percent change
            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: marketInfo.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (preData, data) {
                return preData?.marketIndex != data?.marketIndex ||
                    preData?.indexChange != data?.indexChange ||
                    preData?.indexPercentChange != data?.indexPercentChange;
              },
              builder: (_, __, data, child) {
                final index = data?.marketIndex ?? marketIndex;
                final change = data?.indexChange ?? indexChange;

                final percentChange =
                    data?.indexPercentChange?.toDouble() ?? indexPercentChange;

                final changeFormat = FormatUtils.formatClosePrice(
                  convertToThousand: false,
                  showSign: false,
                  trimZero: false,
                  change,
                );

                final percentFormat = FormatUtils.formatPercent(
                  percentChange,
                  showSign: false,
                );

                return Text(
                  index == -1
                      ? '-%'
                      : index == 0
                      ? '0 0%'
                      : '$changeFormat ($percentFormat)',
                  maxLines: 1,
                  style: vpTextStyle.captionMedium.copyColor(getColor(data)),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget priceIconView(VPMarketInfoData? socketData) {
    final index = socketData?.marketIndex ?? marketIndex;
    final change = socketData?.indexChange ?? indexChange;
    final percentChange = socketData?.indexPercentChange ?? indexPercentChange;

    if (index == -1) {
      return Assets.icons.icArrowIncrease.svg(
        color: vpColor.iconAccentGreen,
        width: 8,
      );
    }

    if (index == 0) {
      return Assets.icons.icDot.svg(color: vpColor.iconAccentYellow);
    }

    if (change > 0 || percentChange > 0) {
      return Assets.icons.icArrowIncrease.svg(
        color: vpColor.iconAccentGreen,
        width: 8,
      );
    }

    if (change < 0 || percentChange < 0) {
      return Assets.icons.icArrowDecrease.svg(
        color: vpColor.iconAccentRed,
        width: 8,
      );
    }

    return Assets.icons.icDot.svg(color: vpColor.iconAccentYellow);
  }

  Color getColor(VPMarketInfoData? socketData) {
    final index = socketData?.marketIndex ?? marketIndex;
    final change = socketData?.indexChange ?? indexChange;
    final percentChange = socketData?.indexPercentChange ?? indexPercentChange;

    if (index == -1) {
      return vpColor.textPriceGreen;
    }

    if (index == 0) {
      return vpColor.textPriceYellow;
    }

    if (change > 0 || percentChange > 0) {
      return vpColor.textPriceGreen;
    }

    if (change < 0 || percentChange < 0) {
      return vpColor.textPriceRed;
    }

    return vpColor.textPriceYellow;
  }

  @override
  bool get wantKeepAlive => true;
}
