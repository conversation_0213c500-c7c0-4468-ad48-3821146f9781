import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/market_index/charts/market_price_chart_extensions.dart';

import 'charts/market_chart.dart';

class MarketIndexItemView extends StatefulWidget {
  const MarketIndexItemView({required this.marketInfo, super.key});

  final MarketInfoModel marketInfo;

  @override
  State<MarketIndexItemView> createState() => _MarketIndexItemViewState();
}

class _MarketIndexItemViewState extends State<MarketIndexItemView>
    with AutomaticKeepAliveClientMixin {
  MarketInfoModel get marketInfo => widget.marketInfo;

  double get marketIndex => marketInfo.marketIndex;

  double get indexChange => marketInfo.indexChange;

  double get indexPercentChange => marketInfo.indexPercentChange;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      width: 200,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: vpColor.backgroundElevation1,
      ),
      padding: const EdgeInsets.all(8),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// symbol
                Expanded(
                  child: Text(
                    marketInfo.indexCode.name,
                    maxLines: 1,
                    style: vpTextStyle.subtitle16.copyColor(
                      vpColor.textPrimary,
                    ),
                  ),
                ),

                buildMarketIndexView(),
              ],
            ),
          ),

          /// chart
          Expanded(
            flex: 2,
            child: AbsorbPointer(
              absorbing: true,
              child: MarketChart(
                indexCode: marketInfo.indexCode,
                referencePrice: marketInfo.reference,
                chartData: [...marketInfo.index.toChartDataList()],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildMarketIndexView() {
    final marketIndex = marketInfo.marketIndex;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        /// index
        VPSocketInvestmentBuilder<VPMarketInfoData>(
          symbol: marketInfo.indexCode.socketChannel,
          channel: VPSocketChannel.marketInfo.name,
          buildWhen: (preData, data) {
            final oldValue = preData?.marketIndex ?? marketInfo.marketIndex;

            return oldValue != data?.marketIndex;
          },
          builder: (_, __, data, Widget? child) {
            final value =
                data?.marketIndex?.toDouble() ?? marketInfo.marketIndex;

            return Text(
              value == 0 || value == -1 ? '-' : value.getIndexFormatted(),
              maxLines: 1,
              style: vpTextStyle.subtitle14.copyColor(getColor(data)),
            );
          },
        ),

        /// icon
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: marketInfo.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (preData, data) {
                return preData?.marketIndex != data?.marketIndex ||
                    preData?.indexChange != data?.indexChange;
              },
              builder: (_, __, data, child) {
                return priceIconView(data);
              },
            ),

            const SizedBox(width: 4),

            /// change and percent change
            VPSocketInvestmentBuilder<VPMarketInfoData>(
              symbol: marketInfo.indexCode.socketChannel,
              channel: VPSocketChannel.marketInfo.name,
              buildWhen: (preData, data) {
                return preData?.marketIndex != data?.marketIndex ||
                    preData?.indexChange != data?.indexChange ||
                    preData?.indexPercentChange != data?.indexPercentChange;
              },
              builder: (_, __, data, child) {
                final index = data?.marketIndex ?? marketIndex;
                final change = data?.indexChange ?? indexChange;

                final percentChange =
                    data?.indexPercentChange?.toDouble() ?? indexPercentChange;

                final changeFormat = FormatUtils.formatClosePrice(
                  convertToThousand: false,
                  showSign: false,
                  trimZero: false,
                  change,
                );

                final percentFormat = FormatUtils.formatPercent(
                  percentChange,
                  showSign: false,
                );

                return Text(
                  index == -1
                      ? '-%'
                      : index == 0
                      ? '0 0%'
                      : '$changeFormat ($percentFormat)',
                  maxLines: 1,
                  style: vpTextStyle.captionMedium.copyColor(getColor(data)),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget priceIconView(VPMarketInfoData? socketData) {
    final index = socketData?.marketIndex ?? marketIndex;
    final change = socketData?.indexChange ?? indexChange;
    final percentChange = socketData?.indexPercentChange ?? indexPercentChange;

    if (index == -1) {
      return Assets.icons.icArrowIncrease.svg(
        color: vpColor.iconAccentGreen,
        width: 12,
      );
    }

    if (index == 0) {
      return Assets.icons.icDot.svg(color: vpColor.iconAccentYellow);
    }

    if (change > 0 || percentChange > 0) {
      return Assets.icons.icArrowIncrease.svg(
        color: vpColor.iconAccentGreen,
        width: 12,
      );
    }

    if (change < 0 || percentChange < 0) {
      return Assets.icons.icArrowDecrease.svg(
        color: vpColor.iconAccentRed,
        width: 12,
      );
    }

    return Assets.icons.icDot.svg(color: vpColor.iconAccentYellow);
  }

  Color getColor(VPMarketInfoData? socketData) {
    final index = socketData?.marketIndex ?? marketIndex;
    final change = socketData?.indexChange ?? indexChange;
    final percentChange = socketData?.indexPercentChange ?? indexPercentChange;

    if (index == -1) {
      return vpColor.textPriceGreen;
    }

    if (index == 0) {
      return vpColor.textPriceYellow;
    }

    if (change > 0 || percentChange > 0) {
      return vpColor.textPriceGreen;
    }

    if (change < 0 || percentChange < 0) {
      return vpColor.textPriceRed;
    }

    return vpColor.textPriceYellow;
  }

  @override
  bool get wantKeepAlive => true;
}
