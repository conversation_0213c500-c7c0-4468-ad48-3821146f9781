import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/market_index/market_index_cubit.dart';
import 'package:vp_stock_common/widgets/market_index/market_index_shimmer_item_view.dart';

class MarketIndexView extends StatefulWidget {
  const MarketIndexView({
    required this.indexCode,
    required this.primaryBuilder,
    required this.marketIndexType,
    this.onTap,
    this.secondaryBuilder,
    this.primaryHeight = 80,
    this.secondaryHeight = 40,
    this.invokeWhenLoadDataSuccess = false,
    this.animationDuration = const Duration(milliseconds: 400),
    super.key,
  });

  final IndexCode indexCode;

  final Duration animationDuration;

  final double primaryHeight;

  final double secondaryHeight;

  final MarketIndexType marketIndexType;

  final Widget Function(MarketInfoModel) primaryBuilder;

  final Widget Function(MarketInfoModel)? secondaryBuilder;

  final ValueChanged<MarketInfoModel>? onTap;

  final bool invokeWhenLoadDataSuccess;

  @override
  State<MarketIndexView> createState() => _MarketIndexViewState();
}

class _MarketIndexViewState extends State<MarketIndexView>
    with AutomaticKeepAliveClientMixin {
  bool get isPrimary => widget.marketIndexType == MarketIndexType.primary;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider<MarketIndexCubit>(
      create: (_) => MarketIndexCubit(indexCode: widget.indexCode)..loadData(),
      child: BlocConsumer<MarketIndexCubit, MarketIndexState>(
        listenWhen: (pState, state) => pState.data != state.data,
        listener: (context, MarketIndexState state) {
          if (widget.invokeWhenLoadDataSuccess && state.data != null) {
            widget.onTap?.call(state.data!);
          }
        },
        builder: (context, state) {
          if (state.apiStatus.isError) {
            return _MarketIndexErrorView(
              onRetry: () => context.read<MarketIndexCubit>().onRefresh(),
            );
          }

          if (state.data != null) {
            if (widget.secondaryBuilder != null) {
              return AnimatedSwitcher(
                duration: widget.animationDuration,
                transitionBuilder: (child, animation) {
                  return ScaleTransition(scale: animation, child: child);
                },
                child: GestureDetector(
                  onTap:
                      widget.onTap != null
                          ? () => widget.onTap!(state.data!)
                          : null,
                  child: AbsorbPointer(
                    absorbing: widget.onTap != null,
                    child:
                        isPrimary
                            ? widget.primaryBuilder(state.data!)
                            : widget.secondaryBuilder!(state.data!),
                  ),
                ),
              );
            }

            return GestureDetector(
              onTap:
                  widget.onTap != null
                      ? () => widget.onTap!(state.data!)
                      : null,
              child: AbsorbPointer(
                absorbing: widget.onTap != null,
                child: widget.primaryBuilder(state.data!),
              ),
            );
          }

          return Container(
            width: 200,
            height: isPrimary ? widget.primaryHeight : widget.secondaryHeight,
            padding: const EdgeInsets.only(right: 8),
            child: const MarketIndexShimmerItemView(),
          );
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class _MarketIndexErrorView extends StatelessWidget {
  const _MarketIndexErrorView({this.onRetry});

  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: vpColor.backgroundElevation1,
      ),
      margin: const EdgeInsets.only(right: 8),
      padding: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          DesignAssets.icons.noData.icNodata.svg(height: 30),

          VpsButton.teriatySmall(onPressed: onRetry, title: 'Thử lại'),
        ],
      ),
    );
  }
}
