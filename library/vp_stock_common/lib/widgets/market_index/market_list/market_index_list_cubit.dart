import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_core/vp_core.dart';

part 'market_index_list_state.dart';

class MarketIndexListCubit extends AppCubit<MarketIndexListState>
    with MarketInfoSocketMixin {
  MarketIndexListCubit({IndexCode? indexCode})
    : super(MarketIndexListState(indexCodeSelected: indexCode));

  void onIndexCodeChanged(IndexCode indexCode) {
    if (state.indexCodeSelected == indexCode) return;

    emit(state.copyWith(indexCode: indexCode));
  }
}
