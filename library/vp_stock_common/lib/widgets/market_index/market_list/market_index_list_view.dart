import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/market_index/market_index_item_view.dart';
import 'package:vp_stock_common/widgets/market_index/market_index_view.dart';
import 'package:vp_stock_common/widgets/market_index/market_list/market_index_list_cubit.dart';

enum MarketIndexType { primary, secondary }

class MarketIndexList extends StatelessWidget {
  const MarketIndexList({
    required this.primaryBuilder,
    this.onChanged,
    this.indexCode,
    this.secondaryBuilder,
    this.indexCodes,
    this.itemExtent,
    this.primaryHeight = 80,
    this.secondaryHeight = 40,
    this.enableSelection = false,
    this.marketIndexType = MarketIndexType.primary,
    this.padding = const EdgeInsets.fromLTRB(16, 0, 16, 0),
    this.borderColor = Colors.red,
    super.key,
  });

  factory MarketIndexList.simple({
    IndexCode? indexCode,
    bool enableSelection = false,
    List<IndexCode>? indexCodes,
    ValueChanged<MarketInfoModel>? onTap,
    EdgeInsets padding = const EdgeInsets.fromLTRB(16, 0, 16, 0),
    Color borderColor = Colors.red,
  }) => MarketIndexList(
    onChanged: onTap,
    indexCode: indexCode,
    indexCodes: indexCodes,
    itemExtent: 200,
    primaryHeight: 80,
    padding: padding,
    enableSelection: enableSelection,
    borderColor: borderColor,
    primaryBuilder: (marketInfo) => MarketIndexItemView(marketInfo: marketInfo),
  );

  final IndexCode? indexCode;

  final EdgeInsets padding;

  final List<IndexCode>? indexCodes;

  final double primaryHeight;

  final double secondaryHeight;

  final double? itemExtent;

  final MarketIndexType marketIndexType;

  final Widget Function(MarketInfoModel) primaryBuilder;

  final Widget Function(MarketInfoModel)? secondaryBuilder;

  final ValueChanged<MarketInfoModel>? onChanged;

  final bool enableSelection;

  final Color borderColor;

  bool get isPrimary => marketIndexType == MarketIndexType.primary;

  final duration = const Duration(milliseconds: 400);

  void onMarketSelected(BuildContext context, MarketInfoModel market) {
    if (enableSelection) {
      context.read<MarketIndexListCubit>().onIndexCodeChanged(market.indexCode);
    }
    onChanged?.call(market);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MarketIndexListCubit>(
      create: (context) => MarketIndexListCubit(indexCode: indexCode),
      child: Builder(
        builder: (context) {
          final child = ListView(
            padding: padding,
            scrollDirection: Axis.horizontal,
            itemExtent: itemExtent,
            children:
                (indexCodes ??
                        [
                          IndexCode.VNINDEX,
                          IndexCode.VN30,
                          IndexCode.HNXINDEX,
                          IndexCode.HNX30,
                          IndexCode.UPCOMINDEX,
                        ])
                    .map((e) => buildItemView(context, e))
                    .toList(),
          );

          if (secondaryBuilder != null) {
            return AnimatedContainer(
              height: isPrimary ? primaryHeight : secondaryHeight,
              duration: duration,
              child: child,
            );
          }

          return SizedBox(
            height: isPrimary ? primaryHeight : secondaryHeight,
            child: child,
          );
        },
      ),
    );
  }

  Widget buildItemView(BuildContext context, IndexCode e) {
    final child = MarketIndexView(
      indexCode: e,
      invokeWhenLoadDataSuccess: enableSelection && indexCode == e,
      onTap: (market) => onMarketSelected(context, market),
      animationDuration: duration,
      marketIndexType: marketIndexType,
      primaryBuilder: primaryBuilder,
      secondaryBuilder: secondaryBuilder,
      primaryHeight: primaryHeight,
      secondaryHeight: secondaryHeight,
    );

    return BlocBuilder<MarketIndexListCubit, MarketIndexListState>(
      buildWhen:
          (pState, state) =>
              pState.indexCodeSelected == e || state.indexCodeSelected == e,
      builder: (context, state) {
        final isSelected = enableSelection && state.indexCodeSelected == e;

        return Container(
          decoration: BoxDecoration(
            border: isSelected ? Border.all(color: borderColor) : null,
            borderRadius: BorderRadius.circular(4),
          ),
          margin: const EdgeInsets.only(right: 8),
          child: child,
        );
      },
    );
  }
}
