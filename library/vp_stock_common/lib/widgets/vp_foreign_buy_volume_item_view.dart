import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

typedef ForeignBuyStyleBuilder = TextStyle? Function(num? floor);
typedef ForeignBuyPriceBuilder = String? Function(num? floor);

class VPForeignBuyVolumeItemView extends StatelessWidget {
  VPForeignBuyVolumeItemView({
    required this.symbol,
    required this.initForeignBuyVolume,
    this.styleBuilder,
    this.priceBuilder,
    this.onTap,
    this.alignment,
    super.key,
  });

  factory VPForeignBuyVolumeItemView.stock({
    required StockInfoModel stock,
    ForeignBuyStyleBuilder? styleBuilder,
    ForeignBuyPriceBuilder? priceBuilder,
    Function(num?)? onTap,
  }) => VPForeignBuyVolumeItemView(
    onTap: onTap,
    symbol: stock.symbol,
    initForeignBuyVolume: stock.foreignBuyVolume,
    styleBuilder: styleBuilder,
    priceBuilder: priceBuilder,
  );

  final String symbol;

  final num? initForeignBuyVolume;

  final ForeignBuyPriceBuilder? priceBuilder;

  final ForeignBuyStyleBuilder? styleBuilder;

  final AlignmentGeometry? alignment;

  final Function(num)? onTap;

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: symbol,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen: (preData, data) {
        return (preData?.foreignBuyVolume ?? initForeignBuyVolume) !=
            data?.foreignBuyVolume;
      },
      builder: (context, _, data, child) {
        final foreignBuy = data?.foreignBuyVolume?.toDouble() ?? initForeignBuyVolume;

        final child = Text(
          priceBuilder?.call(foreignBuy) ?? FormatUtils.formatVol(foreignBuy),
          style:
              styleBuilder?.call(foreignBuy) ??
              vpTextStyle.captionSemiBold?.copyWith(color: vpColor.textPrimary),
        );

        if (onTap == null) return child;

        return InkWell(
          onTap: () {
            if (foreignBuy != null) onTap?.call(foreignBuy);
          },
          child: child,
        );
      },
    );
  }
}
