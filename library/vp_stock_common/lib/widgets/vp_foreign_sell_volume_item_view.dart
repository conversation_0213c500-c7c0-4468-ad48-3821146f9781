import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

typedef ForeignSellStyleBuilder = TextStyle? Function(num? floor);
typedef ForeignSellPriceBuilder = String? Function(num? floor);

class VPForeignSellVolumeItemView extends StatelessWidget {
  VPForeignSellVolumeItemView({
    required this.symbol,
    required this.initForeignSellVolume,
    this.styleBuilder,
    this.priceBuilder,
    this.onTap,
    this.alignment,
    super.key,
  });

  factory VPForeignSellVolumeItemView.stock({
    required StockInfoModel stock,
    ForeignSellStyleBuilder? styleBuilder,
    ForeignSellPriceBuilder? priceBuilder,
    Function(num?)? onTap,
  }) => VPForeignSellVolumeItemView(
    onTap: onTap,
    symbol: stock.symbol,
    initForeignSellVolume: stock.foreignSellVolume,
    styleBuilder: styleBuilder,
    priceBuilder: priceBuilder,
  );

  final String symbol;

  final num? initForeignSellVolume;

  final ForeignSellPriceBuilder? priceBuilder;

  final ForeignSellStyleBuilder? styleBuilder;

  final AlignmentGeometry? alignment;

  final Function(num)? onTap;

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: symbol,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen: (preData, data) {
        return (preData?.foreignSellVolume ?? initForeignSellVolume) !=
            data?.foreignSellVolume;
      },
      builder: (context, _, data, child) {
        final foreignSell =
            data?.foreignSellVolume?.toDouble() ?? initForeignSellVolume;

        final child = Text(
          priceBuilder?.call(foreignSell) ?? FormatUtils.formatVol(foreignSell),
          style:
              styleBuilder?.call(foreignSell) ??
              vpTextStyle.captionSemiBold?.copyWith(color: vpColor.textPrimary),
        );

        if (onTap == null) return child;

        return InkWell(
          onTap: () {
            if (foreignSell != null) onTap?.call(foreignSell);
          },
          child: child,
        );
      },
    );
  }
}
