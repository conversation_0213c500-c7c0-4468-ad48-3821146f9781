import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class VPProfitIconView extends StatelessWidget {
  VPProfitIconView({
    required this.symbol,
    this.closePrice,
    this.refPrice,
    this.initColor,
  });

  factory VPProfitIconView.stock({required StockInfoModel stock}) =>
      VPProfitIconView(
        symbol: stock.symbol,
        closePrice: stock.closePrice,
        refPrice: stock.refPrice,
        initColor: stock.color,
      );

  final String symbol;

  final num? closePrice;

  final num? refPrice;

  final Color? initColor;

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: symbol,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen: (preData, data) {
        return (preData?.closePrice ?? closePrice) != data?.closePrice;
      },
      builder: (context, _, data, child) {
        final closePrice = data?.closePrice?.toDouble() ?? this.closePrice;

        final price = closePrice ?? this.refPrice ?? 0;

        final refPrice = data?.reference ?? this.refPrice;

        final currentPrice = price == 0 ? refPrice : price;

        final color = data.color ?? initColor;

        if (currentPrice == null || refPrice == null) {
          return const SizedBox.shrink();
        }

        if (currentPrice < refPrice) {
          return CommonAssets.icons.icArrowDecrease.svg(
            color: color ?? context.colors.textPriceRed,
            width: 8,
          );
        }

        if (currentPrice > refPrice) {
          return Assets.icons.icArrowIncrease.svg(
            color: color ?? context.colors.textPriceGreen,
            width: 8,
          );
        }

        return Assets.icons.icDot.svg(
          color: context.colors.textPriceYellow,
          width: 4,
          height: 4,
        );
      },
    );
  }
}
