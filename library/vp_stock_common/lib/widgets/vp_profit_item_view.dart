import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class VPProfitItemView extends StatelessWidget {
  const VPProfitItemView({
    required this.stock,
    this.spacing = 3,
    this.showIcon = true,
    this.style,
    super.key,
  });

  final StockInfoModel stock;

  final TextStyle? style;

  final bool showIcon;

  final double spacing;

  TextStyle? get textStyle => style ?? vpTextStyle.subtitle14;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: spacing,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (showIcon) VPProfitIconView.stock(stock: stock),

        VPSocketInvestmentBuilder<VPStockInfoData>(
          symbol: stock.symbol,
          channel: VPSocketChannel.stockInfo.name,
          buildWhen: (preData, data) {
            return (preData?.priceChange ?? stock.priceChange) != data?.priceChange;
          },
          builder: (_, __, data, child) {
            final change = data?.priceChange ?? stock.priceChange ?? 0;

            final changeValue =
                FormatUtils.formatClosePrice(
                  change,
                  showSign: false,
                  convertToThousand: stock.stockType?.isFU != true,
                ) ??
                '-';

            return Text(
              changeValue,
              textAlign: TextAlign.end,
              style: textStyle.copyColor(
                data.color ?? stock.color ?? vpColor.textTertiary,
              ),
            );
          },
        ),
        VPSocketInvestmentBuilder<VPStockInfoData>(
          symbol: stock.symbol,
          channel: VPSocketChannel.stockInfo.name,
          buildWhen: (preData, data) {
            final oldPercent =
                preData?.percentChange ?? stock.percentPriceChange;

            return oldPercent != data?.percentChange;
          },
          builder: (_, __, data, child) {
            final percent =
                data?.percentChange ?? stock.percentPriceChange ?? 0;

            final changePercent = FormatUtils.formatPercent(
              percent,
              showSign: false,
            );

            return Text(
              '($changePercent)',
              textAlign: TextAlign.end,
              style: textStyle.copyColor(
                data.color ?? stock.color ?? vpColor.textTertiary,
              ),
            );
          },
        ),
      ],
    );
  }
}
