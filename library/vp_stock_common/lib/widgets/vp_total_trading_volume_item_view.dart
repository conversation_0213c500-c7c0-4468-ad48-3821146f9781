import 'package:flutter/material.dart';
import 'package:vp_socket/vp_socket.dart';
import 'package:vp_stock_common/widgets/vp_socket_stock_info_builder.dart';

class VPTotalTradingVolumeItemView extends StatelessWidget {
  const VPTotalTradingVolumeItemView({
    required this.symbol,
    required this.builder,
    this.initTotalVolume,
    super.key,
  });

  final String symbol;

  final num? initTotalVolume;

  final Widget Function(num? volume) builder;

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: symbol,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen: (preData, data) {
        return (preData?.totalTradingVolume ?? initTotalVolume) != data?.totalTradingVolume;
      },
      builder: (context, _, data, child) {
        final totalTrading = data?.totalTradingVolume?.toDouble() ?? initTotalVolume;

        return builder.call(totalTrading);
      },
    );
  }
}
