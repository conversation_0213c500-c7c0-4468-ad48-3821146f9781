Tôi cần tạo tính năng "Sửa đặt lệnh chốt lời/cắt lỗ" với các yêu cầu sau:

## Yêu cầu chính:
1. **UI tương tự TakeProfitStopLossOrderView** - Sử dụng cùng layout và components
2. **Pre-fill dữ liệu** - <PERSON><PERSON>c field đã có giá trị sẵn từ lệnh hiện tại
3. **Validation giống nhau** - Áp dụng cùng rules validate như đặt lệnh mới
4. **Cập nhật thay vì tạo mới** - Call API sửa lệnh thay vì tạo lệnh mới

## Dữ liệu khởi tạo ban đầu:
```dart
class TakeProfitStopLossEditData {
  final String orderType;           // Loại lệnh (takeProfit/stopLoss)
  final double costPrice;           // Gi<PERSON> vốn bình quân
  final String symbol;              // Mã chứng khoán
  final double? stopLossRate;       // Tỷ lệ cắt lỗ/chốt lời (%)
  final double? stopLossPriceAmp;   // Biên giá cắt lỗ/chốt lời (VND)
  final double slipPagePrice;       // Biên trượt giá
  final int qty;                    // Khối lượng đặt lệnh
  final String fromDate;            // Ngày bắt đầu hiệu lực
  final String toDate;              // Ngày kết thúc hiệu lực
  final int total;                  // Khối lượng nắm giữ
}
```

## Tham khảo code hiện tại:

### 1. Pattern từ opendEditConditionPendingOrderBottomSheet:
- File: `features/vp_trading/lib/screen/order_container/conditional_order/edit_condition_order/condition_pending_order.dart`
- **Function signature**:
```dart
void opendEditConditionTakeProfitStopLossOrderBottomSheet({
  required BuildContext context,
  required ConditionOrderBookModel item,
  required VoidCallback onEditSuccess,
})
```

### 2. BlocProvider Setup Pattern:
```dart
MultiBlocProvider(
  providers: [
    BlocProvider(create: (context) => EditConditionOrderCubit()),
    BlocProvider(create: (context) => ValidateConditionOrderCubit()),
    BlocProvider(create: (context) => ValidateOrderCubit()),
    BlocProvider(create: (context) => PlaceOrderCubit(/* params */)),
    BlocProvider(create: (context) => StockInfoCubit()),
    BlocProvider(create: (context) => AvailableTradeCubit()),
  ],
  child: _EditConditionTakeProfitStopLossOrder(
    item: item,
    onEditSuccess: onEditSuccess,
  ),
)
```

### 3. UI Layout từ TakeProfitStopLossOrderView:
- **Components cần pre-fill**: 
  - `TriggerComponent` - Điều kiện kích hoạt (từ stopLossRate/stopLossPriceAmp)
  - `SlippageTextInputField` - Biên trượt giá (từ slipPagePrice)
  - `TakeProfitStopLossVolumeWidget` - Khối lượng (từ qty)
  - `GtcEffectiveTimeButton` - Thời gian hiệu lực (từ fromDate-toDate)
  - `ChoiceDilutionActionWidget` - Hành động pha loãng

### 4. MultiBlocListener Pattern:
```dart
MultiBlocListener(
  listeners: [
    BlocListener<AvailableTradeCubit, AvailableTradeState>(/* ... */),
    BlocListener<StockInfoCubit, StockInfoState>(/* ... */),
  ],
  child: /* UI Content */,
)
```

## Yêu cầu implementation:

1. **Tạo function**: `opendEditConditionTakeProfitStopLossOrderBottomSheet()`
2. **Tạo widget**: `_EditConditionTakeProfitStopLossOrder` 
3. **Pre-fill logic**:
   - Symbol field disabled với giá trị từ `item.symbol`
   - Trigger component với giá trị từ `stopLossRate` hoặc `stopLossPriceAmp`
   - Slippage field với `slipPagePrice`
   - Volume field với `qty`
   - Date range với `fromDate-toDate`
4. **Validation**: Sử dụng `ValidateConditionOrderCubit`
5. **API**: Call update qua `EditConditionOrderCubit`
6. **Success callback**: Pattern giống `EditAwaitingOrderWidget`

## Structure cần follow:
- Import structure giống `condition_pending_order.dart`
- BlocProvider setup pattern giống nhau
- MultiBlocListener cho validation updates
- Error handling và success callback pattern
- Bottom sheet với `VPPopup.bottomSheet()` và `copyWith(padding: EdgeInsets.zero)`

Hãy tạo code theo structure và pattern đã có, đảm bảo consistency với tính năng edit condition pending order.